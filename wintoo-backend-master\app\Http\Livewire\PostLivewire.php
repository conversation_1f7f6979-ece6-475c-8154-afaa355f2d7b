<?php

namespace App\Http\Livewire;

use App\Models\Product;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Post;
class PostLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];

        protected $listeners = ['Post-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'post';
        public function mount()
            {
                $searchable = Post::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Post::getColumnLang();
                $this->searchable =Post::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }
        public function render()
       {
           if (auth()->user()->type=="STORE"){
               $this->search_array["store_id"]=auth()->user()->store->id;
           }

           $data =Post::search($this->search_array);
           $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
           return view('dashboard/post/index',[ 'data'=>$data])->extends('dashboard_layout.main');

       }
        public function search(){
                $this->resetPage();
    }
        public function resetSearch(){
        $this->search_array=[];
     }
        public function edit($id){
             return redirect()->route('dashboard.post.edit',$id);
         }
        public function delete($id){
             $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Post-livewire:conformDelete',['id'=>$id]);
         }
        public function conformDelete($id){

             Post::find($id['id'])->delete();

             $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

         }
        public function setStatus($id){
            $object = Post::find($id);
            $object->status =!$object->status;
            $object->save();
        }
}

