<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Maincolor;
use App\Models\Size;
use App\Models\Variation;
use Illuminate\Http\Resources\Json\JsonResource;

class ColorVariationCartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

         //dump($this);





//        $Size_id=Variation::where('product_color_id',$this->id)->pluck('product_size_id')->toArray();
//        $size=Size::whereIn('id',$Size_id)->get();
//          if($this->id !=30) {
        return [
            'id' => $this->id,
            'color' => $this->color_ar,
            'human_color' => $this->human_name ? Maincolor::where('name', $this->human_name)->first()->human_name: '',
            'code' => $this->code,

        ];
//          }


    }
}
