<?php

namespace App\Services;
use App\Models\Order;
use App\Models\OrderTracker;
use App\Models\Transaction;
use Illuminate\Http\Request;


class OrderTransaction{


    public static function save($order,$type){
        $order =Order::find($order->id);

        Transaction::create([
            "order_id"=>$order->id,
            "store_id"=>$order->store_id,
            "customer_id"=>$order->customer_id,
            "type"=>$type,
        ]);
    }
}
?>
