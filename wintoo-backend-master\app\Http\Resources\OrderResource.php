<?php

namespace App\Http\Resources;

use App\Models\Coupon;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\CurrencyResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $counpon= Coupon::find($this->coupon_id);
        if($counpon){
            $object = [
                'id'=>$counpon->id,
                'coupon_value'=>$counpon->coupon_value,
                'coupon_type'=>$counpon->coupon_type,
                'couponAmount'=>numberFormat($this->coupon_value,2),
            ];

        }else{
            $object =null;
        }

        return [
            'id'=>$this->id,

            'address_id'=>$this->address_id,
            'address'=>new AddressResource($this->address),
            'status'=>$this->status,
            'status_name'=>$this->status_name,
            'status_name_translations'=>$this->status_name_translations,
            'delivery_cost'=>getRound($this->delivery_cost),
            'customer'=>$this->customer,
            'store'=>new StoreResource($this->store),
            'total'=>[
                'delivery_cost'=>getRound($this->delivery_cost),
                'sub_total'=>$this->sub_total,
                'coupon'=>$object,
                'total'=>$this->total,
                'currency'=>new CurrencyResource($this->currency)
            ],
            'items'=>OrderItemsResource::collection($this->order_items),
            'created_at'=>$this->created_at,
            'date'=>\Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'time'=>\Carbon\Carbon::parse($this->created_at)->isoFormat('h:mm a'),
          //  'items'=>CartItemResource::collection($carts),

        ];//parent::toArray($request);
    }
}
