<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DrawCandidate extends Model
{


use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded = [];


public function store(){
   return $this->belongsTo(Store::class , "store_id");
}


public function customer(){
   return $this->belongsTo(Customer::class , "customer_id");
}


public function QRRequest(){
   return $this->belongsTo(QRRequest::class , "qr_id");
}

}
