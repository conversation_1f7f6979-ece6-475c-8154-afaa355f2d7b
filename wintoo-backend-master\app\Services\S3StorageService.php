<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class S3StorageService
{
    protected $disk;

    public function __construct($disk = null)
    {
        // Use the  S3 disk defined in config/filesystems.php
        if ($disk) {
            $this->disk = $disk;
        } else {
            $this->disk = Storage::disk('s3');
        }

    }

    /**
     * Upload a file to the  S3 bucket.
     *
     * @param string $filePath The path to store the file in the bucket.
     * @param mixed $fileContents The file content (can be a stream or string).
     * @return bool
     */
    public function uploadFile(string $filePath, $fileContents)
    {
        return $this->disk->put($filePath, $fileContents);
    }

    /**
     * Generate a pre-signed URL for a file in the  S3 bucket.
     *
     * @param string $filePath The file path in the S3 bucket.
     * @param int $expirationMinutes How long the URL should be valid for.
     * @return string|null The pre-signed URL or null if the file does not exist.
     */
    public function generateTemporaryUrl(string $filePath, int $expirationMinutes)
    {
        if ($this->disk->exists($filePath)) {
            return $this->disk->temporaryUrl($filePath, now()->addMinutes($expirationMinutes));
        }

        return null;
    }

    /**
     * Delete a file from the  S3 bucket.
     *
     * @param string $filePath The file path to delete.
     * @return bool
     */
    public function deleteFile(string $filePath)
    {
        return $this->disk->delete($filePath);
    }

    /**
     * Check if a file exists in the  S3 bucket.
     *
     * @param string $filePath The file path to check.
     * @return bool
     */
    public function fileExists(string $filePath)
    {
        return $this->disk->exists($filePath);
    }
}
