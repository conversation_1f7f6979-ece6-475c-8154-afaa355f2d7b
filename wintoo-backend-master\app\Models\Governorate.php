<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Governorate extends Model
{
use \App\Traits\PropertyGetter;
        use HasFactory;
        use HasTranslations;
         protected $guarded = [];
         protected  $appends=['status_name',"country_name"];
        public $translatable = ['name'];

    public  static function  getColumnLang(){
                $columes=[
                    'name'=>[\Lang::get('governorate.name') ,1,true,false,[]],
                    'country_name'=>["الدولة",1,true,false,[]],
                    'status'=>[\Lang::get('governorate.status') ,1,true,false,[]],
                   'actions'=>['الخيارات',1,true,false,['edit','delete']],
                ];
                 return $columes;
          }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('governorate.name')],
 	 //	'status'=>[\Lang::get('governorate.status')],
            'country_id'=>[\Lang::get('lang.country'),['type'=>'select','name'=>'name','value'=>'id','model'=>'Country']],

 	 	 ]; return $columes;
  }


        public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])) {

             $query->where(function ($query) use ($data) {
                 /**/
                 foreach (getSupportedLocales() as $locale)
                     $query->orWhere("name->" . $locale, "LIKE", "%" . $data["name"] . "%");

             });
         }

           if(isset($data["country_id"])){
 	 	   $query->where("country_id","LIKE","%".$data["country_id"]."%");
           }

 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 return $query ;
 	 	 }
        public function getStatusNameAttribute(){
        if($this->status){
            return ' مفعل';
        }
        return 'غير مفعل';
     }
        public function cites()
{
    return $this->hasMany(City::class );
}

        public function country(){
             return $this->belongsTo(Country::class,"country_id");
        }

        public function getCountryNameAttribute(){
             return optional($this->country)->name;
        }



}
