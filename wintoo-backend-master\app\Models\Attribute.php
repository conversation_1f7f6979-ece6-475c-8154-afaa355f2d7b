<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Attribute extends Model
{
use \App\Traits\PropertyGetter;


use HasTranslations;

 protected $guarded = [];
 protected $appends = ["type_array"];
public $translatable = ['title'];

 public  static function  getColumnLang(){

 	 	$columes=[
 	 	'title'=>[\Lang::get('attribute.title') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	'type_name'=>[\Lang::get('attribute.type') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	'category_name'=>[\Lang::get('attribute.store_category_id') ,1,true,false, ['type'=>'integer','actions'=>null] ],
// 	 	'status'=>[\Lang::get('attribute.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
// 	 	'json_data'=>[\Lang::get('attribute.json_data') ,1,false,false, ['type'=>'longText','actions'=>null] ],
 	 	   'actions'=>['الخيارات',4,true,false,['type'=>'button','actions'=>['edit','delete']]],
 	 	];

 	 	 return $columes;

  }

 	 	public static function getSearchable(){
 	 	$columes=['title'=>[\Lang::get('attribute.title')],
 	 	//'store_category_id'=>[\Lang::get('attribute.store_category_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'ProductsRequestsCategory']],
// 	 	'json_data'=>[\Lang::get('attribute.json_data')],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["title"])){
 	 	   $query->where("title","LIKE","%".$data["title"]."%");}
 	 	 if(isset($data["type"])){
 	 	   $query->where("type","LIKE","%".$data["type"]."%");}
 	 	 if(isset($data["store_category_id"])){
               $id =$data["store_category_id"];
             $query->whereHas('categories', function($q) use($id){
                 $q->where('products_requests_categories.id',  $id);
             });
           }
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 if(isset($data["json_data"])){
 	 	   $query->where("json_data","LIKE","%".$data["json_data"]."%");}
 	 	 return $query ;

    }



       public function getTypeArrayAttribute(){
        $types = [
          //  ["name"=>"تاريخ","id"=>"date" ],
         //   ["name"=>"وصف","id"=>"textarea" ],
            ["name"=>"قائمة","id"=>"dropdown" ],
            ["name"=>"قائمة مع عدة خيارات","id"=>"multiselect"],
            ["name"=>"نص","id"=>"text" ],
            ["name"=>"رقم","id"=>"number"],
            ["name"=>"فترة من - الى","id"=>"range"],
       //     ["name"=>"تشيك بوكس","id"=>"checkbox"],
           // ["name"=>"","type"=>"radio"],
        ];

        return $types;

       }


       public function categories(){
            return $this->belongsToMany(ProductsRequestsCategory::class,"attribute_category","attribute_id","category_id");
       }

       public function getCategoryNameAttribute(){
            $names = "";
            foreach ($this->categories as $category){
                $names .= $category->name." , ";
            }
            return $names;
       }
       public function getTypeNameAttribute(){

            $array_index = array_search($this->type,array_column($this->type_array,"id"));

            return $this->type_array[$array_index]['name'];
       }


       public function attribute_options(){
             return $this->hasMany(AttributeOption::class,"attribute_id");
       }

}
