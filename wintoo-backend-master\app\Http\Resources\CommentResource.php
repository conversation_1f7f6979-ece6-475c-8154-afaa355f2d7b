<?php

namespace App\Http\Resources;

use App\Models\Comment;
use App\Models\Like;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $comments_count = Comment::where("post_id",$this->post_id)
            ->count();


        return [

            "id"=>$this->id,
            "customer"=>$this->parent_id?null:new CustomerResource($this->customer),
            "store"=>$this->parent_id?new StoreResource($this->post->store):null,
            "comment_text"=>$this->comment_text,
            "replies"=> \App\Http\Resources\CommentResource::collection($this->replies) ,
            "created_at"=>Carbon::parse($this->created_at)->diffForHumans(),
            "comments_count"=>$comments_count,


        ];
    }
}
