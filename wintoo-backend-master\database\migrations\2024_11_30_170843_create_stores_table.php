<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('stores', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('name')->nullable()->comment('name');
			$table->text('logo')->nullable()->comment('logo');
			$table->text('bio')->nullable()->comment('bio');
			$table->string('mobile', 191)->nullable()->comment('mobile');
			$table->string('full_mobile', 191)->nullable();
			$table->string('phone', 191)->nullable()->comment('phone');
			$table->string('email', 191)->nullable()->comment('email');
			$table->string('address', 191)->nullable()->comment('address');
			$table->integer('category_id')->nullable()->comment('store_category_id');
			$table->string('open_time_from', 191)->nullable()->comment('open_time_from');
			$table->string('open_time_to', 191)->nullable()->comment('open_time_to');
			$table->string('whatsapp', 191)->nullable()->comment('whatsapp');
			$table->text('password')->nullable()->comment('password');
			$table->text('open_days')->nullable()->comment('open_days');
			$table->boolean('status')->default(0)->comment('status');
			$table->timestamps();
			$table->integer('governorate_id')->nullable();
			$table->integer('city_id')->nullable();
			$table->integer('region_id')->nullable();
			$table->string('device_type', 191)->nullable();
			$table->text('fcm_token')->nullable();
			$table->text('token')->nullable();
			$table->string('sms_code', 191)->nullable();
			$table->boolean('sms_verify')->nullable()->default(1);
			$table->text('facebook')->nullable();
			$table->text('instagram')->nullable();
			$table->text('open_type')->nullable();
			$table->boolean('is_black_allow')->default(0);
			$table->boolean('is_chat_register')->default(0);
			$table->string('chat_auth', 200)->nullable();
			$table->integer('country_id')->nullable();
			$table->string('type', 191)->nullable();
			$table->string('code', 191)->nullable();
			$table->string('phone_code', 191)->nullable();
			$table->integer('currency_id')->nullable()->default(1);
			$table->string('whatsapp_phone_code', 191)->nullable();
			$table->string('lang_code', 191)->nullable();
			$table->boolean('is_Main')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('stores');
	}

}
