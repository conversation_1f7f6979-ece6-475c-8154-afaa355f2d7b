<?php return array (
  'storeCategory' => 
  array (
    'name' => 'storeCategory.model_name',
    'icon' => NULL,
    'url' => 'dashboard.storeCategory',
    'order' => 1,
    'sub' => 
    array (
    ),
  ),
  'store' => 
  array (
    'name' => 'store.model_name',
    'icon' => NULL,
    'url' => 'dashboard.store',
    'order' => 2,
    'sub' => 
    array (
    ),
  ),
  'post' => 
  array (
    'name' => 'post.model_name',
    'icon' => '',
    'url' => 'dashboard.post',
    'order' => 2,
    'sub' => 
    array (
    ),
  ),
  'systemDraw' => 
  array (
    'name' => 'systemDraw.model_name',
    'icon' => NULL,
    'url' => 'dashboard.systemDraw',
    'order' => 3,
    'sub' => 
    array (
    ),
  ),
  'sponser' => 
  array (
    'name' => 'sponser.model_name',
    'icon' => NULL,
    'url' => 'dashboard.sponser',
    'order' => 4,
    'sub' => 
    array (
    ),
  ),
  'sponsor' => 
  array (
    'name' => 'sponsor.model_name',
    'icon' => NULL,
    'url' => 'dashboard.sponsor',
    'order' => 5,
    'sub' => 
    array (
    ),
  ),
  'offer' => 
  array (
    'name' => 'offer.model_name',
    'icon' => NULL,
    'url' => 'dashboard.offer',
    'order' => 6,
    'sub' => 
    array (
    ),
  ),
  'QRRequest' => 
  array (
    'name' => 'QRRequest.model_name',
    'icon' => NULL,
    'url' => 'dashboard.QRRequest',
    'order' => 7,
    'sub' => 
    array (
    ),
  ),
  'ProductRequest' => 
  array (
    'name' => 'ProductRequest.model_name',
    'icon' => NULL,
    'url' => 'dashboard.ProductRequest',
    'order' => 10,
    'sub' => 
    array (
    ),
  ),
  'attribute' => 
  array (
    'name' => 'attribute.model_name',
    'icon' => NULL,
    'url' => 'dashboard.attribute',
    'order' => 9,
    'sub' => 
    array (
    ),
  ),
);