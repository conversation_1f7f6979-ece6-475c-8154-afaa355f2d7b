/***
TODO Page
***/
.todo-sidebar {
  float: right;
  width: 230px;
  margin-left: 20px; }

.todo-content {
  overflow: hidden; }

.todo-tasklist-item-border-green {
  border-right: #3faba4 2px solid; }

.todo-tasklist-item-border-blue {
  border-right: #4c87b9 2px solid; }

.todo-tasklist-item-border-purple {
  border-right: #8877a9 2px solid; }

.todo-tasklist-item-border-red {
  border-right: #d05454 2px solid; }

.todo-tasklist-item-border-yellow {
  border-right: #d4ad38 2px solid; }

.padding-top-10px {
  padding-top: 15px; }

.todo-userpic {
  -webkit-border-radius: 50% !important;
  -moz-border-radius: 50% !important;
  border-radius: 50% !important;
  border: 1px #cedae1 solid; }

.todo-text-color {
  color: #45535b; }
.todo-projects-config {
  padding: 6px 9px 3px 9px !important; }
  .todo-projects-config > i {
    font-size: 14px !important; }

.todo-tasklist {
  padding: 0; }

.todo-project-list ul li a {
  font-size: 14px !important;
  padding: 8px 10px; }

.todo-project-list .nav li a .badge {
  float: left;
  margin-top: 1px !important; }

.todo-project-list .nav > li.active > a {
  color: #3f444a;
  background-color: #f1f4f7 !important; }

/* END PROJECT LIST */
.todo-task-history {
  padding: 0;
  margin: 0; }
  .todo-task-history > li {
    padding: 5px 0; }
    .todo-task-history > li > .todo-task-history-desc {
      overflow: hidden; }
    .todo-task-history > li > .todo-task-history-date {
      font-size: 12px;
      float: left;
      width: 150px;
      margin-right: 10px;
      text-align: left;
      color: #999; }
.todo-tasklist-item {
  background: #f6fbfc;
  padding: 10px;
  margin-bottom: 10px;
  margin-bottom: 15px;
  overflow: hidden; }
  .todo-tasklist-item:last-child {
    margin-bottom: 0; }

div .todo-tasklist-item:hover {
  cursor: pointer;
  background-color: #edf7f9; }

.todo-tasklist-item img {
  margin: 0 0 10px 10px; }

.todo-tasklist-item-title {
  font-size: 15px;
  color: #2b4a5c;
  font-weight: 600;
  padding-top: 3px;
  padding-bottom: 13px; }

.todo-tasklist-item-text {
  font-size: 13px;
  color: #577688;
  padding-bottom: 5px; }

.todo-tasklist-item ul {
  margin: 5px 0px 0 0; }

.todo-tasklist-item li {
  color: #577688;
  font-size: 13px;
  margin-left: 10px;
  margin-bottom: 5px;
  padding: 0 !important; }

.todo-tasklist-item li i {
  color: #b3bfcb;
  font-size: 15px; }

.todo-tasklist-controls {
  margin-top: 5px; }

.todo-tasklist-date {
  color: #637b89 !important;
  margin-left: 12px; }

.todo-tasklist-date i {
  color: #abbfca !important;
  margin-left: 5px; }

/* END TASKS LIST */
.todo-taskbody-tasktitle {
  font-size: 18px;
  color: #778d96; }

.todo-taskbody-taskdesc {
  font-size: 14px;
  color: #778d96; }

.todo-username {
  font-size: 16px;
  color: #2b4a5c;
  font-weight: 600;
  padding: 15px 15px 0 0; }

.todo-comment-head {
  padding-top: 3px; }

.todo-comment {
  position: relative; }

.todo-comment:hover > .todo-comment-btn {
  display: block; }

.todo-comment-btn {
  display: none;
  position: absolute;
  top: 1px;
  left: 0px;
  font-size: 12px;
  color: #566e7c;
  border-color: #a2aeb5; }

.todo-comment-btn:hover {
  color: #fff;
  background-color: #a1b6c2;
  border-color: #a1b6c2; }

.todo-comment-username {
  font-size: 14px;
  color: #2b4a5c;
  font-weight: 600; }

.todo-comment-date {
  font-size: 12px;
  color: #2b4a5c;
  font-weight: 400; }

.todo-username-btn {
  margin: 14px 15px 0 0;
  color: #566e7c;
  border-color: #a2aeb5; }

.todo-username-btn:hover {
  color: #fff;
  background-color: #a1b6c2;
  border-color: #a1b6c2; }

.form .form-actions.todo-form-actions {
  padding-top: 10px;
  border: 0;
  margin: 0 0 20px 0; }

/* END TASK BODY */
/* RESPONSIVE MODE */
@media (max-width: 991px) {
  .todo-sidebar {
    float: none;
    width: 100%;
    margin: 0; }
    .todo-sidebar > .portlet {
      margin-bottom: 20px; }
      .todo-sidebar > .portlet .portlet-title .tools {
        margin-right: 5px; }
  .todo-content {
    overflow: visible; } }

.todo-tasklist-devider {
  display: none; }

@media (max-width: 767px) {
  .todo-tasklist-devider {
    display: block;
    height: 20px;
    margin: 20px -10px;
    background: #F1F3FA; }
  .todo-task-history > li {
    padding: 9px 0; }
    .todo-task-history > li > .todo-task-history-date {
      width: 100px;
      font-size: 11px; } }
