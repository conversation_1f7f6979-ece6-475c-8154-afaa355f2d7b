<?php

namespace App\Models;

use App\Http\Resources\AttributeResource;
use App\Services\SendNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class JobRequest extends Model
{
use \App\Traits\PropertyGetter;


         protected $guarded = [];
         protected $appends=['customer_name','gender_name','job_name',"store_category_name","type_name","product_title",'full_address','target_full_address','user_image_url','work_image_url'];
        protected $table = "jobs_requests" ;

    public  static function  getColumnLang(){
    $columes=[
        'job_name'=>["نوع العمل" ,1,true,false, ['type'=>'integer','actions'=>null] ],

        'customer_name'=>["المستخدم",1,true,false, ['type'=>'integer','actions'=>null] ],

        'actions'=>['الخيارات',3,true,false,['type'=>'button','actions'=>['view']]],
    ];
     return $columes;
}
         public static function getSearchable(){
                $columes=[
                  /*  'title'=>["العنوان"],
                    'store_category_id'=>["التصنيفات",['type'=>'select','name'=>'name','value'=>'id','model'=>'StoreCategory']],
                    'customer_id'=>["المستخدمين",['type'=>'select','name'=>'username','value'=>'id','model'=>'Customer']],*/

                 ];
                return $columes;
         }


 public function scopeSearch($query, $data) {
     if(isset($data["title"])){
       $query->where("title","LIKE","%".$data["title"]."%");}
     if(isset($data["job_type_id"])){
       $query->where("job_type_id","LIKE","%".$data["job_type_id"]."%");}
     if(isset($data["customer_id"])){
       $query->where("customer_id","LIKE","%".$data["customer_id"]."%");}

     return $query ;
     }

         public function customer(){
            return $this->belongsTo(Customer::class,"customer_id");
         }

         public function store(){
            return $this->belongsTo(Store::class,"store_id");
         }

         public function job_type(){
            return $this->belongsTo(JobType::class,"job_type_id");
         }

         public function getCustomerNameAttribute(){

            return optional($this->customer)->username;
         }

         public function getTypeNameAttribute(){

                if($this->type =="request"){
                    return Lang::get("lang.job_request");
                }

             return Lang::get("lang.job_offer");

         }
         public function getJobTypeNameAttribute(){

            return optional($this->job_type)->name;
         }




    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');
    }


    public function governorate(){
        return $this->belongsTo(Governorate::class);

    }
    public function city(){
        return $this->belongsTo(City::class);

    }
    public function region(){
        return $this->belongsTo(Region::class);

    }
    public function country(){
        return $this->belongsTo(Country::class);

    }
    public function target_governorate(){
        return $this->belongsTo(Governorate::class,'target_country_id');

    }
    public function target_city(){
        return $this->belongsTo(City::class,'target_city_id');

    }
    public function target_region(){
        return $this->belongsTo(Region::class,'target_region_id');

    }
    public function target_country(){
        return $this->belongsTo(Country::class,'target_governorate_id');

    }






    public function getFullAddressAttribute(){
      //  return optional($this->country)->name .' - '.optional($this->governorate)->name .' - '.optional($this->city)->name .' - '.optional($this->regoin)->name;

        return implode(" - " , array_filter([optional($this->governorate)->name ,optional($this->city)->name ,optional($this->regoin)->name],function ($item){
            return !is_null($item) ;
        }));


    }

    public function getTargetFullAddressAttribute(){
      //  return optional($this->country)->name .' - '.optional($this->governorate)->name .' - '.optional($this->city)->name .' - '.optional($this->regoin)->name;

        return implode(" - " , array_filter([optional($this->target_governorate)->name ,optional($this->target_city)->name ,optional($this->target_regoin)->name],function ($item){
            return !is_null($item) ;
        }));


    }


    public function getMatchingRequestsAndNotify($notify = false){
        $type  = $this->type ;
        $exp_years  = $this->exp_years ;
        $exp_years_from  = $this->offer_exp_years_from ;
        $exp_years_to  = $this->offer_exp_years_to ;

        $job_type_id  = $this->job_type_id ;
        $age  = $this->age ;
        $offer_age_from  = $this->offer_age_from ;
        $offer_age_to  = $this->offer_age_to ;

        $country_id  = $this->country_id ;
        $city_id  = $this->city_id ;
        $region_id  = $this->region_id ;
        $governorate_id  = $this->governorate_id ;

        $target_country_id  = $this->target_country_id ;
        $target_city_id  = $this->target_city_id ;
        $target_region_id  = $this->target_region_id ;
        $target_governorate_id  = $this->target_governorate_id ;
        $gender  = $this->gender ;

        $get_same_product = JobRequest::where("id","!=",$this->id)
            ->where("job_type_id",$job_type_id)
            ->where(function ($query1){

                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser){
                    $query1->where("customer_id","!=",$currentUser->id);
                }

                $query1->where(function ($q) use ($currentUser){

                    $q->where(function ($q) use($currentUser){

                        $q->whereIn("customer_id" ,function ($q){
                            $q->select('id')
                                ->from("customers")
                                ->whereNull("deleted_at")
                            ;
                        })->orWhereNull("customer_id");;


                    });

                });



                if ($currentStore) {
                    $query1->where("store_id", "!=", $currentStore->id);
                }

                $query1->where(function ($q) use ($currentStore){

                    $q->where(function ($q) use($currentStore){

                        $q->where("store_id","!=",$currentStore->id)
                            ->whereIn("store_id" ,function ($q){
                                $q->select('id')
                                    ->from("stores")
                                ;

                                ;
                            })->orWhereNull("store_id");


                    });

                });




            })
            ->where("type",$this->type =="offer"?"request":"offer")
            ->where( function ($query) use($type,$exp_years,$exp_years_from,$exp_years_to,$age,$offer_age_from,$offer_age_to
            ,$country_id
            ,$city_id
            ,$region_id
            ,$governorate_id
            ,$target_country_id
            ,$target_city_id
            ,$target_region_id
            ,$target_governorate_id
            ,$gender
            ){

                if ($type =="request") {

                    $query->where("offer_exp_years_from","<=",(double)$exp_years);
                    $query->where("offer_exp_years_to",">=",(double)$exp_years);

                    $query->where("offer_age_from","<=",(double)$age);
                    $query->where("offer_age_to",">=",(double)$age);

                    if ($target_country_id)
                    $query->where("country_id",$target_country_id);

                    if ($target_city_id)
                    $query->where("city_id",$target_city_id);

                    if ($target_region_id)
                    $query->where("region_id",$target_region_id);

                    if ($target_governorate_id)
                    $query->where("governorate_id",$target_governorate_id);

                    $query->where(function ($query)use ($gender){

                     $query->where("gender","=",$gender)->orWhere("gender","both");

                    });

                }else if ($type =="offer"){

                    $query->where("exp_years",">=",(double)$exp_years_from);
                    $query->where("exp_years","<=",(double)$exp_years_to);

                    $query->where("age",">=",(double)$offer_age_from);
                    $query->where("age","<=",(double)$offer_age_to);


                    if ($gender!= "both")
                    $query->where("gender","=",$gender);

                    if ($country_id)
                    $query->where("target_country_id",$country_id);

                    if ($city_id)
                    $query->where("target_city_id",$city_id);

                    if ($region_id)
                    $query->where("target_region_id",$region_id);

                    if ($governorate_id)
                    $query->where("target_governorate_id",$governorate_id);


                }

            })
            ->orderBy("id","desc")->get();




        if ($notify){

            if (count($get_same_product)){

                if ($this->store_id){
                    SendNotification::sendNotificationOnJobRequestFirstMessageStore($this->store_id ,$this->id );


                } else if ($this->customer_id){
                    SendNotification::sendNotificationOnJobRequestFirstMessage($this->customer_id ,$this->id );
                }


            }


            foreach ($get_same_product as $item){

                if ($item->store_id){

                    SendNotification::sendNotificationOnJobRequestFirstMessageStore($item->store_id ,$item->id );

                } else if ($item->customer_id){

                    SendNotification::sendNotificationOnJobRequestFirstMessage($item->customer_id ,$item->id );
                }

            }
        }

        return $get_same_product;

    }

    public function getUserImageUrlAttribute(){
       // return $this->user_image?asset('storage/customer/'.$this->user_image):null;
        return $this->user_image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),"customer",$this->user_image]):null;

    }

    public function getWorkImageUrlAttribute(){
       // return $this->work_image?asset('storage/customer/'.$this->work_image):null;
        return $this->work_image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),"customer",$this->work_image]):null;
    }


    public function getGenderNameAttribute(){

        switch ($this->gender){

            case "male":

                return  Lang::get("lang.male") ;

            case "female":

                return Lang::get("lang.female") ;

            case "both":

                return  Lang::get("lang.both");

        }
        return $this->work_image?asset('storage/customer/'.$this->work_image):null;


    }

}
