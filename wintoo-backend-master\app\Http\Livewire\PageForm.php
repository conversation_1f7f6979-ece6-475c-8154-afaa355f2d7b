<?php

namespace App\Http\Livewire;

use App\Models\Page;
use Livewire\Component;
use App\Traits\Alert;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
class PageForm extends Component
{
    use Alert,PublicFunction,WithFileUploads;
    public $title = "";
    public $image=null;
    public $selected=[];
    public $page=[
        'name'=>'',
        'description'=>'',
        'link'=>'',
        'image'=>'https://www.gravatar.com/avatar/d41d8cd98f00b204e9800998ecf8427e',
    ];
    public function mount($id =null)
    {
        $this->title = \Lang::get('lang.add_page')  ;
        $page=Page::find($id);
        $this->page   = $id?Page::find($id)->toArray():$this->page;
        if($id){
            $this->page['image']=$page->pageUrl();

            $this->title = \Lang::get('lang.edit_page')  ;

        }

    }

    public function render()
    {
        if(in_array('page_create'||'page_edit',$this->actions_permission()) ) {
            return view('dashboard.pages.form')->extends('dashboard_layout.main');
        }else{
            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }

    }

    public function save(){
        $this->validate(
            [
                'page.name' => 'required',
                'page.description' => 'required',
            ]);
        $filename = $this->image?$this->image->store('/','pages'):null;
        $this->page['image']=$filename;
        $page = Page::create($this->page);

        $page->link = '/page/'.$page->id.'/'.str_replace(' ','-',$this->page['name']);
        $page->save();
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
        return redirect()->route('dashboard.page');
    }

    public function update($id){

        if( $this->page['description'] =="<p><br></p>"){
            $this->page['description']= null;
        }
      //  dd($this->page);
        $page=Page::find($id);
        $this->validate([
            'page.name' => 'required',
            'page.description' => 'required',
        ]);
        $filename = $this->image?$this->image->store('/','pages'):$page->image;

        $this->page['image']=$filename;
        $this->page['link']='/page/'.$this->page['id'].'/'.str_replace(' ','-',$this->page['name']);
        $page->update($this->page);
        $this->page['image']=$page->pageUrl();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success',route('dashboard.page'));

    }

}
