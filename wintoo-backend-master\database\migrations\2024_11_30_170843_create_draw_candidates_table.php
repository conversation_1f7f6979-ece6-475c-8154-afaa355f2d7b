<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDrawCandidatesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('draw_candidates', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('order_id')->nullable();
			$table->integer('qr_id')->nullable();
			$table->integer('customer_id')->nullable();
			$table->integer('store_id')->nullable();
			$table->boolean('system_draw')->nullable()->default(0);
			$table->boolean('store_draw')->nullable()->default(0);
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('draw_candidates');
	}

}
