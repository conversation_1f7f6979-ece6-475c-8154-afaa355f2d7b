<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrderTrackersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('order_trackers', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('order_id')->nullable();
			$table->string('status', 191)->nullable();
			$table->timestamps();
			$table->string('delivery_status', 191)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('order_trackers');
	}

}
