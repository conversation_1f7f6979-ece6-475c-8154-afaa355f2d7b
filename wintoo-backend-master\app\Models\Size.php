<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Size extends Model
{
use \App\Traits\PropertyGetter;

 protected $guarded = [];
 public  static function  getColumnLang(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('size.name') ,1,true,false,[]],
 	 	'size_ar'=>[\Lang::get('size.size_ar') ,1,true,false,[]],
 	 	   'actions'=>['الخيارات',1,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('size.name')],
 	 	'size_ar'=>[\Lang::get('size.size_ar')],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["size_ar"])){
 	 	   $query->where("size_ar","LIKE","%".$data["size_ar"]."%");}
 	 	 return $query ;
 	 	 }

}
