<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Jetstream\HasProfilePhoto;
use Illuminate\Database\Eloquent\SoftDeletes;
use \App\Traits\PropertyGetter;

class Store extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable ,PropertyGetter;
     protected $guarded = [];
     protected $appends = [
                    "logo_url",
                    "category_name",
                    "governorate_name",
                    "city_name",
                    "region_name",
                    "region_name",
                    "qr_image_url",
                    "type_name",
                    "image_url",
                     'full_mobile' ,
         'full_address'

    ];

     public  static function  getColumnLang(){
            $columes=[
                'logo_url'=>[\Lang::get('store.logo') ,1,true,false, ['type'=>'image','actions'=>null] ],
              'name'=>[\Lang::get('store.name') ,1,true,false, ['type'=>'text','actions'=>null] ],

            'mobile'=>[\Lang::get('store.mobile') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'phone'=>[\Lang::get('store.phone') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'email'=>[\Lang::get('store.email') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'category_name'=>[\Lang::get('store.category_id') ,1,true,false, ['type'=>'integer','actions'=>null] ],
            'address'=>[\Lang::get('store.address') ,1,true,false, ['type'=>'string','actions'=>null] ],

            'governorate_name'=>[\Lang::get('store.governorate_id') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'city_name'=>[\Lang::get('store.city_id') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'region_name'=>[\Lang::get('store.region_id') ,1,true,false, ['type'=>'string','actions'=>null] ],

            'open_time_from'=>[\Lang::get('store.open_time_from') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'open_time_to'=>[\Lang::get('store.open_time_to') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'created_at'=>[\Lang::get('store.created_at') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'status'=>[\Lang::get('store.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
            'actions'=>['الخيارات',8,true,false,
                    ['type'=>'button',
                    'actions'=> [
                        "access",
                        "download",
                        'edit',
                        'delete']
                    ]
                ],
            ];
             return $columes;
      }


    public function scopeWhereMobile($query, $data)
    {

        $mobileNumberWithoutLeadingZero = preg_replace('/^0/', '', $data["mobile"], 1);
        $query->where("mobile",$mobileNumberWithoutLeadingZero);
        $query->where("phone_code",$data["phone_code"] );

    }

    public function getFullMobileAttribute(){

       return $this->getFullMobile();

    }

    public function getFullMobile(){

        return getFullMobile($this->phone_code,$this->mobile);

    }


    protected static function boot()
    {
        parent::boot();


        static::creating(function ($model)
        {
            $model->attributes['full_mobile'] = getFullMobile($model->phone_code,$model->mobile);
        });


        static::updating(function ($model)
        {
            $model->attributes['full_mobile'] = getFullMobile($model->phone_code,$model->mobile);
        });


    }


    public function scopeIsWholeSale($query)
    {
       /* return $query->whereHas("category",function ($query){
            $query->where("type","wholesale");
        });*/

        return $query->where("type","wholesaler");

    }



    public function scopeIsNotWholeSale($query)
    {
//        return $query->whereHas("category",function ($query){
//
//            $query->where("type","!=","wholesale")->orWhereNull("type");
//        });

        return $query->where("type","!=","wholesaler");



    }
    public function scopeIsActive($query)
    {
        return $query->where('status',true)->where("sms_verify",true);
    }
     public static function getSearchable(){
            $columes=[
                'name'=>[\Lang::get('store.name')],
            'mobile'=>[\Lang::get('store.mobile')],
            'category_id'=>[\Lang::get('store.store_category_id'),
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'StoreCategory']],
            'created_at_from'=>["تاريخ الانشاء من ",['type'=>'date','name'=>'name',]],
            'created_at_to'=>["تاريخ الانشاء الي ",['type'=>'date','name'=>'name',]],
            'governorate_id'=>[\Lang::get('store.governorate_id'),
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'Governorate']],
            'city_id'=>[\Lang::get('store.city_id'),
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'City']],
            'region_id'=>[\Lang::get('store.region_id'),
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'Region']],

              "is_black_allow" =>["مفعل العروض على البلاك فرايدي",
                  ['type'=>'select','name'=>'name','value'=>'value',
                      'model'=>[['name'=>'فعال','value'=>1],['name'=>'غير فعال','value'=>0]]
                      ,]
              ]
             ]; return $columes;
      }
     public function scopeSearch($query, $data) {
             if(isset($data["name"])){
               $query->where("name","LIKE","%".$data["name"]."%");}
             if(isset($data["bio"])){
               $query->where("bio","LIKE","%".$data["bio"]."%");}
             if(isset($data["mobile"])){
               $query->where("mobile","LIKE","%".$data["mobile"]."%");}
             if(isset($data["phone"])){
               $query->where("phone","LIKE","%".$data["phone"]."%");}
             if(isset($data["email"])){
               $query->where("email","LIKE","%".$data["email"]."%");}
             if(isset($data["address"])){
               $query->where("address","LIKE","%".$data["address"]."%");}
             if(isset($data["category_id"])){
               $query->where("category_id","LIKE","%".$data["category_id"]."%");}
             if(isset($data["open_time_from"])){
               $query->where("open_time_from","LIKE","%".$data["open_time_from"]."%");}
             if(isset($data["open_time_to"])){
               $query->where("open_time_to","LIKE","%".$data["open_time_to"]."%");}
             if(isset($data["governorate_id"])){
               $query->where("governorate_id",$data["governorate_id"]);
             }
             if(isset($data["city_id"])){
                 $query->where("city_id",$data["city_id"]);
             }
             if(isset($data["region_id"])){
                 $query->where("region_id",$data["region_id"]);
             }
             if(isset($data["is_black_allow"])){
                 $query->where("is_black_allow",$data["is_black_allow"]);
             }

            if(isset($data['created_at_to']) && isset($data['created_at_from'])){
             $from = $data['created_at_from'];
             $to = $data['created_at_to'];

             $query
                 ->whereDate('created_at', '>=', $from)
                 ->whereDate('created_at', '<=', $to);

         }
             return $query ;
             }
     public function category(){
         return $this->belongsTo(StoreCategory::class,"category_id");
     }
    public function country(){
        return $this->belongsTo(Country::class,"country_id");
    }
     public function governorate(){
        return $this->belongsTo(Governorate::class,"governorate_id");
    }
     public function city(){
        return $this->belongsTo(City::class,"city_id");
    }
     public function region(){
        return $this->belongsTo(Region::class,"region_id");
    }
     public function user(){
        return $this->hasOne(User::class,"store_id");
    }
    public function follower(){
        return $this->belongsToMany(Customer::class,"followers","store_id","customer_id")->distinct();
    }



     public function getGovernorateNameAttribute(){
     return optional($this->governorate)->name;
    }
     public function getCityNameAttribute(){
        return optional($this->city)->name;
    }
     public function getRegionNameAttribute(){
        return optional($this->region)->name;
    }
     public function getLogoUrlAttribute(){

      //   return asset("storage/".$this->logo);

         return $this->logo?route("resize.storage",[config("app.def_thumb_width"),config("app.def_thumb_height"),$this->logo]):asset('images/shop.png?v=4');

        // return $this->logo?asset('storage/'.$this->logo):asset('images/shop.png?v=4');


     }

     public function getLogoLargeUrlAttribute(){


         return $this->logo?route("resize.storage",[config("app.def_large_width"),config("app.def_large_height"),$this->logo]):asset('images/shop.png?v=4');



     }
     public function getImageUrlAttribute(){
         return asset("storage/".$this->logo);
     }
     public function getCategoryNameAttribute(){
         return optional($this->category)->name;
     }


     public function getOpenDaysArrayAttribute(){
         $days = json_decode($this->open_days);
     }
     public function getQrImageUrlAttribute(){

         if($this->id){
             return route("qr_store_image",$this->id);
         }

         return "";

     }

     public function getTypeNameAttribute(){

         switch ($this->type){
         case "wholesaler" :
         return Lang::get("store.wholesaler") ;
          case "retailer" :
         return Lang::get("store.retailer") ;
         default :
             return "" ;

         }

     }


    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');
    }

    public function getFullAddressAttribute(){
        return optional($this->country)->name .' | '.optional($this->governorate)->name .' | '.optional($this->city)->name .' | '.optional($this->regoin)->name.' | '.$this->address;
    }
    
     public function wallet()
    {
        return $this->hasOne(Wallet::class, 'store_id');
    }

}
