<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();
        $this->call(CountriesTableSeeder::class);
        $this->call(GovernorateTableDataSeeder::class);
        $this->call(CitiesTableSeeder::class);
        $this->call(RegionsTableSeeder::class);
        $this->call(CategoryTableDataSeeder::class);
        $this->call(StoreCategoryTableDataSeeder::class);
        $this->call(SubCategoryTableDataSeeder::class);
        $this->call(SettingsTableSeeder::class);
        $this->call(SizesTableSeeder::class);
        $this->call(StoresTableSeeder::class);
        $this->call(UsersTableSeeder::class);
        $this->call(PostsTableSeeder::class);
        $this->call(JobTypesSeeder::class);
        $this->call(ActionRolesSeeder::class);
        $this->call(AttributesSeeder::class);
        $this->call(AttributeCategorySeeder::class);
        $this->call(ColorSeeder::class);
        $this->call(FAQsSeeder::class);
        $this->call(MainColorsSeeder::class);
        $this->call(CurrenciesSeeder::class);
    }
}
