<?php

namespace App\Models;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */


    public static $searchable=[
        'fullname'=>[],
        'id_card_number'=>[],
    ];

    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'role_id',
        'type',
        'store_id',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'profile_photo_url',
    ];

    public function avatarUrl(){
        return $this->avatar?asset('storage/avatars/'.$this->avatar):asset('images/Picture1.png');
    }

    public function Role()
    {
        return $this->belongsTo(Role::class,   'role_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
    public  static function  getColumnLang(){

        $columes=[
            'avatar'=>[\Lang::get('user.Profile_photo'),1,true,false,[]],
            'name'=>[\Lang::get('user.name'),2,true,true,[]],
            'role_id'=>[\Lang::get('user.Permission'),3,true,true,[]],
            'actions'=>[\Lang::get('user.Actions'),4,true,false,['edit','show','delete']],
        ];

        return $columes;


    }
}
