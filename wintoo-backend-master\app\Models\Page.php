<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
use Spatie\Translatable\HasTranslations;
class Page extends Model
{
use \App\Traits\PropertyGetter;

   // protected $guarded = [];
    protected $fillable = ['name','description','link','image','type'];

    use HasFactory;
    use Loggable ,HasTranslations;

    public static $searchable=[
        'name'=>[],
    ];


    public function pageUrl(){
        return $this->image?asset('storage/pages/'.$this->image):'https://www.gravatar.com/avatar/'.md5(strtolower(trim($this->image)));
    }

    public $appends=['image_url'];

    public function getImageUrlAttribute(){
        return asset('storage/pages/' . $this->image);
    }
    public  static function  getColumnLang(){
        $columes=[
            'name'=>[\Lang::get('page.name') ,1,true,false,[]],
            'description'=>[\Lang::get('page.description') ,1,false,false,[]],
            'link'=>[\Lang::get('page.link') ,1,true,false,[]],
            'image'=>[\Lang::get('page.image') ,1,false,false,[]],
            'actions'=>['الخيارات',3,true,false,['edit','delete']],
        ];
        return $columes;
    }
    public static function getSearchable(){
        $columes=['name'=>[\Lang::get('page.name')],
            'link'=>[\Lang::get('page.link')],

        ]; return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["name"])){
            $query->where("name","LIKE","%".$data["name"]."%");}
        if(isset($data["description"])){
            $query->where("description","LIKE","%".$data["description"]."%");}
        if(isset($data["link"])){
            $query->where("link","LIKE","%".$data["link"]."%");}
        if(isset($data["image"])){
            $query->where("image","LIKE","%".$data["image"]."%");}
        return $query ;
    }
    public function getDescriptionAttribute($value){


          //  $style= "<style> .ql-hidden { visibility: hidden !important;   }; .ql-editor{  direction: rtl; !important;}</style> ";
         //   $script= "<script>  var editableElements = document.querySelectorAll('[contenteditable=true]');  for (var i = 0; i < editableElements.length; ++i) { editableElements[i].setAttribute('contenteditable', false); } </script> ";

            return  $value;//  $style." ".$value.''.$script;

    }

}
