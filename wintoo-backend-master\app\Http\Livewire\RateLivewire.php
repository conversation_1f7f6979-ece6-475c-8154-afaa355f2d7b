<?php

namespace App\Http\Livewire;

use App\Models\ExchangRate;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Region;
class RateLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $columes;
    public $searchable;
    public $page_length = 10;
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $paginationTheme = 'bootstrap';
    public  $search_array=[];
    protected $listeners = ['Region-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
    public $file_name = 'region';
    public function mount()
    {
        $searchable = ExchangRate::getSearchable();
        $this->searchable =collect($searchable);
        $this->columes =ExchangRate::getColumnLang();
        $this->searchable =ExchangRate::getSearchable();
        $this->page_length = request()->query('page_length',$this->page_length);
    }


    public function render()
    {
        $data =ExchangRate::search($this->search_array);
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);

       // if(in_array('region_show',$this->actions_permission()) ){
            return view('dashboard/rate/index',[ 'data'=>$data])->extends('dashboard_layout.main');
//        }else{
//            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
//        }


    }

    public function search(){}
    public function resetSearch(){
        $this->search_array=[];
    }

}

