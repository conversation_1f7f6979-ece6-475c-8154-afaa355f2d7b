<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Order;
class OrderLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 300;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Order-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'order';
        public function mount()
            {
                $searchable = Order::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Order::getColumnLang();
                $this->searchable =Order::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }

        public function render()
{
    $this->search_array = []; // Reset search array if needed

    $data = Order::with(['store.wallet'])->orderBy($this->sortBy, $this->sortDirection)->paginate($this->page_length);

    return view('dashboard.order.index', [
        'data' => $data,
    ])->extends('dashboard_layout.main');
}


        public function search(){}

        public function resetSearch(){
            $this->search_array=[];
         }

        public function edit($id){
             return redirect()->route('dashboard.order.edit',$id);
         }

        public function delete($id){
             $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Order-livewire:conformDelete',['id'=>$id]);
         }

        public function conformDelete($id){

             Order::find($id['id'])->delete();

             $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

         }
}

