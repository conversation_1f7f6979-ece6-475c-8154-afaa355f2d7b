<?php

namespace App\Models;

use Bepsvpt\Blurhash\Facades\BlurHash;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PostMedia extends Model
{

    use \App\Traits\PropertyGetter;

    use HasFactory;
    protected $guarded = [];
    protected $appends = ['media_url'/*,'media_url_hash_blur'*/];

    public function post()
    {
        return $this->belongsTo(Post::class);
    }
    public function getMediaUrlAttribute(){

    return $this->image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->image]):null;
   // return $this->image?str_replace(request()->getHost(),"d759yjlbemjei.cloudfront.net",route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->image])):null;

        //  return $this->image?asset('storage/'.$this->image):null;
    }


/*    public function getMediaUrlHashBlurAttribute(){
        return $this->image?BlurHash::encode('storage/'.$this->image):"";
    }*/

}
