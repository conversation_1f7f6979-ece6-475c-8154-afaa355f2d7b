"object"==typeof navigator&&function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define("Plyr",t):e.Plyr=t()}(this,function(){"use strict";var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=t(function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)}),i=t(function(e){var t=e.exports={version:"2.5.3"};"number"==typeof __e&&(__e=t)}),r=(i.version,function(e){return"object"==typeof e?null!==e:"function"==typeof e}),o=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e},a=function(e){try{return!!e()}catch(e){return!0}},s=!a(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),l=n.document,c=r(l)&&r(l.createElement),u=function(e){return c?l.createElement(e):{}},f=!s&&!a(function(){return 7!=Object.defineProperty(u("div"),"a",{get:function(){return 7}}).a}),d=function(e,t){if(!r(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!r(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")},h=Object.defineProperty,p={f:s?Object.defineProperty:function(e,t,n){if(o(e),t=d(t,!0),o(n),f)try{return h(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},g=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},m=s?function(e,t,n){return p.f(e,t,g(1,n))}:function(e,t,n){return e[t]=n,e},y={}.hasOwnProperty,v=function(e,t){return y.call(e,t)},b=0,w=Math.random(),E=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++b+w).toString(36))},k=t(function(e){var t=E("src"),r=Function.toString,o=(""+r).split("toString");i.inspectSource=function(e){return r.call(e)},(e.exports=function(e,i,r,a){var s="function"==typeof r;s&&(v(r,"name")||m(r,"name",i)),e[i]!==r&&(s&&(v(r,t)||m(r,t,e[i]?""+e[i]:o.join(String(i)))),e===n?e[i]=r:a?e[i]?e[i]=r:m(e,i,r):(delete e[i],m(e,i,r)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[t]||r.call(this)})}),T=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},S=function(e,t,n){if(T(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,r){return e.call(t,n,i,r)}}return function(){return e.apply(t,arguments)}},A=function(e,t,r){var o,a,s,l,c=e&A.F,u=e&A.G,f=e&A.S,d=e&A.P,h=e&A.B,p=u?n:f?n[t]||(n[t]={}):(n[t]||{}).prototype,g=u?i:i[t]||(i[t]={}),y=g.prototype||(g.prototype={});for(o in u&&(r=t),r)s=((a=!c&&p&&void 0!==p[o])?p:r)[o],l=h&&a?S(s,n):d&&"function"==typeof s?S(Function.call,s):s,p&&k(p,o,s,e&A.U),g[o]!=s&&m(g,o,l),d&&y[o]!=s&&(y[o]=s)};n.core=i,A.F=1,A.G=2,A.S=4,A.P=8,A.B=16,A.W=32,A.U=64,A.R=128;for(var _,C=A,P=E("typed_array"),M=E("view"),x=!(!n.ArrayBuffer||!n.DataView),L=x,O=0,N="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");O<9;)(_=n[N[O++]])?(m(_.prototype,P,!0),m(_.prototype,M,!0)):L=!1;var j={ABV:x,CONSTR:L,TYPED:P,VIEW:M},I=function(e,t,n){for(var i in t)k(e,i,t[i],n);return e},F=function(e,t,n,i){if(!(e instanceof t)||void 0!==i&&i in e)throw TypeError(n+": incorrect invocation!");return e},R=Math.ceil,q=Math.floor,B=function(e){return isNaN(e=+e)?0:(e>0?q:R)(e)},U=Math.min,V=function(e){return e>0?U(B(e),9007199254740991):0},D=function(e){if(void 0===e)return 0;var t=B(e),n=V(t);if(t!==n)throw RangeError("Wrong length!");return n},H={}.toString,W=function(e){return H.call(e).slice(8,-1)},z=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==W(e)?e.split(""):Object(e)},G=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},Y=function(e){return z(G(e))},K=Math.max,Q=Math.min,J=function(e,t){return(e=B(e))<0?K(e+t,0):Q(e,t)},$=function(e){return function(t,n,i){var r,o=Y(t),a=V(o.length),s=J(i,a);if(e&&n!=n){for(;a>s;)if((r=o[s++])!=r)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},X=n["__core-js_shared__"]||(n["__core-js_shared__"]={}),Z=function(e){return X[e]||(X[e]={})},ee=Z("keys"),te=function(e){return ee[e]||(ee[e]=E(e))},ne=$(!1),ie=te("IE_PROTO"),re=function(e,t){var n,i=Y(e),r=0,o=[];for(n in i)n!=ie&&v(i,n)&&o.push(n);for(;t.length>r;)v(i,n=t[r++])&&(~ne(o,n)||o.push(n));return o},oe="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),ae=oe.concat("length","prototype"),se={f:Object.getOwnPropertyNames||function(e){return re(e,ae)}},le=function(e){return Object(G(e))},ce=function(e){for(var t=le(this),n=V(t.length),i=arguments.length,r=J(i>1?arguments[1]:void 0,n),o=i>2?arguments[2]:void 0,a=void 0===o?n:J(o,n);a>r;)t[r++]=e;return t},ue=t(function(e){var t=Z("wks"),i=n.Symbol,r="function"==typeof i;(e.exports=function(e){return t[e]||(t[e]=r&&i[e]||(r?i:E)("Symbol."+e))}).store=t}),fe=p.f,de=ue("toStringTag"),he=function(e,t,n){e&&!v(e=n?e:e.prototype,de)&&fe(e,de,{configurable:!0,value:t})},pe=t(function(e,t){var i=se.f,r=p.f,o="prototype",l="Wrong index!",c=n.ArrayBuffer,u=n.DataView,f=n.Math,d=n.RangeError,h=n.Infinity,g=c,y=f.abs,v=f.pow,b=f.floor,w=f.log,E=f.LN2,k=s?"_b":"buffer",T=s?"_l":"byteLength",S=s?"_o":"byteOffset";function A(e,t,n){var i,r,o,a=new Array(n),s=8*n-t-1,l=(1<<s)-1,c=l>>1,u=23===t?v(2,-24)-v(2,-77):0,f=0,d=e<0||0===e&&1/e<0?1:0;for((e=y(e))!=e||e===h?(r=e!=e?1:0,i=l):(i=b(w(e)/E),e*(o=v(2,-i))<1&&(i--,o*=2),(e+=i+c>=1?u/o:u*v(2,1-c))*o>=2&&(i++,o/=2),i+c>=l?(r=0,i=l):i+c>=1?(r=(e*o-1)*v(2,t),i+=c):(r=e*v(2,c-1)*v(2,t),i=0));t>=8;a[f++]=255&r,r/=256,t-=8);for(i=i<<t|r,s+=t;s>0;a[f++]=255&i,i/=256,s-=8);return a[--f]|=128*d,a}function _(e,t,n){var i,r=8*n-t-1,o=(1<<r)-1,a=o>>1,s=r-7,l=n-1,c=e[l--],u=127&c;for(c>>=7;s>0;u=256*u+e[l],l--,s-=8);for(i=u&(1<<-s)-1,u>>=-s,s+=t;s>0;i=256*i+e[l],l--,s-=8);if(0===u)u=1-a;else{if(u===o)return i?NaN:c?-h:h;i+=v(2,t),u-=a}return(c?-1:1)*i*v(2,u-t)}function C(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function P(e){return[255&e]}function M(e){return[255&e,e>>8&255]}function x(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function L(e){return A(e,52,8)}function O(e){return A(e,23,4)}function N(e,t,n){r(e[o],t,{get:function(){return this[n]}})}function R(e,t,n,i){var r=D(+n);if(r+t>e[T])throw d(l);var o=e[k]._b,a=r+e[S],s=o.slice(a,a+t);return i?s:s.reverse()}function q(e,t,n,i,r,o){var a=D(+n);if(a+t>e[T])throw d(l);for(var s=e[k]._b,c=a+e[S],u=i(+r),f=0;f<t;f++)s[c+f]=u[o?f:t-f-1]}if(j.ABV){if(!a(function(){c(1)})||!a(function(){new c(-1)})||a(function(){return new c,new c(1.5),new c(NaN),"ArrayBuffer"!=c.name})){for(var U,H=(c=function(e){return F(this,c),new g(D(e))})[o]=g[o],W=i(g),z=0;W.length>z;)(U=W[z++])in c||m(c,U,g[U]);H.constructor=c}var G=new u(new c(2)),Y=u[o].setInt8;G.setInt8(0,2147483648),G.setInt8(1,2147483649),!G.getInt8(0)&&G.getInt8(1)||I(u[o],{setInt8:function(e,t){Y.call(this,e,t<<24>>24)},setUint8:function(e,t){Y.call(this,e,t<<24>>24)}},!0)}else c=function(e){F(this,c,"ArrayBuffer");var t=D(e);this._b=ce.call(new Array(t),0),this[T]=t},u=function(e,t,n){F(this,u,"DataView"),F(e,c,"DataView");var i=e[T],r=B(t);if(r<0||r>i)throw d("Wrong offset!");if(r+(n=void 0===n?i-r:V(n))>i)throw d("Wrong length!");this[k]=e,this[S]=r,this[T]=n},s&&(N(c,"byteLength","_l"),N(u,"buffer","_b"),N(u,"byteLength","_l"),N(u,"byteOffset","_o")),I(u[o],{getInt8:function(e){return R(this,1,e)[0]<<24>>24},getUint8:function(e){return R(this,1,e)[0]},getInt16:function(e){var t=R(this,2,e,arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=R(this,2,e,arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return C(R(this,4,e,arguments[1]))},getUint32:function(e){return C(R(this,4,e,arguments[1]))>>>0},getFloat32:function(e){return _(R(this,4,e,arguments[1]),23,4)},getFloat64:function(e){return _(R(this,8,e,arguments[1]),52,8)},setInt8:function(e,t){q(this,1,e,P,t)},setUint8:function(e,t){q(this,1,e,P,t)},setInt16:function(e,t){q(this,2,e,M,t,arguments[2])},setUint16:function(e,t){q(this,2,e,M,t,arguments[2])},setInt32:function(e,t){q(this,4,e,x,t,arguments[2])},setUint32:function(e,t){q(this,4,e,x,t,arguments[2])},setFloat32:function(e,t){q(this,4,e,O,t,arguments[2])},setFloat64:function(e,t){q(this,8,e,L,t,arguments[2])}});he(c,"ArrayBuffer"),he(u,"DataView"),m(u[o],j.VIEW,!0),t.ArrayBuffer=c,t.DataView=u}),ge=ue("species"),me=function(e,t){var n,i=o(e).constructor;return void 0===i||null==(n=o(i)[ge])?t:T(n)},ye=ue("species"),ve=function(e){var t=n[e];s&&t&&!t[ye]&&p.f(t,ye,{configurable:!0,get:function(){return this}})},be=n.ArrayBuffer,we=pe.ArrayBuffer,Ee=pe.DataView,ke=j.ABV&&be.isView,Te=we.prototype.slice,Se=j.VIEW;C(C.G+C.W+C.F*(be!==we),{ArrayBuffer:we}),C(C.S+C.F*!j.CONSTR,"ArrayBuffer",{isView:function(e){return ke&&ke(e)||r(e)&&Se in e}}),C(C.P+C.U+C.F*a(function(){return!new we(2).slice(1,void 0).byteLength}),"ArrayBuffer",{slice:function(e,t){if(void 0!==Te&&void 0===t)return Te.call(o(this),e);for(var n=o(this).byteLength,i=J(e,n),r=J(void 0===t?n:t,n),a=new(me(this,we))(V(r-i)),s=new Ee(this),l=new Ee(a),c=0;i<r;)l.setUint8(c++,s.getUint8(i++));return a}}),ve("ArrayBuffer");var Ae=ue("toStringTag"),_e="Arguments"==W(function(){return arguments}()),Ce=function(e){var t,n,i;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),Ae))?n:_e?W(t):"Object"==(i=W(t))&&"function"==typeof t.callee?"Arguments":i},Pe={},Me=ue("iterator"),xe=Array.prototype,Le=function(e){return void 0!==e&&(Pe.Array===e||xe[Me]===e)},Oe=Object.keys||function(e){return re(e,oe)},Ne=s?Object.defineProperties:function(e,t){o(e);for(var n,i=Oe(t),r=i.length,a=0;r>a;)p.f(e,n=i[a++],t[n]);return e},je=n.document,Ie=je&&je.documentElement,Fe=te("IE_PROTO"),Re=function(){},qe=function(){var e,t=u("iframe"),n=oe.length;for(t.style.display="none",Ie.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),qe=e.F;n--;)delete qe.prototype[oe[n]];return qe()},Be=Object.create||function(e,t){var n;return null!==e?(Re.prototype=o(e),n=new Re,Re.prototype=null,n[Fe]=e):n=qe(),void 0===t?n:Ne(n,t)},Ue=te("IE_PROTO"),Ve=Object.prototype,De=Object.getPrototypeOf||function(e){return e=le(e),v(e,Ue)?e[Ue]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Ve:null},He=ue("iterator"),We=i.getIteratorMethod=function(e){if(null!=e)return e[He]||e["@@iterator"]||Pe[Ce(e)]},ze=Array.isArray||function(e){return"Array"==W(e)},Ge=ue("species"),Ye=function(e,t){return new(function(e){var t;return ze(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!ze(t.prototype)||(t=void 0),r(t)&&null===(t=t[Ge])&&(t=void 0)),void 0===t?Array:t}(e))(t)},Ke=function(e,t){var n=1==e,i=2==e,r=3==e,o=4==e,a=6==e,s=5==e||a,l=t||Ye;return function(t,c,u){for(var f,d,h=le(t),p=z(h),g=S(c,u,3),m=V(p.length),y=0,v=n?l(t,m):i?l(t,0):void 0;m>y;y++)if((s||y in p)&&(d=g(f=p[y],y,h),e))if(n)v[y]=d;else if(d)switch(e){case 3:return!0;case 5:return f;case 6:return y;case 2:v.push(f)}else if(o)return!1;return a?-1:r||o?o:v}},Qe=ue("unscopables"),Je=Array.prototype;null==Je[Qe]&&m(Je,Qe,{});var $e=function(e){Je[Qe][e]=!0},Xe=function(e,t){return{value:t,done:!!e}},Ze={};m(Ze,ue("iterator"),function(){return this});var et=function(e,t,n){e.prototype=Be(Ze,{next:g(1,n)}),he(e,t+" Iterator")},tt=ue("iterator"),nt=!([].keys&&"next"in[].keys()),it=function(){return this},rt=function(e,t,n,i,r,o,a){et(n,t,i);var s,l,c,u=function(e){if(!nt&&e in p)return p[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},f=t+" Iterator",d="values"==r,h=!1,p=e.prototype,g=p[tt]||p["@@iterator"]||r&&p[r],y=!nt&&g||u(r),b=r?d?u("entries"):y:void 0,w="Array"==t&&p.entries||g;if(w&&(c=De(w.call(new e)))!==Object.prototype&&c.next&&(he(c,f,!0),v(c,tt)||m(c,tt,it)),d&&g&&"values"!==g.name&&(h=!0,y=function(){return g.call(this)}),(nt||h||!p[tt])&&m(p,tt,y),Pe[t]=y,Pe[f]=it,r)if(s={values:d?y:u("values"),keys:o?y:u("keys"),entries:b},a)for(l in s)l in p||k(p,l,s[l]);else C(C.P+C.F*(nt||h),t,s);return s},ot=rt(Array,"Array",function(e,t){this._t=Y(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,Xe(1)):Xe(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])},"values");Pe.Arguments=Pe.Array,$e("keys"),$e("values"),$e("entries");var at=ue("iterator"),st=!1;try{[7][at]().return=function(){st=!0}}catch(e){}var lt=function(e,t){if(!t&&!st)return!1;var n=!1;try{var i=[7],r=i[at]();r.next=function(){return{done:n=!0}},i[at]=function(){return r},e(i)}catch(e){}return n},ct=[].copyWithin||function(e,t){var n=le(this),i=V(n.length),r=J(e,i),o=J(t,i),a=arguments.length>2?arguments[2]:void 0,s=Math.min((void 0===a?i:J(a,i))-o,i-r),l=1;for(o<r&&r<o+s&&(l=-1,o+=s-1,r+=s-1);s-- >0;)o in n?n[r]=n[o]:delete n[r],r+=l,o+=l;return n},ut={f:{}.propertyIsEnumerable},ft=Object.getOwnPropertyDescriptor,dt={f:s?ft:function(e,t){if(e=Y(e),t=d(t,!0),f)try{return ft(e,t)}catch(e){}if(v(e,t))return g(!ut.f.call(e,t),e[t])}},ht=t(function(e){if(s){var t=n,i=a,o=C,l=j,c=pe,u=S,f=F,h=g,y=m,b=I,w=B,k=V,T=D,A=J,_=d,P=v,M=Ce,x=r,L=le,O=Le,N=Be,R=De,q=se.f,U=We,H=E,W=ue,z=Ke,G=$,Y=me,K=ot,Q=Pe,X=lt,Z=ve,ee=ce,te=ct,ne=p,ie=dt,re=ne.f,oe=ie.f,ae=t.RangeError,fe=t.TypeError,de=t.Uint8Array,he=Array.prototype,ge=c.ArrayBuffer,ye=c.DataView,be=z(0),we=z(2),Ee=z(3),ke=z(4),Te=z(5),Se=z(6),Ae=G(!0),_e=G(!1),Me=K.values,xe=K.keys,Oe=K.entries,Ne=he.lastIndexOf,je=he.reduce,Ie=he.reduceRight,Fe=he.join,Re=he.sort,qe=he.slice,Ue=he.toString,Ve=he.toLocaleString,He=W("iterator"),ze=W("toStringTag"),Ge=H("typed_constructor"),Ye=H("def_constructor"),Qe=l.CONSTR,Je=l.TYPED,$e=l.VIEW,Xe=z(1,function(e,t){return it(Y(e,e[Ye]),t)}),Ze=i(function(){return 1===new de(new Uint16Array([1]).buffer)[0]}),et=!!de&&!!de.prototype.set&&i(function(){new de(1).set({})}),tt=function(e,t){var n=w(e);if(n<0||n%t)throw ae("Wrong offset!");return n},nt=function(e){if(x(e)&&Je in e)return e;throw fe(e+" is not a typed array!")},it=function(e,t){if(!(x(e)&&Ge in e))throw fe("It is not a typed array constructor!");return new e(t)},rt=function(e,t){return at(Y(e,e[Ye]),t)},at=function(e,t){for(var n=0,i=t.length,r=it(e,i);i>n;)r[n]=t[n++];return r},st=function(e,t,n){re(e,t,{get:function(){return this._d[n]}})},ut=function(e){var t,n,i,r,o,a,s=L(e),l=arguments.length,c=l>1?arguments[1]:void 0,f=void 0!==c,d=U(s);if(null!=d&&!O(d)){for(a=d.call(s),i=[],t=0;!(o=a.next()).done;t++)i.push(o.value);s=i}for(f&&l>2&&(c=u(c,arguments[2],2)),t=0,n=k(s.length),r=it(this,n);n>t;t++)r[t]=f?c(s[t],t):s[t];return r},ft=function(){for(var e=0,t=arguments.length,n=it(this,t);t>e;)n[e]=arguments[e++];return n},ht=!!de&&i(function(){Ve.call(new de(1))}),pt=function(){return Ve.apply(ht?qe.call(nt(this)):nt(this),arguments)},gt={copyWithin:function(e,t){return te.call(nt(this),e,t,arguments.length>2?arguments[2]:void 0)},every:function(e){return ke(nt(this),e,arguments.length>1?arguments[1]:void 0)},fill:function(e){return ee.apply(nt(this),arguments)},filter:function(e){return rt(this,we(nt(this),e,arguments.length>1?arguments[1]:void 0))},find:function(e){return Te(nt(this),e,arguments.length>1?arguments[1]:void 0)},findIndex:function(e){return Se(nt(this),e,arguments.length>1?arguments[1]:void 0)},forEach:function(e){be(nt(this),e,arguments.length>1?arguments[1]:void 0)},indexOf:function(e){return _e(nt(this),e,arguments.length>1?arguments[1]:void 0)},includes:function(e){return Ae(nt(this),e,arguments.length>1?arguments[1]:void 0)},join:function(e){return Fe.apply(nt(this),arguments)},lastIndexOf:function(e){return Ne.apply(nt(this),arguments)},map:function(e){return Xe(nt(this),e,arguments.length>1?arguments[1]:void 0)},reduce:function(e){return je.apply(nt(this),arguments)},reduceRight:function(e){return Ie.apply(nt(this),arguments)},reverse:function(){for(var e,t=nt(this).length,n=Math.floor(t/2),i=0;i<n;)e=this[i],this[i++]=this[--t],this[t]=e;return this},some:function(e){return Ee(nt(this),e,arguments.length>1?arguments[1]:void 0)},sort:function(e){return Re.call(nt(this),e)},subarray:function(e,t){var n=nt(this),i=n.length,r=A(e,i);return new(Y(n,n[Ye]))(n.buffer,n.byteOffset+r*n.BYTES_PER_ELEMENT,k((void 0===t?i:A(t,i))-r))}},mt=function(e,t){return rt(this,qe.call(nt(this),e,t))},yt=function(e){nt(this);var t=tt(arguments[1],1),n=this.length,i=L(e),r=k(i.length),o=0;if(r+t>n)throw ae("Wrong length!");for(;o<r;)this[t+o]=i[o++]},vt={entries:function(){return Oe.call(nt(this))},keys:function(){return xe.call(nt(this))},values:function(){return Me.call(nt(this))}},bt=function(e,t){return x(e)&&e[Je]&&"symbol"!=typeof t&&t in e&&String(+t)==String(t)},wt=function(e,t){return bt(e,t=_(t,!0))?h(2,e[t]):oe(e,t)},Et=function(e,t,n){return!(bt(e,t=_(t,!0))&&x(n)&&P(n,"value"))||P(n,"get")||P(n,"set")||n.configurable||P(n,"writable")&&!n.writable||P(n,"enumerable")&&!n.enumerable?re(e,t,n):(e[t]=n.value,e)};Qe||(ie.f=wt,ne.f=Et),o(o.S+o.F*!Qe,"Object",{getOwnPropertyDescriptor:wt,defineProperty:Et}),i(function(){Ue.call({})})&&(Ue=Ve=function(){return Fe.call(this)});var kt=b({},gt);b(kt,vt),y(kt,He,vt.values),b(kt,{slice:mt,set:yt,constructor:function(){},toString:Ue,toLocaleString:pt}),st(kt,"buffer","b"),st(kt,"byteOffset","o"),st(kt,"byteLength","l"),st(kt,"length","e"),re(kt,ze,{get:function(){return this[Je]}}),e.exports=function(e,n,r,a){var s=e+((a=!!a)?"Clamped":"")+"Array",c="get"+e,u="set"+e,d=t[s],h=d||{},p=d&&R(d),g=!d||!l.ABV,m={},v=d&&d.prototype,b=function(e,t){re(e,t,{get:function(){return function(e,t){var i=e._d;return i.v[c](t*n+i.o,Ze)}(this,t)},set:function(e){return function(e,t,i){var r=e._d;a&&(i=(i=Math.round(i))<0?0:i>255?255:255&i),r.v[u](t*n+r.o,i,Ze)}(this,t,e)},enumerable:!0})};g?(d=r(function(e,t,i,r){f(e,d,s,"_d");var o,a,l,c,u=0,h=0;if(x(t)){if(!(t instanceof ge||"ArrayBuffer"==(c=M(t))||"SharedArrayBuffer"==c))return Je in t?at(d,t):ut.call(d,t);o=t,h=tt(i,n);var p=t.byteLength;if(void 0===r){if(p%n)throw ae("Wrong length!");if((a=p-h)<0)throw ae("Wrong length!")}else if((a=k(r)*n)+h>p)throw ae("Wrong length!");l=a/n}else l=T(t),o=new ge(a=l*n);for(y(e,"_d",{b:o,o:h,l:a,e:l,v:new ye(o)});u<l;)b(e,u++)}),v=d.prototype=N(kt),y(v,"constructor",d)):i(function(){d(1)})&&i(function(){new d(-1)})&&X(function(e){new d,new d(null),new d(1.5),new d(e)},!0)||(d=r(function(e,t,i,r){var o;return f(e,d,s),x(t)?t instanceof ge||"ArrayBuffer"==(o=M(t))||"SharedArrayBuffer"==o?void 0!==r?new h(t,tt(i,n),r):void 0!==i?new h(t,tt(i,n)):new h(t):Je in t?at(d,t):ut.call(d,t):new h(T(t))}),be(p!==Function.prototype?q(h).concat(q(p)):q(h),function(e){e in d||y(d,e,h[e])}),d.prototype=v,v.constructor=d);var w=v[He],E=!!w&&("values"==w.name||null==w.name),S=vt.values;y(d,Ge,!0),y(v,Je,s),y(v,$e,!0),y(v,Ye,d),(a?new d(1)[ze]==s:ze in v)||re(v,ze,{get:function(){return s}}),m[s]=d,o(o.G+o.W+o.F*(d!=h),m),o(o.S,s,{BYTES_PER_ELEMENT:n}),o(o.S+o.F*i(function(){h.of.call(d,1)}),s,{from:ut,of:ft}),"BYTES_PER_ELEMENT"in v||y(v,"BYTES_PER_ELEMENT",n),o(o.P,s,gt),Z(s),o(o.P+o.F*et,s,{set:yt}),o(o.P+o.F*!E,s,vt),v.toString!=Ue&&(v.toString=Ue),o(o.P+o.F*i(function(){new d(1).slice()}),s,{slice:mt}),o(o.P+o.F*(i(function(){return[1,2].toLocaleString()!=new d([1,2]).toLocaleString()})||!i(function(){v.toLocaleString.call([1,2])})),s,{toLocaleString:pt}),Q[s]=E?w:S,E||y(v,He,S)}}else e.exports=function(){}});ht("Int8",1,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Uint8",1,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Uint8",1,function(e){return function(t,n,i){return e(this,t,n,i)}},!0),ht("Int16",2,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Uint16",2,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Int32",4,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Uint32",4,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Float32",4,function(e){return function(t,n,i){return e(this,t,n,i)}}),ht("Float64",8,function(e){return function(t,n,i){return e(this,t,n,i)}});var pt=function(e,t,n,i){try{return i?t(o(n)[0],n[1]):t(n)}catch(t){var r=e.return;throw void 0!==r&&o(r.call(e)),t}},gt=t(function(e){var t={},n={},i=e.exports=function(e,i,r,a,s){var l,c,u,f,d=s?function(){return e}:We(e),h=S(r,a,i?2:1),p=0;if("function"!=typeof d)throw TypeError(e+" is not iterable!");if(Le(d)){for(l=V(e.length);l>p;p++)if((f=i?h(o(c=e[p])[0],c[1]):h(e[p]))===t||f===n)return f}else for(u=d.call(e);!(c=u.next()).done;)if((f=pt(u,h,c.value,i))===t||f===n)return f};i.BREAK=t,i.RETURN=n}),mt=t(function(e){var t=E("meta"),n=p.f,i=0,o=Object.isExtensible||function(){return!0},s=!a(function(){return o(Object.preventExtensions({}))}),l=function(e){n(e,t,{value:{i:"O"+ ++i,w:{}}})},c=e.exports={KEY:t,NEED:!1,fastKey:function(e,n){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!v(e,t)){if(!o(e))return"F";if(!n)return"E";l(e)}return e[t].i},getWeak:function(e,n){if(!v(e,t)){if(!o(e))return!0;if(!n)return!1;l(e)}return e[t].w},onFreeze:function(e){return s&&c.NEED&&o(e)&&!v(e,t)&&l(e),e}}}),yt=(mt.KEY,mt.NEED,mt.fastKey,mt.getWeak,mt.onFreeze,function(e,t){if(!r(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}),vt=p.f,bt=mt.fastKey,wt=s?"_s":"size",Et=function(e,t){var n,i=bt(t);if("F"!==i)return e._i[i];for(n=e._f;n;n=n.n)if(n.k==t)return n},kt={getConstructor:function(e,t,n,i){var r=e(function(e,o){F(e,r,t,"_i"),e._t=t,e._i=Be(null),e._f=void 0,e._l=void 0,e[wt]=0,null!=o&&gt(o,n,e[i],e)});return I(r.prototype,{clear:function(){for(var e=yt(this,t),n=e._i,i=e._f;i;i=i.n)i.r=!0,i.p&&(i.p=i.p.n=void 0),delete n[i.i];e._f=e._l=void 0,e[wt]=0},delete:function(e){var n=yt(this,t),i=Et(n,e);if(i){var r=i.n,o=i.p;delete n._i[i.i],i.r=!0,o&&(o.n=r),r&&(r.p=o),n._f==i&&(n._f=r),n._l==i&&(n._l=o),n[wt]--}return!!i},forEach:function(e){yt(this,t);for(var n,i=S(e,arguments.length>1?arguments[1]:void 0,3);n=n?n.n:this._f;)for(i(n.v,n.k,this);n&&n.r;)n=n.p},has:function(e){return!!Et(yt(this,t),e)}}),s&&vt(r.prototype,"size",{get:function(){return yt(this,t)[wt]}}),r},def:function(e,t,n){var i,r,o=Et(e,t);return o?o.v=n:(e._l=o={i:r=bt(t,!0),k:t,v:n,p:i=e._l,n:void 0,r:!1},e._f||(e._f=o),i&&(i.n=o),e[wt]++,"F"!==r&&(e._i[r]=o)),e},getEntry:Et,setStrong:function(e,t,n){rt(e,t,function(e,n){this._t=yt(e,t),this._k=n,this._l=void 0},function(){for(var e=this._k,t=this._l;t&&t.r;)t=t.p;return this._t&&(this._l=t=t?t.n:this._t._f)?Xe(0,"keys"==e?t.k:"values"==e?t.v:[t.k,t.v]):(this._t=void 0,Xe(1))},n?"entries":"values",!n,!0),ve(t)}},Tt=function(e,t){if(o(e),!r(t)&&null!==t)throw TypeError(t+": can't set as prototype!")},St={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=S(Function.call,dt.f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,i){return Tt(e,i),t?e.__proto__=i:n(e,i),e}}({},!1):void 0),check:Tt},At=St.set,_t=function(e,t,i,o,s,l){var c=n[e],u=c,f=s?"set":"add",d=u&&u.prototype,h={},p=function(e){var t=d[e];k(d,e,"delete"==e?function(e){return!(l&&!r(e))&&t.call(this,0===e?0:e)}:"has"==e?function(e){return!(l&&!r(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return l&&!r(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,n){return t.call(this,0===e?0:e,n),this})};if("function"==typeof u&&(l||d.forEach&&!a(function(){(new u).entries().next()}))){var g=new u,m=g[f](l?{}:-0,1)!=g,y=a(function(){g.has(1)}),v=lt(function(e){new u(e)}),b=!l&&a(function(){for(var e=new u,t=5;t--;)e[f](t,t);return!e.has(-0)});v||((u=t(function(t,n){F(t,u,e);var i=function(e,t,n){var i,o=t.constructor;return o!==n&&"function"==typeof o&&(i=o.prototype)!==n.prototype&&r(i)&&At&&At(e,i),e}(new c,t,u);return null!=n&&gt(n,s,i[f],i),i})).prototype=d,d.constructor=u),(y||b)&&(p("delete"),p("has"),s&&p("get")),(b||m)&&p(f),l&&d.clear&&delete d.clear}else u=o.getConstructor(t,e,s,f),I(u.prototype,i),mt.NEED=!0;return he(u,e),h[e]=u,C(C.G+C.W+C.F*(u!=c),h),l||o.setStrong(u,e,s),u},Ct=(_t("Map",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{get:function(e){var t=kt.getEntry(yt(this,"Map"),e);return t&&t.v},set:function(e,t){return kt.def(yt(this,"Map"),0===e?0:e,t)}},kt,!0),_t("Set",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(e){return kt.def(yt(this,"Set"),e=0===e?0:e,e)}},kt),{f:Object.getOwnPropertySymbols}),Pt=Object.assign,Mt=!Pt||a(function(){var e={},t={},n=Symbol(),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach(function(e){t[e]=e}),7!=Pt({},e)[n]||Object.keys(Pt({},t)).join("")!=i})?function(e,t){for(var n=le(e),i=arguments.length,r=1,o=Ct.f,a=ut.f;i>r;)for(var s,l=z(arguments[r++]),c=o?Oe(l).concat(o(l)):Oe(l),u=c.length,f=0;u>f;)a.call(l,s=c[f++])&&(n[s]=l[s]);return n}:Pt,xt=mt.getWeak,Lt=Ke(5),Ot=Ke(6),Nt=0,jt=function(e){return e._l||(e._l=new It)},It=function(){this.a=[]},Ft=function(e,t){return Lt(e.a,function(e){return e[0]===t})};It.prototype={get:function(e){var t=Ft(this,e);if(t)return t[1]},has:function(e){return!!Ft(this,e)},set:function(e,t){var n=Ft(this,e);n?n[1]=t:this.a.push([e,t])},delete:function(e){var t=Ot(this.a,function(t){return t[0]===e});return~t&&this.a.splice(t,1),!!~t}};var Rt={getConstructor:function(e,t,n,i){var o=e(function(e,r){F(e,o,t,"_i"),e._t=t,e._i=Nt++,e._l=void 0,null!=r&&gt(r,n,e[i],e)});return I(o.prototype,{delete:function(e){if(!r(e))return!1;var n=xt(e);return!0===n?jt(yt(this,t)).delete(e):n&&v(n,this._i)&&delete n[this._i]},has:function(e){if(!r(e))return!1;var n=xt(e);return!0===n?jt(yt(this,t)).has(e):n&&v(n,this._i)}}),o},def:function(e,t,n){var i=xt(o(t),!0);return!0===i?jt(e).set(t,n):i[e._i]=n,e},ufstore:jt};t(function(e){var t,n=Ke(0),i=mt.getWeak,o=Object.isExtensible,s=Rt.ufstore,l={},c=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},u={get:function(e){if(r(e)){var t=i(e);return!0===t?s(yt(this,"WeakMap")).get(e):t?t[this._i]:void 0}},set:function(e,t){return Rt.def(yt(this,"WeakMap"),e,t)}},f=e.exports=_t("WeakMap",c,u,Rt,!0,!0);a(function(){return 7!=(new f).set((Object.freeze||Object)(l),7).get(l)})&&(t=Rt.getConstructor(c,"WeakMap"),Mt(t.prototype,u),mt.NEED=!0,n(["delete","has","get","set"],function(e){var n=f.prototype,i=n[e];k(n,e,function(n,a){if(r(n)&&!o(n)){this._f||(this._f=new t);var s=this._f[e](n,a);return"set"==e?this:s}return i.call(this,n,a)})}))});_t("WeakSet",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(e){return Rt.def(yt(this,"WeakSet"),e,!0)}},Rt,!1,!0);var qt=(n.Reflect||{}).apply,Bt=Function.apply;C(C.S+C.F*!a(function(){qt(function(){})}),"Reflect",{apply:function(e,t,n){var i=T(e),r=o(n);return qt?qt(i,t,r):Bt.call(i,t,r)}});var Ut=function(e,t,n){var i=void 0===n;switch(t.length){case 0:return i?e():e.call(n);case 1:return i?e(t[0]):e.call(n,t[0]);case 2:return i?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return i?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return i?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)},Vt=[].slice,Dt={},Ht=Function.bind||function(e){var t=T(this),n=Vt.call(arguments,1),i=function(){var r=n.concat(Vt.call(arguments));return this instanceof i?function(e,t,n){if(!(t in Dt)){for(var i=[],r=0;r<t;r++)i[r]="a["+r+"]";Dt[t]=Function("F,a","return new F("+i.join(",")+")")}return Dt[t](e,n)}(t,r.length,r):Ut(t,r,e)};return r(t.prototype)&&(i.prototype=t.prototype),i},Wt=(n.Reflect||{}).construct,zt=a(function(){function e(){}return!(Wt(function(){},[],e)instanceof e)}),Gt=!a(function(){Wt(function(){})});C(C.S+C.F*(zt||Gt),"Reflect",{construct:function(e,t){T(e),o(t);var n=arguments.length<3?e:T(arguments[2]);if(Gt&&!zt)return Wt(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var i=[null];return i.push.apply(i,t),new(Ht.apply(e,i))}var a=n.prototype,s=Be(r(a)?a:Object.prototype),l=Function.apply.call(e,s,t);return r(l)?l:s}}),C(C.S+C.F*a(function(){Reflect.defineProperty(p.f({},1,{value:1}),1,{value:2})}),"Reflect",{defineProperty:function(e,t,n){o(e),t=d(t,!0),o(n);try{return p.f(e,t,n),!0}catch(e){return!1}}});var Yt=dt.f;C(C.S,"Reflect",{deleteProperty:function(e,t){var n=Yt(o(e),t);return!(n&&!n.configurable)&&delete e[t]}}),C(C.S,"Reflect",{get:function e(t,n){var i,a,s=arguments.length<3?t:arguments[2];return o(t)===s?t[n]:(i=dt.f(t,n))?v(i,"value")?i.value:void 0!==i.get?i.get.call(s):void 0:r(a=De(t))?e(a,n,s):void 0}}),C(C.S,"Reflect",{getOwnPropertyDescriptor:function(e,t){return dt.f(o(e),t)}}),C(C.S,"Reflect",{getPrototypeOf:function(e){return De(o(e))}}),C(C.S,"Reflect",{has:function(e,t){return t in e}});var Kt=Object.isExtensible;C(C.S,"Reflect",{isExtensible:function(e){return o(e),!Kt||Kt(e)}});var Qt=n.Reflect,Jt=Qt&&Qt.ownKeys||function(e){var t=se.f(o(e)),n=Ct.f;return n?t.concat(n(e)):t};C(C.S,"Reflect",{ownKeys:Jt});var $t=Object.preventExtensions;C(C.S,"Reflect",{preventExtensions:function(e){o(e);try{return $t&&$t(e),!0}catch(e){return!1}}}),C(C.S,"Reflect",{set:function e(t,n,i){var a,s,l=arguments.length<4?t:arguments[3],c=dt.f(o(t),n);if(!c){if(r(s=De(t)))return e(s,n,i,l);c=g(0)}return v(c,"value")?!(!1===c.writable||!r(l)||((a=dt.f(l,n)||g(0)).value=i,p.f(l,n,a),0)):void 0!==c.set&&(c.set.call(l,i),!0)}}),St&&C(C.S,"Reflect",{setPrototypeOf:function(e,t){St.check(e,t);try{return St.set(e,t),!0}catch(e){return!1}}});var Xt,Zt,en,tn=n.process,nn=n.setImmediate,rn=n.clearImmediate,on=n.MessageChannel,an=n.Dispatch,sn=0,ln={},cn=function(){var e=+this;if(ln.hasOwnProperty(e)){var t=ln[e];delete ln[e],t()}},un=function(e){cn.call(e.data)};nn&&rn||(nn=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return ln[++sn]=function(){Ut("function"==typeof e?e:Function(e),t)},Xt(sn),sn},rn=function(e){delete ln[e]},"process"==W(tn)?Xt=function(e){tn.nextTick(S(cn,e,1))}:an&&an.now?Xt=function(e){an.now(S(cn,e,1))}:on?(en=(Zt=new on).port2,Zt.port1.onmessage=un,Xt=S(en.postMessage,en,1)):n.addEventListener&&"function"==typeof postMessage&&!n.importScripts?(Xt=function(e){n.postMessage(e+"","*")},n.addEventListener("message",un,!1)):Xt="onreadystatechange"in u("script")?function(e){Ie.appendChild(u("script")).onreadystatechange=function(){Ie.removeChild(this),cn.call(e)}}:function(e){setTimeout(S(cn,e,1),0)});var fn={set:nn,clear:rn},dn=fn.set,hn=n.MutationObserver||n.WebKitMutationObserver,pn=n.process,gn=n.Promise,mn="process"==W(pn);function yn(e){var t,n;this.promise=new e(function(e,i){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=i}),this.resolve=T(t),this.reject=T(n)}var vn,bn,wn,En,kn={f:function(e){return new yn(e)}},Tn=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}},Sn=fn.set,An=function(){var e,t,i,r=function(){var n,r;for(mn&&(n=pn.domain)&&n.exit();e;){r=e.fn,e=e.next;try{r()}catch(n){throw e?i():t=void 0,n}}t=void 0,n&&n.enter()};if(mn)i=function(){pn.nextTick(r)};else if(!hn||n.navigator&&n.navigator.standalone)if(gn&&gn.resolve){var o=gn.resolve();i=function(){o.then(r)}}else i=function(){dn.call(n,r)};else{var a=!0,s=document.createTextNode("");new hn(r).observe(s,{characterData:!0}),i=function(){s.data=a=!a}}return function(n){var r={fn:n,next:void 0};t&&(t.next=r),e||(e=r,i()),t=r}}(),_n=n.TypeError,Cn=n.process,Pn=n.Promise,Mn="process"==Ce(Cn),xn=function(){},Ln=bn=kn.f,On=!!function(){try{var e=Pn.resolve(1),t=(e.constructor={})[ue("species")]=function(e){e(xn,xn)};return(Mn||"function"==typeof PromiseRejectionEvent)&&e.then(xn)instanceof t}catch(e){}}(),Nn=function(e){var t;return!(!r(e)||"function"!=typeof(t=e.then))&&t},jn=function(e,t){if(!e._n){e._n=!0;var n=e._c;An(function(){for(var i=e._v,r=1==e._s,o=0,a=function(t){var n,o,a=r?t.ok:t.fail,s=t.resolve,l=t.reject,c=t.domain;try{a?(r||(2==e._h&&Rn(e),e._h=1),!0===a?n=i:(c&&c.enter(),n=a(i),c&&c.exit()),n===t.promise?l(_n("Promise-chain cycle")):(o=Nn(n))?o.call(n,s,l):s(n)):l(i)}catch(e){l(e)}};n.length>o;)a(n[o++]);e._c=[],e._n=!1,t&&!e._h&&In(e)})}},In=function(e){Sn.call(n,function(){var t,i,r,o=e._v,a=Fn(e);if(a&&(t=Tn(function(){Mn?Cn.emit("unhandledRejection",o,e):(i=n.onunhandledrejection)?i({promise:e,reason:o}):(r=n.console)&&r.error&&r.error("Unhandled promise rejection",o)}),e._h=Mn||Fn(e)?2:1),e._a=void 0,a&&t.e)throw t.v})},Fn=function(e){return 1!==e._h&&0===(e._a||e._c).length},Rn=function(e){Sn.call(n,function(){var t;Mn?Cn.emit("rejectionHandled",e):(t=n.onrejectionhandled)&&t({promise:e,reason:e._v})})},qn=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),jn(t,!0))},Bn=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw _n("Promise can't be resolved itself");(t=Nn(e))?An(function(){var i={_w:n,_d:!1};try{t.call(e,S(Bn,i,1),S(qn,i,1))}catch(e){qn.call(i,e)}}):(n._v=e,n._s=1,jn(n,!1))}catch(e){qn.call({_w:n,_d:!1},e)}}};On||(Pn=function(e){F(this,Pn,"Promise","_h"),T(e),vn.call(this);try{e(S(Bn,this,1),S(qn,this,1))}catch(e){qn.call(this,e)}},(vn=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=I(Pn.prototype,{then:function(e,t){var n=Ln(me(this,Pn));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=Mn?Cn.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&jn(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),wn=function(){var e=new vn;this.promise=e,this.resolve=S(Bn,e,1),this.reject=S(qn,e,1)},kn.f=Ln=function(e){return e===Pn||e===En?new wn(e):bn(e)}),C(C.G+C.W+C.F*!On,{Promise:Pn}),he(Pn,"Promise"),ve("Promise"),En=i.Promise,C(C.S+C.F*!On,"Promise",{reject:function(e){var t=Ln(this);return(0,t.reject)(e),t.promise}}),C(C.S+C.F*!On,"Promise",{resolve:function(e){return function(e,t){if(o(e),r(t)&&t.constructor===e)return t;var n=kn.f(e);return(0,n.resolve)(t),n.promise}(this,e)}}),C(C.S+C.F*!(On&&lt(function(e){Pn.all(e).catch(xn)})),"Promise",{all:function(e){var t=this,n=Ln(t),i=n.resolve,r=n.reject,o=Tn(function(){var n=[],o=0,a=1;gt(e,!1,function(e){var s=o++,l=!1;n.push(void 0),a++,t.resolve(e).then(function(e){l||(l=!0,n[s]=e,--a||i(n))},r)}),--a||i(n)});return o.e&&r(o.v),n.promise},race:function(e){var t=this,n=Ln(t),i=n.reject,r=Tn(function(){gt(e,!1,function(e){t.resolve(e).then(n.resolve,i)})});return r.e&&i(r.v),n.promise}});var Un={f:ue},Vn=p.f,Dn=function(e){var t=i.Symbol||(i.Symbol=n.Symbol||{});"_"==e.charAt(0)||e in t||Vn(t,e,{value:Un.f(e)})},Hn=se.f,Wn={}.toString,zn="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Gn={f:function(e){return zn&&"[object Window]"==Wn.call(e)?function(e){try{return Hn(e)}catch(e){return zn.slice()}}(e):Hn(Y(e))}},Yn=mt.KEY,Kn=dt.f,Qn=p.f,Jn=Gn.f,$n=n.Symbol,Xn=n.JSON,Zn=Xn&&Xn.stringify,ei=ue("_hidden"),ti=ue("toPrimitive"),ni={}.propertyIsEnumerable,ii=Z("symbol-registry"),ri=Z("symbols"),oi=Z("op-symbols"),ai=Object.prototype,si="function"==typeof $n,li=n.QObject,ci=!li||!li.prototype||!li.prototype.findChild,ui=s&&a(function(){return 7!=Be(Qn({},"a",{get:function(){return Qn(this,"a",{value:7}).a}})).a})?function(e,t,n){var i=Kn(ai,t);i&&delete ai[t],Qn(e,t,n),i&&e!==ai&&Qn(ai,t,i)}:Qn,fi=function(e){var t=ri[e]=Be($n.prototype);return t._k=e,t},di=si&&"symbol"==typeof $n.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof $n},hi=function(e,t,n){return e===ai&&hi(oi,t,n),o(e),t=d(t,!0),o(n),v(ri,t)?(n.enumerable?(v(e,ei)&&e[ei][t]&&(e[ei][t]=!1),n=Be(n,{enumerable:g(0,!1)})):(v(e,ei)||Qn(e,ei,g(1,{})),e[ei][t]=!0),ui(e,t,n)):Qn(e,t,n)},pi=function(e,t){o(e);for(var n,i=function(e){var t=Oe(e),n=Ct.f;if(n)for(var i,r=n(e),o=ut.f,a=0;r.length>a;)o.call(e,i=r[a++])&&t.push(i);return t}(t=Y(t)),r=0,a=i.length;a>r;)hi(e,n=i[r++],t[n]);return e},gi=function(e){var t=ni.call(this,e=d(e,!0));return!(this===ai&&v(ri,e)&&!v(oi,e))&&(!(t||!v(this,e)||!v(ri,e)||v(this,ei)&&this[ei][e])||t)},mi=function(e,t){if(e=Y(e),t=d(t,!0),e!==ai||!v(ri,t)||v(oi,t)){var n=Kn(e,t);return!n||!v(ri,t)||v(e,ei)&&e[ei][t]||(n.enumerable=!0),n}},yi=function(e){for(var t,n=Jn(Y(e)),i=[],r=0;n.length>r;)v(ri,t=n[r++])||t==ei||t==Yn||i.push(t);return i},vi=function(e){for(var t,n=e===ai,i=Jn(n?oi:Y(e)),r=[],o=0;i.length>o;)!v(ri,t=i[o++])||n&&!v(ai,t)||r.push(ri[t]);return r};si||(k(($n=function(){if(this instanceof $n)throw TypeError("Symbol is not a constructor!");var e=E(arguments.length>0?arguments[0]:void 0),t=function(n){this===ai&&t.call(oi,n),v(this,ei)&&v(this[ei],e)&&(this[ei][e]=!1),ui(this,e,g(1,n))};return s&&ci&&ui(ai,e,{configurable:!0,set:t}),fi(e)}).prototype,"toString",function(){return this._k}),dt.f=mi,p.f=hi,se.f=Gn.f=yi,ut.f=gi,Ct.f=vi,s&&k(ai,"propertyIsEnumerable",gi,!0),Un.f=function(e){return fi(ue(e))}),C(C.G+C.W+C.F*!si,{Symbol:$n});for(var bi="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),wi=0;bi.length>wi;)ue(bi[wi++]);for(var Ei=Oe(ue.store),ki=0;Ei.length>ki;)Dn(Ei[ki++]);C(C.S+C.F*!si,"Symbol",{for:function(e){return v(ii,e+="")?ii[e]:ii[e]=$n(e)},keyFor:function(e){if(!di(e))throw TypeError(e+" is not a symbol!");for(var t in ii)if(ii[t]===e)return t},useSetter:function(){ci=!0},useSimple:function(){ci=!1}}),C(C.S+C.F*!si,"Object",{create:function(e,t){return void 0===t?Be(e):pi(Be(e),t)},defineProperty:hi,defineProperties:pi,getOwnPropertyDescriptor:mi,getOwnPropertyNames:yi,getOwnPropertySymbols:vi}),Xn&&C(C.S+C.F*(!si||a(function(){var e=$n();return"[null]"!=Zn([e])||"{}"!=Zn({a:e})||"{}"!=Zn(Object(e))})),"JSON",{stringify:function(e){for(var t,n,i=[e],o=1;arguments.length>o;)i.push(arguments[o++]);if(n=t=i[1],(r(t)||void 0!==e)&&!di(e))return ze(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!di(t))return t}),i[1]=t,Zn.apply(Xn,i)}}),$n.prototype[ti]||m($n.prototype,ti,$n.prototype.valueOf),he($n,"Symbol"),he(Math,"Math",!0),he(n.JSON,"JSON",!0);var Ti=function(e,t){var n=(i.Object||{})[e]||Object[e],r={};r[e]=t(n),C(C.S+C.F*a(function(){n(1)}),"Object",r)},Si=mt.onFreeze;Ti("freeze",function(e){return function(t){return e&&r(t)?e(Si(t)):t}});var Ai=mt.onFreeze;Ti("seal",function(e){return function(t){return e&&r(t)?e(Ai(t)):t}});var _i=mt.onFreeze;Ti("preventExtensions",function(e){return function(t){return e&&r(t)?e(_i(t)):t}}),Ti("isFrozen",function(e){return function(t){return!r(t)||!!e&&e(t)}}),Ti("isSealed",function(e){return function(t){return!r(t)||!!e&&e(t)}}),Ti("isExtensible",function(e){return function(t){return!!r(t)&&(!e||e(t))}});var Ci=dt.f;Ti("getOwnPropertyDescriptor",function(){return function(e,t){return Ci(Y(e),t)}}),Ti("getPrototypeOf",function(){return function(e){return De(le(e))}}),Ti("keys",function(){return function(e){return Oe(le(e))}}),Ti("getOwnPropertyNames",function(){return Gn.f}),C(C.S+C.F,"Object",{assign:Mt});var Pi=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};C(C.S,"Object",{is:Pi});var Mi=p.f,xi=Function.prototype,Li=/^\s*function ([^ (]*)/;"name"in xi||s&&Mi(xi,"name",{configurable:!0,get:function(){try{return(""+this).match(Li)[1]}catch(e){return""}}}),C(C.S,"String",{raw:function(e){for(var t=Y(e.raw),n=V(t.length),i=arguments.length,r=[],o=0;n>o;)r.push(String(t[o++])),o<i&&r.push(String(arguments[o]));return r.join("")}});var Oi=String.fromCharCode,Ni=String.fromCodePoint;C(C.S+C.F*(!!Ni&&1!=Ni.length),"String",{fromCodePoint:function(e){for(var t,n=[],i=arguments.length,r=0;i>r;){if(t=+arguments[r++],J(t,1114111)!==t)throw RangeError(t+" is not a valid code point");n.push(t<65536?Oi(t):Oi(55296+((t-=65536)>>10),t%1024+56320))}return n.join("")}});var ji,Ii=(ji=!1,function(e,t){var n,i,r=String(G(e)),o=B(t),a=r.length;return o<0||o>=a?ji?"":void 0:(n=r.charCodeAt(o))<55296||n>56319||o+1===a||(i=r.charCodeAt(o+1))<56320||i>57343?ji?r.charAt(o):n:ji?r.slice(o,o+2):i-56320+(n-55296<<10)+65536});C(C.P,"String",{codePointAt:function(e){return Ii(this,e)}});var Fi=function(e){var t=String(G(this)),n="",i=B(e);if(i<0||i==1/0)throw RangeError("Count can't be negative");for(;i>0;(i>>>=1)&&(t+=t))1&i&&(n+=t);return n};C(C.P,"String",{repeat:Fi});var Ri=ue("match"),qi=function(e){var t;return r(e)&&(void 0!==(t=e[Ri])?!!t:"RegExp"==W(e))},Bi=function(e,t,n){if(qi(t))throw TypeError("String#"+n+" doesn't accept regex!");return String(G(e))},Ui=ue("match"),Vi=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Ui]=!1,!"/./"[e](t)}catch(e){}}return!0},Di="".startsWith;C(C.P+C.F*Vi("startsWith"),"String",{startsWith:function(e){var t=Bi(this,e,"startsWith"),n=V(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),i=String(e);return Di?Di.call(t,i,n):t.slice(n,n+i.length)===i}});var Hi="".endsWith;C(C.P+C.F*Vi("endsWith"),"String",{endsWith:function(e){var t=Bi(this,e,"endsWith"),n=arguments.length>1?arguments[1]:void 0,i=V(t.length),r=void 0===n?i:Math.min(V(n),i),o=String(e);return Hi?Hi.call(t,o,r):t.slice(r-o.length,r)===o}});C(C.P+C.F*Vi("includes"),"String",{includes:function(e){return!!~Bi(this,e,"includes").indexOf(e,arguments.length>1?arguments[1]:void 0)}});s&&"g"!=/./g.flags&&p.f(RegExp.prototype,"flags",{configurable:!0,get:function(){var e=o(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}});var Wi=function(e,t,n){var i=ue(e),r=n(G,i,""[e]),o=r[0],s=r[1];a(function(){var t={};return t[i]=function(){return 7},7!=""[e](t)})&&(k(String.prototype,e,o),m(RegExp.prototype,i,2==t?function(e,t){return s.call(e,this,t)}:function(e){return s.call(e,this)}))};Wi("match",1,function(e,t,n){return[function(n){var i=e(this),r=null==n?void 0:n[t];return void 0!==r?r.call(n,i):new RegExp(n)[t](String(i))},n]}),Wi("replace",2,function(e,t,n){return[function(i,r){var o=e(this),a=null==i?void 0:i[t];return void 0!==a?a.call(i,o,r):n.call(String(o),i,r)},n]}),Wi("split",2,function(e,t,n){var i=qi,r=n,o=[].push;if("c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length){var a=void 0===/()??/.exec("")[1];n=function(e,t){var n=String(this);if(void 0===e&&0===t)return[];if(!i(e))return r.call(n,e,t);var s,l,c,u,f,d=[],h=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,g=void 0===t?4294967295:t>>>0,m=new RegExp(e.source,h+"g");for(a||(s=new RegExp("^"+m.source+"$(?!\\s)",h));(l=m.exec(n))&&!((c=l.index+l[0].length)>p&&(d.push(n.slice(p,l.index)),!a&&l.length>1&&l[0].replace(s,function(){for(f=1;f<arguments.length-2;f++)void 0===arguments[f]&&(l[f]=void 0)}),l.length>1&&l.index<n.length&&o.apply(d,l.slice(1)),u=l[0].length,p=c,d.length>=g));)m.lastIndex===l.index&&m.lastIndex++;return p===n.length?!u&&m.test("")||d.push(""):d.push(n.slice(p)),d.length>g?d.slice(0,g):d}}else"0".split(void 0,0).length&&(n=function(e,t){return void 0===e&&0===t?[]:r.call(this,e,t)});return[function(i,r){var o=e(this),a=null==i?void 0:i[t];return void 0!==a?a.call(i,o,r):n.call(String(o),i,r)},n]}),Wi("search",1,function(e,t,n){return[function(n){var i=e(this),r=null==n?void 0:n[t];return void 0!==r?r.call(n,i):new RegExp(n)[t](String(i))},n]});var zi=function(e,t,n){t in e?p.f(e,t,g(0,n)):e[t]=n};C(C.S+C.F*!lt(function(e){}),"Array",{from:function(e){var t,n,i,r,o=le(e),a="function"==typeof this?this:Array,s=arguments.length,l=s>1?arguments[1]:void 0,c=void 0!==l,u=0,f=We(o);if(c&&(l=S(l,s>2?arguments[2]:void 0,2)),null==f||a==Array&&Le(f))for(n=new a(t=V(o.length));t>u;u++)zi(n,u,c?l(o[u],u):o[u]);else for(r=f.call(o),n=new a;!(i=r.next()).done;u++)zi(n,u,c?pt(r,l,[i.value,u],!0):i.value);return n.length=u,n}}),C(C.S+C.F*a(function(){function e(){}return!(Array.of.call(e)instanceof e)}),"Array",{of:function(){for(var e=0,t=arguments.length,n=new("function"==typeof this?this:Array)(t);t>e;)zi(n,e,arguments[e++]);return n.length=t,n}}),C(C.P,"Array",{copyWithin:ct}),$e("copyWithin");var Gi=Ke(5),Yi=!0;"find"in[]&&Array(1).find(function(){Yi=!1}),C(C.P+C.F*Yi,"Array",{find:function(e){return Gi(this,e,arguments.length>1?arguments[1]:void 0)}}),$e("find");var Ki=Ke(6),Qi=!0;"findIndex"in[]&&Array(1).findIndex(function(){Qi=!1}),C(C.P+C.F*Qi,"Array",{findIndex:function(e){return Ki(this,e,arguments.length>1?arguments[1]:void 0)}}),$e("findIndex"),C(C.P,"Array",{fill:ce}),$e("fill");var Ji=n.isFinite;C(C.S,"Number",{isFinite:function(e){return"number"==typeof e&&Ji(e)}});var $i=Math.floor,Xi=function(e){return!r(e)&&isFinite(e)&&$i(e)===e};C(C.S,"Number",{isInteger:Xi});var Zi=Math.abs;C(C.S,"Number",{isSafeInteger:function(e){return Xi(e)&&Zi(e)<=9007199254740991}}),C(C.S,"Number",{isNaN:function(e){return e!=e}}),C(C.S,"Number",{EPSILON:Math.pow(2,-52)}),C(C.S,"Number",{MIN_SAFE_INTEGER:-9007199254740991}),C(C.S,"Number",{MAX_SAFE_INTEGER:9007199254740991});var er=Math.log1p||function(e){return(e=+e)>-1e-8&&e<1e-8?e-e*e/2:Math.log(1+e)},tr=Math.sqrt,nr=Math.acosh;C(C.S+C.F*!(nr&&710==Math.floor(nr(Number.MAX_VALUE))&&nr(1/0)==1/0),"Math",{acosh:function(e){return(e=+e)<1?NaN:e>94906265.62425156?Math.log(e)+Math.LN2:er(e-1+tr(e-1)*tr(e+1))}});var ir=Math.asinh;C(C.S+C.F*!(ir&&1/ir(0)>0),"Math",{asinh:function e(t){return isFinite(t=+t)&&0!=t?t<0?-e(-t):Math.log(t+Math.sqrt(t*t+1)):t}});var rr=Math.atanh;C(C.S+C.F*!(rr&&1/rr(-0)<0),"Math",{atanh:function(e){return 0==(e=+e)?e:Math.log((1+e)/(1-e))/2}});var or=Math.sign||function(e){return 0==(e=+e)||e!=e?e:e<0?-1:1};C(C.S,"Math",{cbrt:function(e){return or(e=+e)*Math.pow(Math.abs(e),1/3)}}),C(C.S,"Math",{clz32:function(e){return(e>>>=0)?31-Math.floor(Math.log(e+.5)*Math.LOG2E):32}});var ar=Math.exp;C(C.S,"Math",{cosh:function(e){return(ar(e=+e)+ar(-e))/2}});var sr=Math.expm1,lr=!sr||sr(10)>22025.465794806718||sr(10)<22025.465794806718||-2e-17!=sr(-2e-17)?function(e){return 0==(e=+e)?e:e>-1e-6&&e<1e-6?e+e*e/2:Math.exp(e)-1}:sr;C(C.S+C.F*(lr!=Math.expm1),"Math",{expm1:lr});var cr=Math.pow,ur=cr(2,-52),fr=cr(2,-23),dr=cr(2,127)*(2-fr),hr=cr(2,-126),pr=Math.fround||function(e){var t,n,i=Math.abs(e),r=or(e);return i<hr?r*(i/hr/fr+1/ur-1/ur)*hr*fr:(n=(t=(1+fr/ur)*i)-(t-i))>dr||n!=n?r*(1/0):r*n};C(C.S,"Math",{fround:pr});var gr=Math.abs;C(C.S,"Math",{hypot:function(e,t){for(var n,i,r=0,o=0,a=arguments.length,s=0;o<a;)s<(n=gr(arguments[o++]))?(r=r*(i=s/n)*i+1,s=n):r+=n>0?(i=n/s)*i:n;return s===1/0?1/0:s*Math.sqrt(r)}});var mr=Math.imul;C(C.S+C.F*a(function(){return-5!=mr(4294967295,5)||2!=mr.length}),"Math",{imul:function(e,t){var n=+e,i=+t,r=65535&n,o=65535&i;return 0|r*o+((65535&n>>>16)*o+r*(65535&i>>>16)<<16>>>0)}}),C(C.S,"Math",{log1p:er}),C(C.S,"Math",{log10:function(e){return Math.log(e)*Math.LOG10E}}),C(C.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}}),C(C.S,"Math",{sign:or});var yr=Math.exp;C(C.S+C.F*a(function(){return-2e-17!=!Math.sinh(-2e-17)}),"Math",{sinh:function(e){return Math.abs(e=+e)<1?(lr(e)-lr(-e))/2:(yr(e-1)-yr(-e-1))*(Math.E/2)}});var vr=Math.exp;C(C.S,"Math",{tanh:function(e){var t=lr(e=+e),n=lr(-e);return t==1/0?1:n==1/0?-1:(t-n)/(vr(e)+vr(-e))}}),C(C.S,"Math",{trunc:function(e){return(e>0?Math.floor:Math.ceil)(e)}});var br=$(!0);C(C.P,"Array",{includes:function(e){return br(this,e,arguments.length>1?arguments[1]:void 0)}}),$e("includes");var wr=ut.f,Er=function(e){return function(t){for(var n,i=Y(t),r=Oe(i),o=r.length,a=0,s=[];o>a;)wr.call(i,n=r[a++])&&s.push(e?[n,i[n]]:i[n]);return s}},kr=Er(!1);C(C.S,"Object",{values:function(e){return kr(e)}});var Tr=Er(!0);C(C.S,"Object",{entries:function(e){return Tr(e)}}),C(C.S,"Object",{getOwnPropertyDescriptors:function(e){for(var t,n,i=Y(e),r=dt.f,o=Jt(i),a={},s=0;o.length>s;)void 0!==(n=r(i,t=o[s++]))&&zi(a,t,n);return a}});var Sr=function(e,t,n,i){var r=String(G(e)),o=r.length,a=void 0===n?" ":String(n),s=V(t);if(s<=o||""==a)return r;var l=s-o,c=Fi.call(a,Math.ceil(l/a.length));return c.length>l&&(c=c.slice(0,l)),i?c+r:r+c},Ar=n.navigator,_r=Ar&&Ar.userAgent||"";C(C.P+C.F*/Version\/10\.\d+(\.\d+)? Safari\//.test(_r),"String",{padStart:function(e){return Sr(this,e,arguments.length>1?arguments[1]:void 0,!0)}}),C(C.P+C.F*/Version\/10\.\d+(\.\d+)? Safari\//.test(_r),"String",{padEnd:function(e){return Sr(this,e,arguments.length>1?arguments[1]:void 0,!1)}});var Cr=[].slice,Pr=/MSIE .\./.test(_r),Mr=function(e){return function(t,n){var i=arguments.length>2,r=!!i&&Cr.call(arguments,2);return e(i?function(){("function"==typeof t?t:Function(t)).apply(this,r)}:t,n)}};C(C.G+C.B+C.F*Pr,{setTimeout:Mr(n.setTimeout),setInterval:Mr(n.setInterval)}),C(C.G+C.B,{setImmediate:fn.set,clearImmediate:fn.clear});for(var xr=ue("iterator"),Lr=ue("toStringTag"),Or=Pe.Array,Nr={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},jr=Oe(Nr),Ir=0;Ir<jr.length;Ir++){var Fr,Rr=jr[Ir],qr=Nr[Rr],Br=n[Rr],Ur=Br&&Br.prototype;if(Ur&&(Ur[xr]||m(Ur,xr,Or),Ur[Lr]||m(Ur,Lr,Rr),Pe[Rr]=Or,qr))for(Fr in ot)Ur[Fr]||k(Ur,Fr,ot[Fr],!0)}t(function(t){!function(e){var n,i=Object.prototype,r=i.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag",c=e.regeneratorRuntime;if(c)t.exports=c;else{(c=e.regeneratorRuntime=t.exports).wrap=b;var u="suspendedStart",f="suspendedYield",d="executing",h="completed",p={},g={};g[a]=function(){return this};var m=Object.getPrototypeOf,y=m&&m(m(x([])));y&&y!==i&&r.call(y,a)&&(g=y);var v=T.prototype=E.prototype=Object.create(g);k.prototype=v.constructor=T,T.constructor=k,T[l]=k.displayName="GeneratorFunction",c.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},c.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,T):(e.__proto__=T,l in e||(e[l]="GeneratorFunction")),e.prototype=Object.create(v),e},c.awrap=function(e){return{__await:e}},S(A.prototype),A.prototype[s]=function(){return this},c.AsyncIterator=A,c.async=function(e,t,n,i){var r=new A(b(e,t,n,i));return c.isGeneratorFunction(t)?r:r.next().then(function(e){return e.done?e.value:r.next()})},S(v),v[l]="Generator",v[a]=function(){return this},v.toString=function(){return"[object Generator]"},c.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var i=t.pop();if(i in e)return n.value=i,n.done=!1,n}return n.done=!0,n}},c.values=x,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(P),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(i,r){return s.type="throw",s.arg=e,t.next=i,r&&(t.method="next",t.arg=n),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var r=i.arg;P(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:x(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=n),p}}}function b(e,t,n,i){var r=t&&t.prototype instanceof E?t:E,o=Object.create(r.prototype),a=new M(i||[]);return o._invoke=function(e,t,n){var i=u;return function(r,o){if(i===d)throw new Error("Generator is already running");if(i===h){if("throw"===r)throw o;return L()}for(n.method=r,n.arg=o;;){var a=n.delegate;if(a){var s=_(a,n);if(s){if(s===p)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===u)throw i=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=d;var l=w(e,t,n);if("normal"===l.type){if(i=n.done?h:f,l.arg===p)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=h,n.method="throw",n.arg=l.arg)}}}(e,n,a),o}function w(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}function E(){}function k(){}function T(){}function S(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function A(t){function n(e,i,o,a){var s=w(t[e],t,i);if("throw"!==s.type){var l=s.arg,c=l.value;return c&&"object"==typeof c&&r.call(c,"__await")?Promise.resolve(c.__await).then(function(e){n("next",e,o,a)},function(e){n("throw",e,o,a)}):Promise.resolve(c).then(function(e){l.value=e,o(l)},a)}a(s.arg)}var i;"object"==typeof e.process&&e.process.domain&&(n=e.process.domain.bind(n)),this._invoke=function(e,t){function r(){return new Promise(function(i,r){n(e,t,i,r)})}return i=i?i.then(r,r):r()}}function _(e,t){var i=e.iterator[t.method];if(i===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,_(e,t),"throw"===t.method))return p;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=w(i,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,p;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,p):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function x(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function t(){for(;++i<e.length;)if(r.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=n,t.done=!0,t};return o.next=o}}return{next:L}}function L(){return{value:n,done:!0}}}("object"==typeof e?e:"object"==typeof window?window:"object"==typeof self?self:e)});try{var Vr=new window.CustomEvent("test");if(Vr.preventDefault(),!0!==Vr.defaultPrevented)throw new Error("Could not prevent default")}catch(e){var Dr=function(e,t){var n,i;return t=t||{bubbles:!1,cancelable:!1,detail:void 0},(n=document.createEvent("CustomEvent")).initCustomEvent(e,t.bubbles,t.cancelable,t.detail),i=n.preventDefault,n.preventDefault=function(){i.call(this);try{Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}catch(e){this.defaultPrevented=!0}},n};Dr.prototype=window.Event.prototype,window.CustomEvent=Dr}!function(e){var t=function(){try{return!!Symbol.iterator}catch(e){return!1}}(),n=function(e){var n={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return t&&(n[Symbol.iterator]=function(){return n}),n},i=function(e){return encodeURIComponent(e).replace(/%20/g,"+")},r=function(e){return decodeURIComponent(e).replace(/\+/g," ")};"URLSearchParams"in e&&"a=1"===new URLSearchParams("?a=1").toString()||function(){var o=function(e){if(Object.defineProperty(this,"_entries",{value:{}}),"string"==typeof e){if(""!==e)for(var t,n=(e=e.replace(/^\?/,"")).split("&"),i=0;i<n.length;i++)t=n[i].split("="),this.append(r(t[0]),t.length>1?r(t[1]):"")}else if(e instanceof o){var a=this;e.forEach(function(e,t){a.append(e,t)})}},a=o.prototype;a.append=function(e,t){e in this._entries?this._entries[e].push(t.toString()):this._entries[e]=[t.toString()]},a.delete=function(e){delete this._entries[e]},a.get=function(e){return e in this._entries?this._entries[e][0]:null},a.getAll=function(e){return e in this._entries?this._entries[e].slice(0):[]},a.has=function(e){return e in this._entries},a.set=function(e,t){this._entries[e]=[t.toString()]},a.forEach=function(e,t){var n;for(var i in this._entries)if(this._entries.hasOwnProperty(i)){n=this._entries[i];for(var r=0;r<n.length;r++)e.call(t,n[r],i,this)}},a.keys=function(){var e=[];return this.forEach(function(t,n){e.push(n)}),n(e)},a.values=function(){var e=[];return this.forEach(function(t){e.push(t)}),n(e)},a.entries=function(){var e=[];return this.forEach(function(t,n){e.push([n,t])}),n(e)},t&&(a[Symbol.iterator]=a.entries),a.toString=function(){var e="";return this.forEach(function(t,n){e.length>0&&(e+="&"),e+=i(n)+"="+i(t)}),e},e.URLSearchParams=o}()}(void 0!==e?e:"undefined"!=typeof window?window:"undefined"!=typeof self?self:e),function(e){if(function(){try{var e=new URL("b","http://a");return e.pathname="c%20d","http://a/c%20d"===e.href&&e.searchParams}catch(e){return!1}}()||function(){var t=e.URL,n=function(e,t){"string"!=typeof e&&(e=String(e));var n=document.implementation.createHTMLDocument("");if(window.doc=n,t){var i=n.createElement("base");i.href=t,n.head.appendChild(i)}var r=n.createElement("a");if(r.href=e,n.body.appendChild(r),r.href=r.href,":"===r.protocol||!/:/.test(r.href))throw new TypeError("Invalid URL");Object.defineProperty(this,"_anchorElement",{value:r})},i=n.prototype;["hash","host","hostname","port","protocol","search"].forEach(function(e){!function(e){Object.defineProperty(i,e,{get:function(){return this._anchorElement[e]},set:function(t){this._anchorElement[e]=t},enumerable:!0})}(e)}),Object.defineProperties(i,{toString:{get:function(){var e=this;return function(){return e.href}}},href:{get:function(){return this._anchorElement.href.replace(/\?$/,"")},set:function(e){this._anchorElement.href=e},enumerable:!0},pathname:{get:function(){return this._anchorElement.pathname.replace(/(^\/?)/,"/")},set:function(e){this._anchorElement.pathname=e},enumerable:!0},origin:{get:function(){var e={"http:":80,"https:":443,"ftp:":21}[this._anchorElement.protocol],t=this._anchorElement.port!=e&&""!==this._anchorElement.port;return this._anchorElement.protocol+"//"+this._anchorElement.hostname+(t?":"+this._anchorElement.port:"")},enumerable:!0},password:{get:function(){return""},set:function(e){},enumerable:!0},username:{get:function(){return""},set:function(e){},enumerable:!0},searchParams:{get:function(){var e=new URLSearchParams(this.search),t=this;return["append","delete","set"].forEach(function(n){var i=e[n];e[n]=function(){i.apply(e,arguments),t.search=e.toString()}}),e},enumerable:!0}}),n.createObjectURL=function(e){return t.createObjectURL.apply(t,arguments)},n.revokeObjectURL=function(e){return t.revokeObjectURL.apply(t,arguments)},e.URL=n}(),void 0!==e.location&&!("origin"in e.location)){var t=function(){return e.location.protocol+"//"+e.location.hostname+(e.location.port?":"+e.location.port:"")};try{Object.defineProperty(e.location,"origin",{get:t,enumerable:!0})}catch(n){setInterval(function(){e.location.origin=t()},100)}}}(void 0!==e?e:"undefined"!=typeof window?window:"undefined"!=typeof self?self:e);var Hr,Wr=t(function(e,t){var n;n=function(){var e=function(){},t={},n={},i={};function r(e,t){if(e){var r=i[e];if(n[e]=t,r)for(;r.length;)r[0](e,t),r.splice(0,1)}}function o(t,n){t.call&&(t={success:t}),n.length?(t.error||e)(n):(t.success||e)(t)}function a(t,n,i,r){var o,s,l=document,c=i.async,u=(i.numRetries||0)+1,f=i.before||e,d=t.replace(/^(css|img)!/,"");r=r||0,/(^css!|\.css$)/.test(t)?(o=!0,(s=l.createElement("link")).rel="stylesheet",s.href=d):/(^img!|\.(png|gif|jpg|svg)$)/.test(t)?(s=l.createElement("img")).src=d:((s=l.createElement("script")).src=t,s.async=void 0===c||c),s.onload=s.onerror=s.onbeforeload=function(e){var l=e.type[0];if(o&&"hideFocus"in s)try{s.sheet.cssText.length||(l="e")}catch(e){l="e"}if("e"==l&&(r+=1)<u)return a(t,n,i,r);n(t,l,e.defaultPrevented)},!1!==f(t,s)&&l.head.appendChild(s)}function s(e,n,i){var s,l;if(n&&n.trim&&(s=n),l=(s?i:n)||{},s){if(s in t)throw"LoadJS";t[s]=!0}!function(e,t,n){var i,r,o=(e=e.push?e:[e]).length,s=o,l=[];for(i=function(e,n,i){if("e"==n&&l.push(e),"b"==n){if(!i)return;l.push(e)}--o||t(l)},r=0;r<s;r++)a(e[r],i,n)}(e,function(e){o(l,e),r(s,e)},l)}return s.ready=function(e,t){return function(e,t){e=e.push?e:[e];var r,o,a,s=[],l=e.length,c=l;for(r=function(e,n){n.length&&s.push(e),--c||t(s)};l--;)o=e[l],(a=n[o])?r(o,a):(i[o]=i[o]||[]).push(r)}(e,function(e){o(t,e)}),s},s.done=function(e){r(e,[])},s.reset=function(){t={},n={},i={}},s.isDefined=function(e){return e in t},s},e.exports=n()}),zr=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Gr=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),Yr=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},Kr=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],i=!0,r=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(i=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);i=!0);}catch(e){r=!0,o=e}finally{try{!i&&s.return&&s.return()}finally{if(r)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),Qr=function(){function e(t){zr(this,e),this.enabled=t.config.storage.enabled,this.key=t.config.storage.key}return Gr(e,[{key:"get",value:function(t){if(!e.supported||!this.enabled)return null;var n=window.localStorage.getItem(this.key);if(Xr.is.empty(n))return null;var i=JSON.parse(n);return Xr.is.string(t)&&t.length?i[t]:i}},{key:"set",value:function(t){if(e.supported&&this.enabled&&Xr.is.object(t)){var n=this.get();Xr.is.empty(n)&&(n={}),Xr.extend(n,t),window.localStorage.setItem(this.key,JSON.stringify(n))}}}],[{key:"supported",get:function(){try{if(!("localStorage"in window))return!1;return window.localStorage.setItem("___test","___test"),window.localStorage.removeItem("___test"),!0}catch(e){return!1}}}]),e}(),Jr={html5:"html5",youtube:"youtube",vimeo:"vimeo"},$r={audio:"audio",video:"video"},Xr={is:{object:function(e){return Xr.getConstructor(e)===Object},number:function(e){return Xr.getConstructor(e)===Number&&!Number.isNaN(e)},string:function(e){return Xr.getConstructor(e)===String},boolean:function(e){return Xr.getConstructor(e)===Boolean},function:function(e){return Xr.getConstructor(e)===Function},array:function(e){return!Xr.is.nullOrUndefined(e)&&Array.isArray(e)},weakMap:function(e){return Xr.is.instanceof(e,WeakMap)},nodeList:function(e){return Xr.is.instanceof(e,NodeList)},element:function(e){return Xr.is.instanceof(e,Element)},textNode:function(e){return Xr.getConstructor(e)===Text},event:function(e){return Xr.is.instanceof(e,Event)},cue:function(e){return Xr.is.instanceof(e,window.TextTrackCue)||Xr.is.instanceof(e,window.VTTCue)},track:function(e){return Xr.is.instanceof(e,TextTrack)||!Xr.is.nullOrUndefined(e)&&Xr.is.string(e.kind)},url:function(e){return!Xr.is.nullOrUndefined(e)&&/(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-/]))?/.test(e)},nullOrUndefined:function(e){return null==e},empty:function(e){return Xr.is.nullOrUndefined(e)||(Xr.is.string(e)||Xr.is.array(e)||Xr.is.nodeList(e))&&!e.length||Xr.is.object(e)&&!Object.keys(e).length},instanceof:function(e,t){return Boolean(e&&t&&e instanceof t)}},getConstructor:function(e){return Xr.is.nullOrUndefined(e)?null:e.constructor},getBrowser:function(){return{isIE:!!document.documentMode,isWebkit:"WebkitAppearance"in document.documentElement.style&&!/Edge/.test(navigator.userAgent),isIPhone:/(iPhone|iPod)/gi.test(navigator.platform),isIos:/(iPad|iPhone|iPod)/gi.test(navigator.platform)}},fetch:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"text";return new Promise(function(n,i){try{var r=new XMLHttpRequest;if(!("withCredentials"in r))return;r.addEventListener("load",function(){if("text"===t)try{n(JSON.parse(r.responseText))}catch(e){n(r.responseText)}else n(r.response)}),r.addEventListener("error",function(){throw new Error(r.statusText)}),r.open("GET",e,!0),r.responseType=t,r.send()}catch(e){i(e)}})},loadImage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return new Promise(function(n,i){var r=new Image,o=function(){delete r.onload,delete r.onerror,(r.naturalWidth>=t?n:i)(r)};Object.assign(r,{onload:o,onerror:o,src:e})})},loadScript:function(e){return new Promise(function(t,n){Wr(e,{success:t,error:n})})},loadSprite:function(e,t){if(Xr.is.string(e)){var n=Xr.is.string(t),i=function(){return null!==document.getElementById(t)},r=function(e,t){e.innerHTML=t,n&&i()||document.body.insertAdjacentElement("afterbegin",e)};if(!n||!i()){var o=Qr.supported,a=document.createElement("div");if(Xr.toggleHidden(a,!0),n&&a.setAttribute("id",t),o){var s=window.localStorage.getItem("cache-"+t);if(null!==s){var l=JSON.parse(s);r(a,l.content)}}Xr.fetch(e).then(function(e){Xr.is.empty(e)||(o&&window.localStorage.setItem("cache-"+t,JSON.stringify({content:e})),r(a,e))}).catch(function(){})}}},generateId:function(e){return e+"-"+Math.floor(1e4*Math.random())},wrap:function(e,t){var n=e.length?e:[e];Array.from(n).reverse().forEach(function(e,n){var i=n>0?t.cloneNode(!0):t,r=e.parentNode,o=e.nextSibling;i.appendChild(e),o?r.insertBefore(i,o):r.appendChild(i)})},createElement:function(e,t,n){var i=document.createElement(e);return Xr.is.object(t)&&Xr.setAttributes(i,t),Xr.is.string(n)&&(i.innerText=n),i},insertAfter:function(e,t){t.parentNode.insertBefore(e,t.nextSibling)},insertElement:function(e,t,n,i){t.appendChild(Xr.createElement(e,n,i))},removeElement:function(e){Xr.is.nodeList(e)||Xr.is.array(e)?Array.from(e).forEach(Xr.removeElement):Xr.is.element(e)&&Xr.is.element(e.parentNode)&&e.parentNode.removeChild(e)},emptyElement:function(e){for(var t=e.childNodes.length;t>0;)e.removeChild(e.lastChild),t-=1},replaceElement:function(e,t){return Xr.is.element(t)&&Xr.is.element(t.parentNode)&&Xr.is.element(e)?(t.parentNode.replaceChild(e,t),e):null},setAttributes:function(e,t){Xr.is.element(e)&&!Xr.is.empty(t)&&Object.entries(t).forEach(function(t){var n=Kr(t,2),i=n[0],r=n[1];e.setAttribute(i,r)})},getAttributesFromSelector:function(e,t){if(!Xr.is.string(e)||Xr.is.empty(e))return{};var n={},i=t;return e.split(",").forEach(function(e){var t=e.trim(),r=t.replace(".",""),o=t.replace(/[[\]]/g,"").split("="),a=o[0],s=o.length>1?o[1].replace(/["']/g,""):"";switch(t.charAt(0)){case".":Xr.is.object(i)&&Xr.is.string(i.class)&&(i.class+=" "+r),n.class=r;break;case"#":n.id=t.replace("#","");break;case"[":n[a]=s}}),n},toggleHidden:function(e,t){if(Xr.is.element(e)){var n=t;Xr.is.boolean(n)||(n=!e.hasAttribute("hidden")),n?e.setAttribute("hidden",""):e.removeAttribute("hidden")}},toggleClass:function(e,t,n){if(Xr.is.element(e)){var i="toggle";return void 0!==n&&(i=n?"add":"remove"),e.classList[i](t),e.classList.contains(t)}return null},hasClass:function(e,t){return Xr.is.element(e)&&e.classList.contains(t)},matches:function(e,t){var n={Element:Element};var i=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.msMatchesSelector||function(){return Array.from(document.querySelectorAll(t)).includes(this)};return i.call(e,t)},getElements:function(e){return this.elements.container.querySelectorAll(e)},getElement:function(e){return this.elements.container.querySelector(e)},getFocusElement:function(){var e=document.activeElement;return e=e&&e!==document.body?document.querySelector(":focus"):null},trapFocus:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(Xr.is.element(e)){var n=Xr.getElements.call(this,"button:not(:disabled), input:not(:disabled), [tabindex]"),i=n[0],r=n[n.length-1],o=function(e){if("Tab"===e.key&&9===e.keyCode){var t=Xr.getFocusElement();t!==r||e.shiftKey?t===i&&e.shiftKey&&(r.focus(),e.preventDefault()):(i.focus(),e.preventDefault())}};t?Xr.on(this.elements.container,"keydown",o,!1):Xr.off(this.elements.container,"keydown",o,!1)}},toggleListener:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],o=arguments.length>5&&void 0!==arguments[5]&&arguments[5];if(!Xr.is.empty(e)&&!Xr.is.empty(t)&&Xr.is.function(n))if(Xr.is.nodeList(e)||Xr.is.array(e))Array.from(e).forEach(function(e){e instanceof Node&&Xr.toggleListener.call(null,e,t,n,i,r,o)});else{var a=t.split(" "),s=o;Zr.passiveListeners&&(s={passive:r,capture:o}),a.forEach(function(t){e[i?"addEventListener":"removeEventListener"](t,n,s)})}},on:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments[2],i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];Xr.toggleListener(e,t,n,!0,i,r)},off:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments[2],i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];Xr.toggleListener(e,t,n,!1,i,r)},dispatchEvent:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(Xr.is.element(e)&&!Xr.is.empty(t)){var r=new CustomEvent(t,{bubbles:n,detail:Object.assign({},i,{plyr:this})});e.dispatchEvent(r)}},toggleState:function(e,t){if(Xr.is.array(e)||Xr.is.nodeList(e))Array.from(e).forEach(function(e){return Xr.toggleState(e,t)});else if(Xr.is.element(e)){var n="true"===e.getAttribute("aria-pressed"),i=Xr.is.boolean(t)?t:!n;e.setAttribute("aria-pressed",i)}},format:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return Xr.is.empty(e)?e:e.toString().replace(/{(\d+)}/g,function(e,t){return Xr.is.string(n[t])?n[t]:""})},getPercentage:function(e,t){return 0===e||0===t||Number.isNaN(e)||Number.isNaN(t)?0:(e/t*100).toFixed(2)},getHours:function(e){return parseInt(e/60/60%60,10)},getMinutes:function(e){return parseInt(e/60%60,10)},getSeconds:function(e){return parseInt(e%60,10)},formatTime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!Xr.is.number(e))return Xr.formatTime(null,t,n);var i=function(e){return("0"+e).slice(-2)},r=Xr.getHours(e),o=Xr.getMinutes(e),a=Xr.getSeconds(e);return t||r>0?r+=":":r="",(n?"-":"")+r+i(o)+":"+i(a)},replaceAll:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return e.replace(new RegExp(t.toString().replace(/([.*+?^=!:${}()|[\]/\\])/g,"\\$1"),"g"),n.toString())},toTitleCase:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toString().replace(/\w\S*/g,function(e){return e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()})},toPascalCase:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toString();return e=Xr.replaceAll(e,"-"," "),e=Xr.replaceAll(e,"_"," "),e=Xr.toTitleCase(e),Xr.replaceAll(e," ","")},toCamelCase:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toString();return(e=Xr.toPascalCase(e)).charAt(0).toLowerCase()+e.slice(1)},extend:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];if(!n.length)return e;var r=n.shift();return Xr.is.object(r)?(Object.keys(r).forEach(function(t){Xr.is.object(r[t])?(Object.keys(e).includes(t)||Object.assign(e,Yr({},t,{})),Xr.extend(e[t],r[t])):Object.assign(e,Yr({},t,r[t]))}),Xr.extend.apply(Xr,[e].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(n)))):e},dedupe:function(e){return Xr.is.array(e)?e.filter(function(t,n){return e.indexOf(t)===n}):e},cloneDeep:function(e){return JSON.parse(JSON.stringify(e))},getDeep:function(e,t){return t.split(".").reduce(function(e,t){return e&&e[t]},e)},closest:function(e,t){return Xr.is.array(e)&&e.length?e.reduce(function(e,n){return Math.abs(n-t)<Math.abs(e-t)?n:e}):null},getProviderByUrl:function(e){return/^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/.test(e)?Jr.youtube:/^https?:\/\/player.vimeo.com\/video\/\d{0,9}(?=\b|\/)/.test(e)?Jr.vimeo:null},parseYouTubeId:function(e){if(Xr.is.empty(e))return null;return e.match(/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/)?RegExp.$2:e},parseVimeoId:function(e){if(Xr.is.empty(e))return null;if(Xr.is.number(Number(e)))return e;return e.match(/^.*(vimeo.com\/|video\/)(\d+).*/)?RegExp.$2:e},parseUrl:function(e){var t=document.createElement("a");return t.href=e,t},getUrlParams:function(e){var t=e;(e.startsWith("http://")||e.startsWith("https://"))&&(t=Xr.parseUrl(e).search);return Xr.is.empty(t)?null:t.slice(t.indexOf("?")+1).split("&").reduce(function(e,t){var n=t.split("="),i=Kr(n,2),r=i[0],o=i[1];return Object.assign(e,Yr({},r,decodeURIComponent(o)))},{})},buildUrlParams:function(e){return Xr.is.object(e)?Object.keys(e).map(function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])}).join("&"):""},stripHTML:function(e){var t=document.createDocumentFragment(),n=document.createElement("div");return t.appendChild(n),n.innerHTML=e,t.firstChild.innerText},getHTML:function(e){var t=document.createElement("div");return t.appendChild(e),t.innerHTML},getAspectRatio:function(e,t){var n=function e(t,n){return 0===n?t:e(n,t%n)}(e,t);return e/n+":"+t/n},get transitionEndEvent(){var e=document.createElement("span"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"},n=Object.keys(t).find(function(t){return void 0!==e.style[t]});return!!Xr.is.string(n)&&t[n]},repaint:function(e){setTimeout(function(){Xr.toggleHidden(e,!0),e.offsetHeight,Xr.toggleHidden(e,!1)},0)}},Zr={audio:"canPlayType"in document.createElement("audio"),video:"canPlayType"in document.createElement("video"),check:function(e,t,n){var i=!1,r=!1,o=Xr.getBrowser(),a=o.isIPhone&&n&&Zr.playsinline;switch(t+":"+e){case"html5:video":r=(i=Zr.video)&&Zr.rangeInput&&(!o.isIPhone||a);break;case"html5:audio":r=(i=Zr.audio)&&Zr.rangeInput;break;case"youtube:video":case"vimeo:video":i=!0,r=Zr.rangeInput&&(!o.isIPhone||a);break;default:r=(i=Zr.audio&&Zr.video)&&Zr.rangeInput}return{api:i,ui:r}},pip:!Xr.getBrowser().isIPhone&&Xr.is.function(Xr.createElement("video").webkitSetPresentationMode),airplay:Xr.is.function(window.WebKitPlaybackTargetAvailabilityEvent),playsinline:"playsInline"in document.createElement("video"),mime:function(e){var t=this.media;try{if(!this.isHTML5||!Xr.is.function(t.canPlayType))return!1;if(e.includes("codecs="))return t.canPlayType(e).replace(/no/,"");if(this.isVideo)switch(e){case"video/webm":return t.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/no/,"");case"video/mp4":return t.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"').replace(/no/,"");case"video/ogg":return t.canPlayType('video/ogg; codecs="theora"').replace(/no/,"");default:return!1}else if(this.isAudio)switch(e){case"audio/mpeg":return t.canPlayType("audio/mpeg;").replace(/no/,"");case"audio/ogg":return t.canPlayType('audio/ogg; codecs="vorbis"').replace(/no/,"");case"audio/wav":return t.canPlayType('audio/wav; codecs="1"').replace(/no/,"");default:return!1}}catch(e){return!1}return!1},textTracks:"textTracks"in document.createElement("video"),passiveListeners:function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){return e=!0,null}});window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}return e}(),rangeInput:(Hr=document.createElement("input"),Hr.type="range","range"===Hr.type),touch:"ontouchstart"in document.documentElement,transitions:!1!==Xr.transitionEndEvent,reducedMotion:"matchMedia"in window&&window.matchMedia("(prefers-reduced-motion)").matches},eo={getSources:function(){return this.isHTML5?this.media.querySelectorAll("source"):null},getQualityOptions:function(){if(!this.isHTML5)return null;var e=eo.getSources.call(this);if(Xr.is.empty(e))return null;var t=Array.from(e).filter(function(e){return!Xr.is.empty(e.getAttribute("size"))});return Xr.is.empty(t)?null:Xr.dedupe(t.map(function(e){return Number(e.getAttribute("size"))}))},extend:function(){if(this.isHTML5){var e=this;Object.defineProperty(e.media,"quality",{get:function(){var t=eo.getSources.call(e);if(Xr.is.empty(t))return null;var n=Array.from(t).filter(function(t){return t.getAttribute("src")===e.source});return Xr.is.empty(n)?null:Number(n[0].getAttribute("size"))},set:function(t){var n=eo.getSources.call(e);if(!Xr.is.empty(n)){var i=Array.from(n).filter(function(e){return Number(e.getAttribute("size"))===t});if(!Xr.is.empty(i)){var r=i.filter(function(t){return Zr.mime.call(e,t.getAttribute("type"))});if(!Xr.is.empty(r)){Xr.dispatchEvent.call(e,e.media,"qualityrequested",!1,{quality:t});var o=e.currentTime,a=e.playing;e.media.src=r[0].getAttribute("src");e.on("loadedmetadata",function t(){e.currentTime=o,e.off("loadedmetadata",t)}),e.media.load(),a&&e.play(),Xr.dispatchEvent.call(e,e.media,"qualitychange",!1,{quality:t})}}}}})}},cancelRequests:function(){this.isHTML5&&(Xr.removeElement(eo.getSources()),this.media.setAttribute("src",this.config.blankVideo),this.media.load(),this.debug.log("Cancelled network requests"))}},to=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Xr.is.empty(e)||Xr.is.empty(t))return"";var n=Xr.getDeep(t.i18n,e);if(Xr.is.empty(n))return"";var i={"{seektime}":t.seekTime,"{title}":t.title};return Object.entries(i).forEach(function(e){var t=Kr(e,2),i=t[0],r=t[1];n=Xr.replaceAll(n,i,r)}),n},no=Xr.getBrowser(),io={getIconUrl:function(){var e=new URL(this.config.iconUrl,window.location).host!==window.location.host||no.isIE&&!window.svg4everybody;return{url:this.config.iconUrl,cors:e}},findElements:function(){try{return this.elements.controls=Xr.getElement.call(this,this.config.selectors.controls.wrapper),this.elements.buttons={play:Xr.getElements.call(this,this.config.selectors.buttons.play),pause:Xr.getElement.call(this,this.config.selectors.buttons.pause),restart:Xr.getElement.call(this,this.config.selectors.buttons.restart),rewind:Xr.getElement.call(this,this.config.selectors.buttons.rewind),fastForward:Xr.getElement.call(this,this.config.selectors.buttons.fastForward),mute:Xr.getElement.call(this,this.config.selectors.buttons.mute),pip:Xr.getElement.call(this,this.config.selectors.buttons.pip),airplay:Xr.getElement.call(this,this.config.selectors.buttons.airplay),settings:Xr.getElement.call(this,this.config.selectors.buttons.settings),captions:Xr.getElement.call(this,this.config.selectors.buttons.captions),fullscreen:Xr.getElement.call(this,this.config.selectors.buttons.fullscreen)},this.elements.progress=Xr.getElement.call(this,this.config.selectors.progress),this.elements.inputs={seek:Xr.getElement.call(this,this.config.selectors.inputs.seek),volume:Xr.getElement.call(this,this.config.selectors.inputs.volume)},this.elements.display={buffer:Xr.getElement.call(this,this.config.selectors.display.buffer),currentTime:Xr.getElement.call(this,this.config.selectors.display.currentTime),duration:Xr.getElement.call(this,this.config.selectors.display.duration)},Xr.is.element(this.elements.progress)&&(this.elements.display.seekTooltip=this.elements.progress.querySelector("."+this.config.classNames.tooltip)),!0}catch(e){return this.debug.warn("It looks like there is a problem with your custom controls HTML",e),this.toggleNativeControls(!0),!1}},createIcon:function(e,t){var n=io.getIconUrl.call(this),i=(n.cors?"":n.url)+"#"+this.config.iconPrefix,r=document.createElementNS("http://www.w3.org/2000/svg","svg");Xr.setAttributes(r,Xr.extend(t,{role:"presentation",focusable:"false"}));var o=document.createElementNS("http://www.w3.org/2000/svg","use"),a=i+"-"+e;return"href"in o?o.setAttributeNS("http://www.w3.org/1999/xlink","href",a):o.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a),r.appendChild(o),r},createLabel:function(e,t){var n=to(e,this.config),i=Object.assign({},t);switch(e){case"pip":n="PIP";break;case"airplay":n="AirPlay"}return"class"in i?i.class+=" "+this.config.classNames.hidden:i.class=this.config.classNames.hidden,Xr.createElement("span",i,n)},createBadge:function(e){if(Xr.is.empty(e))return null;var t=Xr.createElement("span",{class:this.config.classNames.menu.value});return t.appendChild(Xr.createElement("span",{class:this.config.classNames.menu.badge},e)),t},createButton:function(e,t){var n=Xr.createElement("button"),i=Object.assign({},t),r=Xr.toCamelCase(e),o=!1,a=void 0,s=void 0,l=void 0,c=void 0;switch("type"in i||(i.type="button"),"class"in i?i.class.includes(this.config.classNames.control)&&(i.class+=" "+this.config.classNames.control):i.class=this.config.classNames.control,e){case"play":o=!0,a="play",l="pause",s="play",c="pause";break;case"mute":o=!0,a="mute",l="unmute",s="volume",c="muted";break;case"captions":o=!0,a="enableCaptions",l="disableCaptions",s="captions-off",c="captions-on";break;case"fullscreen":o=!0,a="enterFullscreen",l="exitFullscreen",s="enter-fullscreen",c="exit-fullscreen";break;case"play-large":i.class+=" "+this.config.classNames.control+"--overlaid",r="play",a="play",s="play";break;default:a=r,s=e}return o?(n.appendChild(io.createIcon.call(this,c,{class:"icon--pressed"})),n.appendChild(io.createIcon.call(this,s,{class:"icon--not-pressed"})),n.appendChild(io.createLabel.call(this,l,{class:"label--pressed"})),n.appendChild(io.createLabel.call(this,a,{class:"label--not-pressed"})),i["aria-pressed"]=!1):(n.appendChild(io.createIcon.call(this,s)),n.appendChild(io.createLabel.call(this,a))),Xr.extend(i,Xr.getAttributesFromSelector(this.config.selectors.buttons[r],i)),Xr.setAttributes(n,i),"play"===r?(Xr.is.array(this.elements.buttons[r])||(this.elements.buttons[r]=[]),this.elements.buttons[r].push(n)):this.elements.buttons[r]=n,n},createRange:function(e,t){var n=Xr.createElement("label",{for:t.id,id:t.id+"-label",class:this.config.classNames.hidden},to(e,this.config)),i=Xr.createElement("input",Xr.extend(Xr.getAttributesFromSelector(this.config.selectors.inputs[e]),{type:"range",min:0,max:100,step:.01,value:0,autocomplete:"off",role:"slider","aria-labelledby":t.id+"-label","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":0},t));return this.elements.inputs[e]=i,io.updateRangeFill.call(this,i),{label:n,input:i}},createProgress:function(e,t){var n=Xr.createElement("progress",Xr.extend(Xr.getAttributesFromSelector(this.config.selectors.display[e]),{min:0,max:100,value:0,role:"presentation","aria-hidden":!0},t));if("volume"!==e){n.appendChild(Xr.createElement("span",null,"0"));var i="";switch(e){case"played":i=to("played",this.config);break;case"buffer":i=to("buffered",this.config)}n.innerText="% "+i.toLowerCase()}return this.elements.display[e]=n,n},createTime:function(e){var t=Xr.getAttributesFromSelector(this.config.selectors.display[e]),n=Xr.createElement("div",Xr.extend(t,{class:"plyr__time "+t.class,"aria-label":to(e,this.config)}),"00:00");return this.elements.display[e]=n,n},createMenuItem:function(e){var t=e.value,n=e.list,i=e.type,r=e.title,o=e.badge,a=void 0===o?null:o,s=e.checked,l=void 0!==s&&s,c=Xr.createElement("li"),u=Xr.createElement("label",{class:this.config.classNames.control}),f=Xr.createElement("input",Xr.extend(Xr.getAttributesFromSelector(this.config.selectors.inputs[i]),{type:"radio",name:"plyr-"+i,value:t,checked:l,class:"plyr__sr-only"})),d=Xr.createElement("span",{hidden:""});u.appendChild(f),u.appendChild(d),u.insertAdjacentHTML("beforeend",r),Xr.is.element(a)&&u.appendChild(a),c.appendChild(u),n.appendChild(c)},updateTimeDisplay:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(Xr.is.element(e)&&Xr.is.number(t)){var i=Xr.getHours(this.duration)>0;e.innerText=Xr.formatTime(t,i,n)}},updateVolume:function(){this.supported.ui&&(Xr.is.element(this.elements.inputs.volume)&&io.setRange.call(this,this.elements.inputs.volume,this.muted?0:this.volume),Xr.is.element(this.elements.buttons.mute)&&Xr.toggleState(this.elements.buttons.mute,this.muted||0===this.volume))},setRange:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Xr.is.element(e)&&(e.value=t,io.updateRangeFill.call(this,e))},updateProgress:function(e){var t=this;if(this.supported.ui&&Xr.is.event(e)){var n=0;if(e)switch(e.type){case"timeupdate":case"seeking":case"seeked":n=Xr.getPercentage(this.currentTime,this.duration),"timeupdate"===e.type&&io.setRange.call(this,this.elements.inputs.seek,n);break;case"playing":case"progress":!function(e,n){var i=Xr.is.number(n)?n:0,r=Xr.is.element(e)?e:t.elements.display.buffer;if(Xr.is.element(r)){r.value=i;var o=r.getElementsByTagName("span")[0];Xr.is.element(o)&&(o.childNodes[0].nodeValue=i)}}(this.elements.display.buffer,100*this.buffered)}}},updateRangeFill:function(e){var t=Xr.is.event(e)?e.target:e;Xr.is.element(t)&&"range"===t.getAttribute("type")&&(t.setAttribute("aria-valuenow",t.value),no.isWebkit&&t.style.setProperty("--value",t.value/t.max*100+"%"))},updateSeekTooltip:function(e){var t=this;if(this.config.tooltips.seek&&Xr.is.element(this.elements.inputs.seek)&&Xr.is.element(this.elements.display.seekTooltip)&&0!==this.duration){var n=0,i=this.elements.progress.getBoundingClientRect(),r=this.config.classNames.tooltip+"--visible",o=function(e){Xr.toggleClass(t.elements.display.seekTooltip,r,e)};if(this.touch)o(!1);else{if(Xr.is.event(e))n=100/i.width*(e.pageX-i.left);else{if(!Xr.hasClass(this.elements.display.seekTooltip,r))return;n=parseFloat(this.elements.display.seekTooltip.style.left,10)}n<0?n=0:n>100&&(n=100),io.updateTimeDisplay.call(this,this.elements.display.seekTooltip,this.duration/100*n),this.elements.display.seekTooltip.style.left=n+"%",Xr.is.event(e)&&["mouseenter","mouseleave"].includes(e.type)&&o("mouseenter"===e.type)}}},timeUpdate:function(e){var t=!Xr.is.element(this.elements.display.duration)&&this.config.invertTime;io.updateTimeDisplay.call(this,this.elements.display.currentTime,t?this.duration-this.currentTime:this.currentTime,t),e&&"timeupdate"===e.type&&this.media.seeking||io.updateProgress.call(this,e)},durationUpdate:function(){if(this.supported.ui&&(this.config.invertTime||!this.currentTime)){var e=Xr.is.element(this.elements.display.duration);!e&&this.config.displayDuration&&this.paused&&io.updateTimeDisplay.call(this,this.elements.display.currentTime,this.duration),e&&io.updateTimeDisplay.call(this,this.elements.display.duration,this.duration),io.updateSeekTooltip.call(this)}},toggleTab:function(e,t){Xr.toggleHidden(this.elements.settings.tabs[e],!t)},setQualityMenu:function(e){var t=this;if(Xr.is.element(this.elements.settings.panes.quality)){var n=this.elements.settings.panes.quality.querySelector("ul");Xr.is.array(e)&&(this.options.quality=e.filter(function(e){return t.config.quality.options.includes(e)}));var i=!Xr.is.empty(this.options.quality)&&this.options.quality.length>1;if(io.toggleTab.call(this,"quality",i),io.checkMenu.call(this),i){Xr.emptyElement(n);this.options.quality.sort(function(e,n){var i=t.config.quality.options;return i.indexOf(e)>i.indexOf(n)?1:-1}).forEach(function(e){io.createMenuItem.call(t,{value:e,list:n,type:"quality",title:io.getLabel.call(t,"quality",e),badge:function(e){var n=to("qualityBadge."+e,t.config);return n.length?io.createBadge.call(t,n):null}(e)})}),io.updateSetting.call(this,"quality",n)}}},getLabel:function(e,t){switch(e){case"speed":return 1===t?to("normal",this.config):t+"&times;";case"quality":if(Xr.is.number(t)){var n=to("qualityLabel."+t,this.config);return n.length?n:t+"p"}return Xr.toTitleCase(t);case"captions":return ro.getLabel.call(this);default:return null}},updateSetting:function(e,t,n){var i=this.elements.settings.panes[e],r=null,o=t;switch(e){case"captions":r=this.currentTrack;break;default:if(r=Xr.is.empty(n)?this[e]:n,Xr.is.empty(r)&&(r=this.config[e].default),!Xr.is.empty(this.options[e])&&!this.options[e].includes(r))return void this.debug.warn("Unsupported value of '"+r+"' for "+e);if(!this.config[e].options.includes(r))return void this.debug.warn("Disabled value of '"+r+"' for "+e)}if(Xr.is.element(o)||(o=i&&i.querySelector("ul")),Xr.is.element(o)){this.elements.settings.tabs[e].querySelector("."+this.config.classNames.menu.value).innerHTML=io.getLabel.call(this,e,r);var a=o&&o.querySelector('input[value="'+r+'"]');Xr.is.element(a)&&(a.checked=!0)}},setCaptionsMenu:function(){var e=this,t=this.elements.settings.panes.captions.querySelector("ul"),n=ro.getTracks.call(this);if(io.toggleTab.call(this,"captions",n.length),Xr.emptyElement(t),io.checkMenu.call(this),n.length){var i=n.map(function(n,i){return{value:i,checked:e.captions.active&&e.currentTrack===i,title:ro.getLabel.call(e,n),badge:n.language&&io.createBadge.call(e,n.language.toUpperCase()),list:t,type:"language"}});i.unshift({value:-1,checked:!this.captions.active,title:to("disabled",this.config),list:t,type:"language"}),i.forEach(io.createMenuItem.bind(this)),io.updateSetting.call(this,"captions",t)}},setSpeedMenu:function(e){var t=this;if(this.config.controls.includes("settings")&&this.config.settings.includes("speed")&&Xr.is.element(this.elements.settings.panes.speed)){Xr.is.array(e)?this.options.speed=e:(this.isHTML5||this.isVimeo)&&(this.options.speed=[.5,.75,1,1.25,1.5,1.75,2]),this.options.speed=this.options.speed.filter(function(e){return t.config.speed.options.includes(e)});var n=!Xr.is.empty(this.options.speed)&&this.options.speed.length>1;if(io.toggleTab.call(this,"speed",n),io.checkMenu.call(this),n){var i=this.elements.settings.panes.speed.querySelector("ul");Xr.emptyElement(i),this.options.speed.forEach(function(e){io.createMenuItem.call(t,{value:e,list:i,type:"speed",title:io.getLabel.call(t,"speed",e)})}),io.updateSetting.call(this,"speed",i)}}},checkMenu:function(){var e=this.elements.settings.tabs,t=!Xr.is.empty(e)&&Object.values(e).some(function(e){return!e.hidden});Xr.toggleHidden(this.elements.settings.menu,!t)},toggleMenu:function(e){var t=this.elements.settings.form,n=this.elements.buttons.settings;if(Xr.is.element(t)&&Xr.is.element(n)){var i=Xr.is.boolean(e)?e:Xr.is.element(t)&&t.hasAttribute("hidden");if(Xr.is.event(e)){var r=Xr.is.element(t)&&t.contains(e.target),o=e.target===this.elements.buttons.settings;if(r||!r&&!o&&i)return;o&&e.stopPropagation()}Xr.is.element(n)&&n.setAttribute("aria-expanded",i),Xr.is.element(t)&&(Xr.toggleHidden(t,!i),Xr.toggleClass(this.elements.container,this.config.classNames.menu.open,i),i?t.removeAttribute("tabindex"):t.setAttribute("tabindex",-1))}},getTabSize:function(e){var t=e.cloneNode(!0);t.style.position="absolute",t.style.opacity=0,t.removeAttribute("hidden"),Array.from(t.querySelectorAll("input[name]")).forEach(function(e){var t=e.getAttribute("name");e.setAttribute("name",t+"-clone")}),e.parentNode.appendChild(t);var n=t.scrollWidth,i=t.scrollHeight;return Xr.removeElement(t),{width:n,height:i}},showTab:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.elements.settings.menu,n=document.getElementById(e);if(Xr.is.element(n)&&"tabpanel"===n.getAttribute("role")){var i=t.querySelector('[role="tabpanel"]:not([hidden])'),r=i.parentNode;if(Array.from(t.querySelectorAll('[aria-controls="'+i.getAttribute("id")+'"]')).forEach(function(e){e.setAttribute("aria-expanded",!1)}),Zr.transitions&&!Zr.reducedMotion){r.style.width=i.scrollWidth+"px",r.style.height=i.scrollHeight+"px";var o=io.getTabSize.call(this,n);Xr.on(r,Xr.transitionEndEvent,function e(t){t.target===r&&["width","height"].includes(t.propertyName)&&(r.style.width="",r.style.height="",Xr.off(r,Xr.transitionEndEvent,e))}),r.style.width=o.width+"px",r.style.height=o.height+"px"}Xr.toggleHidden(i,!0),i.setAttribute("tabindex",-1),Xr.toggleHidden(n,!1);var a=Xr.getElements.call(this,'[aria-controls="'+e+'"]');Array.from(a).forEach(function(e){e.setAttribute("aria-expanded",!0)}),n.removeAttribute("tabindex"),n.querySelectorAll("button:not(:disabled), input:not(:disabled), [tabindex]")[0].focus()}},create:function(e){var t=this;if(Xr.is.empty(this.config.controls))return null;var n=Xr.createElement("div",Xr.getAttributesFromSelector(this.config.selectors.controls.wrapper));if(this.config.controls.includes("restart")&&n.appendChild(io.createButton.call(this,"restart")),this.config.controls.includes("rewind")&&n.appendChild(io.createButton.call(this,"rewind")),this.config.controls.includes("play")&&n.appendChild(io.createButton.call(this,"play")),this.config.controls.includes("fast-forward")&&n.appendChild(io.createButton.call(this,"fast-forward")),this.config.controls.includes("progress")){var i=Xr.createElement("div",Xr.getAttributesFromSelector(this.config.selectors.progress)),r=io.createRange.call(this,"seek",{id:"plyr-seek-"+e.id});if(i.appendChild(r.label),i.appendChild(r.input),i.appendChild(io.createProgress.call(this,"buffer")),this.config.tooltips.seek){var o=Xr.createElement("span",{class:this.config.classNames.tooltip},"00:00");i.appendChild(o),this.elements.display.seekTooltip=o}this.elements.progress=i,n.appendChild(this.elements.progress)}if(this.config.controls.includes("current-time")&&n.appendChild(io.createTime.call(this,"currentTime")),this.config.controls.includes("duration")&&n.appendChild(io.createTime.call(this,"duration")),this.config.controls.includes("mute")&&n.appendChild(io.createButton.call(this,"mute")),this.config.controls.includes("volume")){var a=Xr.createElement("div",{class:"plyr__volume"}),s={max:1,step:.05,value:this.config.volume},l=io.createRange.call(this,"volume",Xr.extend(s,{id:"plyr-volume-"+e.id}));a.appendChild(l.label),a.appendChild(l.input),this.elements.volume=a,n.appendChild(a)}if(this.config.controls.includes("captions")&&n.appendChild(io.createButton.call(this,"captions")),this.config.controls.includes("settings")&&!Xr.is.empty(this.config.settings)){var c=Xr.createElement("div",{class:"plyr__menu",hidden:""});c.appendChild(io.createButton.call(this,"settings",{id:"plyr-settings-toggle-"+e.id,"aria-haspopup":!0,"aria-controls":"plyr-settings-"+e.id,"aria-expanded":!1}));var u=Xr.createElement("form",{class:"plyr__menu__container",id:"plyr-settings-"+e.id,hidden:"","aria-labelled-by":"plyr-settings-toggle-"+e.id,role:"tablist",tabindex:-1}),f=Xr.createElement("div"),d=Xr.createElement("div",{id:"plyr-settings-"+e.id+"-home","aria-labelled-by":"plyr-settings-toggle-"+e.id,role:"tabpanel"}),h=Xr.createElement("ul",{role:"tablist"});this.config.settings.forEach(function(n){var i=Xr.createElement("li",{role:"tab",hidden:""}),r=Xr.createElement("button",Xr.extend(Xr.getAttributesFromSelector(t.config.selectors.buttons.settings),{type:"button",class:t.config.classNames.control+" "+t.config.classNames.control+"--forward",id:"plyr-settings-"+e.id+"-"+n+"-tab","aria-haspopup":!0,"aria-controls":"plyr-settings-"+e.id+"-"+n,"aria-expanded":!1}),to(n,t.config)),o=Xr.createElement("span",{class:t.config.classNames.menu.value});o.innerHTML=e[n],r.appendChild(o),i.appendChild(r),h.appendChild(i),t.elements.settings.tabs[n]=i}),d.appendChild(h),f.appendChild(d),this.config.settings.forEach(function(n){var i=Xr.createElement("div",{id:"plyr-settings-"+e.id+"-"+n,hidden:"","aria-labelled-by":"plyr-settings-"+e.id+"-"+n+"-tab",role:"tabpanel",tabindex:-1}),r=Xr.createElement("button",{type:"button",class:t.config.classNames.control+" "+t.config.classNames.control+"--back","aria-haspopup":!0,"aria-controls":"plyr-settings-"+e.id+"-home","aria-expanded":!1},to(n,t.config));i.appendChild(r);var o=Xr.createElement("ul");i.appendChild(o),f.appendChild(i),t.elements.settings.panes[n]=i}),u.appendChild(f),c.appendChild(u),n.appendChild(c),this.elements.settings.form=u,this.elements.settings.menu=c}return this.config.controls.includes("pip")&&Zr.pip&&n.appendChild(io.createButton.call(this,"pip")),this.config.controls.includes("airplay")&&Zr.airplay&&n.appendChild(io.createButton.call(this,"airplay")),this.config.controls.includes("fullscreen")&&n.appendChild(io.createButton.call(this,"fullscreen")),this.config.controls.includes("play-large")&&this.elements.container.appendChild(io.createButton.call(this,"play-large")),this.elements.controls=n,this.isHTML5&&io.setQualityMenu.call(this,eo.getQualityOptions.call(this)),io.setSpeedMenu.call(this),n},inject:function(){var e=this;if(this.config.loadSprite){var t=io.getIconUrl.call(this);t.cors&&Xr.loadSprite(t.url,"sprite-plyr")}this.id=Math.floor(1e4*Math.random());var n=null;this.elements.controls=null;var i={id:this.id,seektime:this.config.seekTime,title:this.config.title},r=!0;Xr.is.string(this.config.controls)||Xr.is.element(this.config.controls)?n=this.config.controls:Xr.is.function(this.config.controls)?n=this.config.controls.call(this,i):(n=io.create.call(this,{id:this.id,seektime:this.config.seekTime,speed:this.speed,quality:this.quality,captions:ro.getLabel.call(this)}),r=!1);var o=function(e){var t=e;return Object.entries(i).forEach(function(e){var n=Kr(e,2),i=n[0],r=n[1];t=Xr.replaceAll(t,"{"+i+"}",r)}),t};r&&(Xr.is.string(this.config.controls)?n=o(n):Xr.is.element(n)&&(n.innerHTML=o(n.innerHTML)));var a=void 0;if(Xr.is.string(this.config.selectors.controls.container)&&(a=document.querySelector(this.config.selectors.controls.container)),Xr.is.element(a)||(a=this.elements.container),Xr.is.element(n)?a.appendChild(n):n&&a.insertAdjacentHTML("beforeend",n),Xr.is.element(this.elements.controls)||io.findElements.call(this),window.navigator.userAgent.includes("Edge")&&Xr.repaint(a),this.config.tooltips.controls){var s=Xr.getElements.call(this,[this.config.selectors.controls.wrapper," ",this.config.selectors.labels," .",this.config.classNames.hidden].join(""));Array.from(s).forEach(function(t){Xr.toggleClass(t,e.config.classNames.hidden,!1),Xr.toggleClass(t,e.config.classNames.tooltip,!0),t.setAttribute("role","tooltip")})}}},ro={setup:function(){if(this.supported.ui)if(!this.isVideo||this.isYouTube||this.isHTML5&&!Zr.textTracks)Xr.is.array(this.config.controls)&&this.config.controls.includes("settings")&&this.config.settings.includes("captions")&&io.setCaptionsMenu.call(this);else{if(Xr.is.element(this.elements.captions)||(this.elements.captions=Xr.createElement("div",Xr.getAttributesFromSelector(this.config.selectors.captions)),Xr.insertAfter(this.elements.captions,this.elements.wrapper)),Xr.getBrowser().isIE&&window.URL){var e=this.media.querySelectorAll("track");Array.from(e).forEach(function(e){var t=e.getAttribute("src"),n=Xr.parseUrl(t);n.hostname!==window.location.href.hostname&&["http:","https:"].includes(n.protocol)&&Xr.fetch(t,"blob").then(function(t){e.setAttribute("src",window.URL.createObjectURL(t))}).catch(function(){Xr.removeElement(e)})})}var t=this.storage.get("captions");Xr.is.boolean(t)||(t=this.config.captions.active);var n=this.storage.get("language")||this.config.captions.language;if("auto"===n){var i=(navigator.language||navigator.userLanguage).split("-");n=Kr(i,1)[0]}if(ro.setLanguage.call(this,n,t),this.isHTML5){var r=this.config.captions.update?"addtrack removetrack":"removetrack";Xr.on(this.media.textTracks,r,ro.update.bind(this))}setTimeout(ro.update.bind(this),0)}},update:function(){var e=this,t=ro.getTracks.call(this,!0),n=this.captions,i=n.language,r=n.meta;this.isHTML5&&this.isVideo&&t.filter(function(e){return!r.get(e)}).forEach(function(t){e.debug.log("Track added",t),r.set(t,{default:"showing"===t.mode}),t.mode="hidden",Xr.on(t,"cuechange",function(){return ro.updateCues.call(e)})});var o=!t.find(function(t){return t===e.captions.currentTrackNode}),a=this.language!==i&&t.find(function(e){return e.language===i});(o||a)&&ro.setLanguage.call(this,i,this.config.captions.active),Xr.toggleClass(this.elements.container,this.config.classNames.captions.enabled,!Xr.is.empty(t)),(this.config.controls||[]).includes("settings")&&this.config.settings.includes("captions")&&io.setCaptionsMenu.call(this)},set:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=ro.getTracks.call(this);if(-1!==e)if(Xr.is.number(e))if(e in i){if(this.captions.currentTrack!==e){this.captions.currentTrack=e;var r=ro.getCurrentTrack.call(this),o=(r||{}).language;this.captions.currentTrackNode=r,t&&(this.captions.language=o),this.isVimeo&&this.embed.enableTextTrack(o),Xr.dispatchEvent.call(this,this.media,"languagechange")}this.isHTML5&&this.isVideo&&ro.updateCues.call(this),n&&this.toggleCaptions(!0)}else this.debug.warn("Track not found",e);else this.debug.warn("Invalid caption argument",e);else this.toggleCaptions(!1)},setLanguage:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(Xr.is.string(e)){this.captions.language=e.toLowerCase();var n=ro.getTracks.call(this),i=ro.getCurrentTrack.call(this,!0);ro.set.call(this,n.indexOf(i),!1,t)}else this.debug.warn("Invalid language argument",e)},getTracks:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return Array.from((this.media||{}).textTracks||[]).filter(function(n){return!e.isHTML5||t||e.captions.meta.has(n)}).filter(function(e){return["captions","subtitles"].includes(e.kind)})},getCurrentTrack:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=ro.getTracks.call(this),i=function(t){return Number((e.captions.meta.get(t)||{}).default)},r=Array.from(n).sort(function(e,t){return i(t)-i(e)});return!t&&n[this.currentTrack]||r.find(function(t){return t.language===e.captions.language})||r[0]},getLabel:function(e){var t=e;return!Xr.is.track(t)&&Zr.textTracks&&this.captions.active&&(t=ro.getCurrentTrack.call(this)),Xr.is.track(t)?Xr.is.empty(t.label)?Xr.is.empty(t.language)?to("enabled",this.config):e.language.toUpperCase():t.label:to("disabled",this.config)},updateCues:function(e){if(this.supported.ui)if(Xr.is.element(this.elements.captions))if(Xr.is.nullOrUndefined(e)||Array.isArray(e)){var t=e;if(!t){var n=ro.getCurrentTrack.call(this);t=Array.from((n||{}).activeCues||[]).map(function(e){return e.getCueAsHTML()}).map(Xr.getHTML)}var i=t.map(function(e){return e.trim()}).join("\n");if(i!==this.elements.captions.innerHTML){Xr.emptyElement(this.elements.captions);var r=Xr.createElement("span",Xr.getAttributesFromSelector(this.config.selectors.caption));r.innerHTML=i,this.elements.captions.appendChild(r),Xr.dispatchEvent.call(this,this.media,"cuechange")}}else this.debug.warn("updateCues: Invalid input",e);else this.debug.warn("No captions element to render to")}},oo=function(){},ao=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];zr(this,e),this.enabled=window.console&&t,this.enabled&&this.log("Debugging enabled")}return Gr(e,[{key:"log",get:function(){return this.enabled?Function.prototype.bind.call(console.log,console):oo}},{key:"warn",get:function(){return this.enabled?Function.prototype.bind.call(console.warn,console):oo}},{key:"error",get:function(){return this.enabled?Function.prototype.bind.call(console.error,console):oo}}]),e}(),so={enabled:!0,title:"",debug:!1,autoplay:!1,autopause:!0,seekTime:10,volume:1,muted:!1,duration:null,displayDuration:!0,invertTime:!0,toggleInvert:!0,ratio:"16:9",clickToPlay:!0,hideControls:!0,resetOnEnd:!1,disableContextMenu:!0,loadSprite:!0,iconPrefix:"plyr",iconUrl:"https://cdn.plyr.io/3.3.12/plyr.svg",blankVideo:"https://cdn.plyr.io/static/blank.mp4",quality:{default:576,options:[4320,2880,2160,1440,1080,720,576,480,360,240,"default"]},loop:{active:!1},speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2]},keyboard:{focused:!0,global:!1},tooltips:{controls:!1,seek:!0},captions:{active:!1,language:"auto",update:!1},fullscreen:{enabled:!0,fallback:!0,iosNative:!1},storage:{enabled:!0,key:"plyr"},controls:["play-large","play","progress","current-time","mute","volume","captions","settings","pip","airplay","fullscreen"],settings:["captions","quality","speed"],i18n:{restart:"Restart",rewind:"Rewind {seektime}s",play:"Play",pause:"Pause",fastForward:"Forward {seektime}s",seek:"Seek",played:"Played",buffered:"Buffered",currentTime:"Current time",duration:"Duration",volume:"Volume",mute:"Mute",unmute:"Unmute",enableCaptions:"Enable captions",disableCaptions:"Disable captions",enterFullscreen:"Enter fullscreen",exitFullscreen:"Exit fullscreen",frameTitle:"Player for {title}",captions:"Captions",settings:"Settings",speed:"Speed",normal:"Normal",quality:"Quality",loop:"Loop",start:"Start",end:"End",all:"All",reset:"Reset",disabled:"Disabled",enabled:"Enabled",advertisement:"Ad",qualityBadge:{2160:"4K",1440:"HD",1080:"HD",720:"HD",576:"SD",480:"SD"}},urls:{vimeo:{sdk:"https://player.vimeo.com/api/player.js",iframe:"https://player.vimeo.com/video/{0}?{1}",api:"https://vimeo.com/api/v2/video/{0}.json"},youtube:{sdk:"https://www.youtube.com/iframe_api",api:"https://www.googleapis.com/youtube/v3/videos?id={0}&key={1}&fields=items(snippet(title))&part=snippet"},googleIMA:{sdk:"https://imasdk.googleapis.com/js/sdkloader/ima3.js"}},listeners:{seek:null,play:null,pause:null,restart:null,rewind:null,fastForward:null,mute:null,volume:null,captions:null,fullscreen:null,pip:null,airplay:null,speed:null,quality:null,loop:null,language:null},events:["ended","progress","stalled","playing","waiting","canplay","canplaythrough","loadstart","loadeddata","loadedmetadata","timeupdate","volumechange","play","pause","error","seeking","seeked","emptied","ratechange","cuechange","enterfullscreen","exitfullscreen","captionsenabled","captionsdisabled","languagechange","controlshidden","controlsshown","ready","statechange","qualitychange","qualityrequested","adsloaded","adscontentpause","adscontentresume","adstarted","adsmidpoint","adscomplete","adsallcomplete","adsimpression","adsclick"],selectors:{editable:"input, textarea, select, [contenteditable]",container:".plyr",controls:{container:null,wrapper:".plyr__controls"},labels:"[data-plyr]",buttons:{play:'[data-plyr="play"]',pause:'[data-plyr="pause"]',restart:'[data-plyr="restart"]',rewind:'[data-plyr="rewind"]',fastForward:'[data-plyr="fast-forward"]',mute:'[data-plyr="mute"]',captions:'[data-plyr="captions"]',fullscreen:'[data-plyr="fullscreen"]',pip:'[data-plyr="pip"]',airplay:'[data-plyr="airplay"]',settings:'[data-plyr="settings"]',loop:'[data-plyr="loop"]'},inputs:{seek:'[data-plyr="seek"]',volume:'[data-plyr="volume"]',speed:'[data-plyr="speed"]',language:'[data-plyr="language"]',quality:'[data-plyr="quality"]'},display:{currentTime:".plyr__time--current",duration:".plyr__time--duration",buffer:".plyr__progress__buffer",loop:".plyr__progress__loop",volume:".plyr__volume--display"},progress:".plyr__progress",captions:".plyr__captions",caption:".plyr__caption",menu:{quality:".js-plyr__menu__list--quality"}},classNames:{type:"plyr--{0}",provider:"plyr--{0}",video:"plyr__video-wrapper",embed:"plyr__video-embed",embedContainer:"plyr__video-embed__container",poster:"plyr__poster",posterEnabled:"plyr__poster-enabled",ads:"plyr__ads",control:"plyr__control",playing:"plyr--playing",paused:"plyr--paused",stopped:"plyr--stopped",loading:"plyr--loading",hover:"plyr--hover",tooltip:"plyr__tooltip",cues:"plyr__cues",hidden:"plyr__sr-only",hideControls:"plyr--hide-controls",isIos:"plyr--is-ios",isTouch:"plyr--is-touch",uiSupported:"plyr--full-ui",noTransition:"plyr--no-transition",menu:{value:"plyr__menu__value",badge:"plyr__badge",open:"plyr--menu-open"},captions:{enabled:"plyr--captions-enabled",active:"plyr--captions-active"},fullscreen:{enabled:"plyr--fullscreen-enabled",fallback:"plyr--fullscreen-fallback"},pip:{supported:"plyr--pip-supported",active:"plyr--pip-active"},airplay:{supported:"plyr--airplay-supported",active:"plyr--airplay-active"},tabFocus:"plyr__tab-focus"},attributes:{embed:{provider:"data-plyr-provider",id:"data-plyr-embed-id"}},keys:{google:null},ads:{enabled:!1,publisherId:""}},lo=Xr.getBrowser();function co(){if(this.enabled){var e=this.player.elements.buttons.fullscreen;Xr.is.element(e)&&Xr.toggleState(e,this.active),Xr.dispatchEvent.call(this.player,this.target,this.active?"enterfullscreen":"exitfullscreen",!0),lo.isIos||Xr.trapFocus.call(this.player,this.target,this.active)}}function uo(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e?this.scrollPosition={x:window.scrollX||0,y:window.scrollY||0}:window.scrollTo(this.scrollPosition.x,this.scrollPosition.y),document.body.style.overflow=e?"hidden":"",Xr.toggleClass(this.target,this.player.config.classNames.fullscreen.fallback,e),co.call(this)}var fo=function(){function e(t){var n=this;zr(this,e),this.player=t,this.prefix=e.prefix,this.property=e.property,this.scrollPosition={x:0,y:0},Xr.on(document,"ms"===this.prefix?"MSFullscreenChange":this.prefix+"fullscreenchange",function(){co.call(n)}),Xr.on(this.player.elements.container,"dblclick",function(e){Xr.is.element(n.player.elements.controls)&&n.player.elements.controls.contains(e.target)||n.toggle()}),this.update()}return Gr(e,[{key:"update",value:function(){this.enabled?this.player.debug.log((e.native?"Native":"Fallback")+" fullscreen enabled"):this.player.debug.log("Fullscreen not supported and fallback disabled"),Xr.toggleClass(this.player.elements.container,this.player.config.classNames.fullscreen.enabled,this.enabled)}},{key:"enter",value:function(){this.enabled&&(lo.isIos&&this.player.config.fullscreen.iosNative?this.player.playing&&this.target.webkitEnterFullscreen():e.native?this.prefix?Xr.is.empty(this.prefix)||this.target[this.prefix+"Request"+this.property]():this.target.requestFullscreen():uo.call(this,!0))}},{key:"exit",value:function(){if(this.enabled)if(lo.isIos&&this.player.config.fullscreen.iosNative)this.target.webkitExitFullscreen(),this.player.play();else if(e.native)if(this.prefix){if(!Xr.is.empty(this.prefix)){var t="moz"===this.prefix?"Cancel":"Exit";document[""+this.prefix+t+this.property]()}}else(document.cancelFullScreen||document.exitFullscreen).call(document);else uo.call(this,!1)}},{key:"toggle",value:function(){this.active?this.exit():this.enter()}},{key:"enabled",get:function(){return(e.native||this.player.config.fullscreen.fallback)&&this.player.config.fullscreen.enabled&&this.player.supported.ui&&this.player.isVideo}},{key:"active",get:function(){return!!this.enabled&&(e.native?(this.prefix?document[""+this.prefix+this.property+"Element"]:document.fullscreenElement)===this.target:Xr.hasClass(this.target,this.player.config.classNames.fullscreen.fallback))}},{key:"target",get:function(){return lo.isIos&&this.player.config.fullscreen.iosNative?this.player.media:this.player.elements.container}}],[{key:"native",get:function(){return!!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)}},{key:"prefix",get:function(){if(Xr.is.function(document.exitFullscreen))return"";var e="";return["webkit","moz","ms"].some(function(t){return!(!Xr.is.function(document[t+"ExitFullscreen"])&&!Xr.is.function(document[t+"CancelFullScreen"]))&&(e=t,!0)}),e}},{key:"property",get:function(){return"moz"===this.prefix?"FullScreen":"Fullscreen"}}]),e}(),ho=Xr.getBrowser(),po={addStyleHook:function(){Xr.toggleClass(this.elements.container,this.config.selectors.container.replace(".",""),!0),Xr.toggleClass(this.elements.container,this.config.classNames.uiSupported,this.supported.ui)},toggleNativeControls:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.isHTML5?this.media.setAttribute("controls",""):this.media.removeAttribute("controls")},build:function(){var e=this;if(this.listeners.media(),!this.supported.ui)return this.debug.warn("Basic support only for "+this.provider+" "+this.type),void po.toggleNativeControls.call(this,!0);Xr.is.element(this.elements.controls)||(io.inject.call(this),this.listeners.controls()),po.toggleNativeControls.call(this),this.isHTML5&&ro.setup.call(this),this.volume=null,this.muted=null,this.speed=null,this.loop=null,this.quality=null,io.updateVolume.call(this),io.timeUpdate.call(this),po.checkPlaying.call(this),Xr.toggleClass(this.elements.container,this.config.classNames.pip.supported,Zr.pip&&this.isHTML5&&this.isVideo),Xr.toggleClass(this.elements.container,this.config.classNames.airplay.supported,Zr.airplay&&this.isHTML5),Xr.toggleClass(this.elements.container,this.config.classNames.isIos,ho.isIos),Xr.toggleClass(this.elements.container,this.config.classNames.isTouch,this.touch),this.ready=!0,setTimeout(function(){Xr.dispatchEvent.call(e,e.media,"ready")},0),po.setTitle.call(this),this.poster&&this.elements.poster&&!this.elements.poster.style.backgroundImage&&po.setPoster.call(this,this.poster),this.config.duration&&io.durationUpdate.call(this)},setTitle:function(){var e=to("play",this.config);if(Xr.is.string(this.config.title)&&!Xr.is.empty(this.config.title)&&(e+=", "+this.config.title,this.elements.container.setAttribute("aria-label",this.config.title)),Xr.is.nodeList(this.elements.buttons.play)&&Array.from(this.elements.buttons.play).forEach(function(t){t.setAttribute("aria-label",e)}),this.isEmbed){var t=Xr.getElement.call(this,"iframe");if(!Xr.is.element(t))return;var n=Xr.is.empty(this.config.title)?"video":this.config.title,i=to("frameTitle",this.config);t.setAttribute("title",i.replace("{title}",n))}},togglePoster:function(e){Xr.toggleClass(this.elements.container,this.config.classNames.posterEnabled,e)},setPoster:function(e){var t=this;if(this.media.setAttribute("poster",e),!Xr.is.element(this.elements.poster))return Promise.reject();var n=Xr.loadImage(e).then(function(){return t.elements.poster.style.backgroundImage="url('"+e+"')",Object.assign(t.elements.poster.style,{backgroundImage:"url('"+e+"')",backgroundSize:""}),po.togglePoster.call(t,!0),e});return n.catch(function(){return po.togglePoster.call(t,!1)}),n},checkPlaying:function(e){Xr.toggleClass(this.elements.container,this.config.classNames.playing,this.playing),Xr.toggleClass(this.elements.container,this.config.classNames.paused,this.paused),Xr.toggleClass(this.elements.container,this.config.classNames.stopped,this.stopped),Xr.toggleState(this.elements.buttons.play,this.playing),Xr.is.event(e)&&"timeupdate"===e.type||po.toggleControls.call(this)},checkLoading:function(e){var t=this;this.loading=["stalled","waiting"].includes(e.type),clearTimeout(this.timers.loading),this.timers.loading=setTimeout(function(){Xr.toggleClass(t.elements.container,t.config.classNames.loading,t.loading),po.toggleControls.call(t)},this.loading?250:0)},toggleControls:function(e){var t=this.elements.controls;t&&this.config.hideControls&&this.toggleControls(Boolean(e||this.loading||this.paused||t.pressed||t.hover))}},go=Xr.getBrowser(),mo=function(){function e(t){zr(this,e),this.player=t,this.lastKey=null,this.handleKey=this.handleKey.bind(this),this.toggleMenu=this.toggleMenu.bind(this),this.firstTouch=this.firstTouch.bind(this)}return Gr(e,[{key:"handleKey",value:function(e){var t=this,n=e.keyCode?e.keyCode:e.which,i="keydown"===e.type,r=i&&n===this.lastKey;if(!(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)&&Xr.is.number(n)){if(i){var o=Xr.getFocusElement();if(Xr.is.element(o)&&o!==this.player.elements.inputs.seek&&Xr.matches(o,this.player.config.selectors.editable))return;switch([48,49,50,51,52,53,54,56,57,32,75,38,40,77,39,37,70,67,73,76,79].includes(n)&&(e.preventDefault(),e.stopPropagation()),n){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:r||(t.player.currentTime=t.player.duration/10*(n-48));break;case 32:case 75:r||this.player.togglePlay();break;case 38:this.player.increaseVolume(.1);break;case 40:this.player.decreaseVolume(.1);break;case 77:r||(this.player.muted=!this.player.muted);break;case 39:this.player.forward();break;case 37:this.player.rewind();break;case 70:this.player.fullscreen.toggle();break;case 67:r||this.player.toggleCaptions();break;case 76:this.player.loop=!this.player.loop}!this.player.fullscreen.enabled&&this.player.fullscreen.active&&27===n&&this.player.fullscreen.toggle(),this.lastKey=n}else this.lastKey=null}}},{key:"toggleMenu",value:function(e){io.toggleMenu.call(this.player,e)}},{key:"firstTouch",value:function(){this.player.touch=!0,Xr.toggleClass(this.player.elements.container,this.player.config.classNames.isTouch,!0),Xr.off(document.body,"touchstart",this.firstTouch)}},{key:"global",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.player.config.keyboard.global&&Xr.toggleListener(window,"keydown keyup",this.handleKey,e,!1),Xr.toggleListener(document.body,"click",this.toggleMenu,e),Xr.on(document.body,"touchstart",this.firstTouch)}},{key:"container",value:function(){var e=this;!this.player.config.keyboard.global&&this.player.config.keyboard.focused&&Xr.on(this.player.elements.container,"keydown keyup",this.handleKey,!1),Xr.on(this.player.elements.container,"focusout",function(t){Xr.toggleClass(t.target,e.player.config.classNames.tabFocus,!1)}),Xr.on(this.player.elements.container,"keydown",function(t){9===t.keyCode&&setTimeout(function(){Xr.toggleClass(Xr.getFocusElement(),e.player.config.classNames.tabFocus,!0)},0)}),Xr.on(this.player.elements.container,"mousemove mouseleave touchstart touchmove enterfullscreen exitfullscreen",function(t){var n=e.player.elements.controls;"enterfullscreen"===t.type&&(n.pressed=!1,n.hover=!1);var i=0;["touchstart","touchmove","mousemove"].includes(t.type)&&(po.toggleControls.call(e.player,!0),i=e.player.touch?3e3:2e3),clearTimeout(e.player.timers.controls),e.player.timers.controls=setTimeout(function(){return po.toggleControls.call(e.player,!1)},i)})}},{key:"media",value:function(){var e=this;if(Xr.on(this.player.media,"timeupdate seeking seeked",function(t){return io.timeUpdate.call(e.player,t)}),Xr.on(this.player.media,"durationchange loadeddata loadedmetadata",function(t){return io.durationUpdate.call(e.player,t)}),Xr.on(this.player.media,"loadeddata",function(){Xr.toggleHidden(e.player.elements.volume,!e.player.hasAudio),Xr.toggleHidden(e.player.elements.buttons.mute,!e.player.hasAudio)}),Xr.on(this.player.media,"ended",function(){e.player.isHTML5&&e.player.isVideo&&e.player.config.resetOnEnd&&e.player.restart()}),Xr.on(this.player.media,"progress playing seeking seeked",function(t){return io.updateProgress.call(e.player,t)}),Xr.on(this.player.media,"volumechange",function(t){return io.updateVolume.call(e.player,t)}),Xr.on(this.player.media,"playing play pause ended emptied timeupdate",function(t){return po.checkPlaying.call(e.player,t)}),Xr.on(this.player.media,"waiting canplay seeked playing",function(t){return po.checkLoading.call(e.player,t)}),Xr.on(this.player.media,"playing",function(){e.player.ads&&e.player.ads.enabled&&!e.player.ads.initialized&&e.player.ads.managerPromise.then(function(){return e.player.ads.play()}).catch(function(){return e.player.play()})}),this.player.supported.ui&&this.player.config.clickToPlay&&!this.player.isAudio){var t=Xr.getElement.call(this.player,"."+this.player.config.classNames.video);if(!Xr.is.element(t))return;Xr.on(t,"click",function(){e.player.config.hideControls&&e.player.touch&&!e.player.paused||(e.player.paused?e.player.play():e.player.ended?(e.player.restart(),e.player.play()):e.player.pause())})}this.player.supported.ui&&this.player.config.disableContextMenu&&Xr.on(this.player.elements.wrapper,"contextmenu",function(e){e.preventDefault()},!1),Xr.on(this.player.media,"volumechange",function(){e.player.storage.set({volume:e.player.volume,muted:e.player.muted})}),Xr.on(this.player.media,"ratechange",function(){io.updateSetting.call(e.player,"speed"),e.player.storage.set({speed:e.player.speed})}),Xr.on(this.player.media,"qualityrequested",function(t){e.player.storage.set({quality:t.detail.quality})}),Xr.on(this.player.media,"qualitychange",function(t){io.updateSetting.call(e.player,"quality",null,t.detail.quality)}),Xr.on(this.player.media,"languagechange",function(){io.updateSetting.call(e.player,"captions"),e.player.storage.set({language:e.player.language})}),Xr.on(this.player.media,"captionsenabled captionsdisabled",function(){io.updateSetting.call(e.player,"captions"),e.player.storage.set({captions:e.player.captions.active})}),Xr.on(this.player.media,this.player.config.events.concat(["keyup","keydown"]).join(" "),function(t){var n=t.detail,i=void 0===n?{}:n;"error"===t.type&&(i=e.player.media.error),Xr.dispatchEvent.call(e.player,e.player.elements.container,t.type,!0,i)})}},{key:"controls",value:function(){var e=this,t=go.isIE?"change":"input",n=function(t,n,i){var r=e.player.config.listeners[i],o=!0;Xr.is.function(r)&&(o=r.call(e.player,t)),o&&Xr.is.function(n)&&n.call(e.player,t)},i=function(t,i,r,o){var a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],s=e.player.config.listeners[o],l=Xr.is.function(s);Xr.on(t,i,function(e){return n(e,r,o)},a&&!l)};i(this.player.elements.buttons.play,"click",this.player.togglePlay,"play"),i(this.player.elements.buttons.restart,"click",this.player.restart,"restart"),i(this.player.elements.buttons.rewind,"click",this.player.rewind,"rewind"),i(this.player.elements.buttons.fastForward,"click",this.player.forward,"fastForward"),i(this.player.elements.buttons.mute,"click",function(){e.player.muted=!e.player.muted},"mute"),i(this.player.elements.buttons.captions,"click",this.player.toggleCaptions),i(this.player.elements.buttons.fullscreen,"click",function(){e.player.fullscreen.toggle()},"fullscreen"),i(this.player.elements.buttons.pip,"click",function(){e.player.pip="toggle"},"pip"),i(this.player.elements.buttons.airplay,"click",this.player.airplay,"airplay"),i(this.player.elements.buttons.settings,"click",function(t){io.toggleMenu.call(e.player,t)}),i(this.player.elements.settings.form,"click",function(t){t.stopPropagation();var i=function(){var t="plyr-settings-"+e.player.id+"-home";io.showTab.call(e.player,t)};if(Xr.matches(t.target,e.player.config.selectors.inputs.language))n(t,function(){e.player.currentTrack=Number(t.target.value),i()},"language");else if(Xr.matches(t.target,e.player.config.selectors.inputs.quality))n(t,function(){e.player.quality=t.target.value,i()},"quality");else if(Xr.matches(t.target,e.player.config.selectors.inputs.speed))n(t,function(){e.player.speed=parseFloat(t.target.value),i()},"speed");else{var r=t.target;io.showTab.call(e.player,r.getAttribute("aria-controls"))}}),i(this.player.elements.inputs.seek,"mousedown mousemove",function(t){var n=e.player.elements.progress.getBoundingClientRect(),i=100/n.width*(t.pageX-n.left);t.currentTarget.setAttribute("seek-value",i)}),i(this.player.elements.inputs.seek,"mousedown mouseup keydown keyup touchstart touchend",function(t){var n=t.currentTarget,i=t.keyCode?t.keyCode:t.which,r=t.type;if("keydown"!==r&&"keyup"!==r||39===i||37===i){var o=n.hasAttribute("play-on-seeked"),a=["mouseup","touchend","keyup"].includes(t.type);o&&a?(n.removeAttribute("play-on-seeked"),e.player.play()):!a&&e.player.playing&&(n.setAttribute("play-on-seeked",""),e.player.pause())}}),i(this.player.elements.inputs.seek,t,function(t){var n=t.currentTarget,i=n.getAttribute("seek-value");Xr.is.empty(i)&&(i=n.value),n.removeAttribute("seek-value"),e.player.currentTime=i/n.max*e.player.duration},"seek"),this.player.config.toggleInvert&&!Xr.is.element(this.player.elements.display.duration)&&i(this.player.elements.display.currentTime,"click",function(){0!==e.player.currentTime&&(e.player.config.invertTime=!e.player.config.invertTime,io.timeUpdate.call(e.player))}),i(this.player.elements.inputs.volume,t,function(t){e.player.volume=t.target.value},"volume"),go.isWebkit&&i(Xr.getElements.call(this.player,'input[type="range"]'),"input",function(t){io.updateRangeFill.call(e.player,t.target)}),i(this.player.elements.progress,"mouseenter mouseleave mousemove",function(t){return io.updateSeekTooltip.call(e.player,t)}),i(this.player.elements.controls,"mouseenter mouseleave",function(t){e.player.elements.controls.hover=!e.player.touch&&"mouseenter"===t.type}),i(this.player.elements.controls,"mousedown mouseup touchstart touchend touchcancel",function(t){e.player.elements.controls.pressed=["mousedown","touchstart"].includes(t.type)}),i(this.player.elements.controls,"focusin focusout",function(t){var n=e.player,i=n.config,r=n.elements,o=n.timers;if(Xr.toggleClass(r.controls,i.classNames.noTransition,"focusin"===t.type),po.toggleControls.call(e.player,"focusin"===t.type),"focusin"===t.type){setTimeout(function(){Xr.toggleClass(r.controls,i.classNames.noTransition,!1)},0);var a=e.touch?3e3:4e3;clearTimeout(o.controls),o.controls=setTimeout(function(){return po.toggleControls.call(e.player,!1)},a)}}),i(this.player.elements.inputs.volume,"wheel",function(t){var n=t.webkitDirectionInvertedFromDevice,i=0;(t.deltaY<0||t.deltaX>0)&&(n?(e.player.decreaseVolume(.02),i=-1):(e.player.increaseVolume(.02),i=1)),(t.deltaY>0||t.deltaX<0)&&(n?(e.player.increaseVolume(.02),i=1):(e.player.decreaseVolume(.02),i=-1)),(1===i&&e.player.media.volume<1||-1===i&&e.player.media.volume>0)&&t.preventDefault()},"volume",!1)}},{key:"clear",value:function(){this.global(!1)}}]),e}();function yo(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Xr.dispatchEvent.call(this,this.media,e?"play":"pause"))}var vo={setup:function(){var e=this;Xr.toggleClass(this.elements.wrapper,this.config.classNames.embed,!0),vo.setAspectRatio.call(this),Xr.is.object(window.Vimeo)?vo.ready.call(this):Xr.loadScript(this.config.urls.vimeo.sdk).then(function(){vo.ready.call(e)}).catch(function(t){e.debug.warn("Vimeo API failed to load",t)})},setAspectRatio:function(e){var t=Xr.is.string(e)?e.split(":"):this.config.ratio.split(":"),n=100/t[0]*t[1];if(this.elements.wrapper.style.paddingBottom=n+"%",this.supported.ui){var i=(240-n)/4.8;this.media.style.transform="translateY(-"+i+"%)"}},ready:function(){var e=this,t=this,n={loop:t.config.loop.active,autoplay:t.autoplay,byline:!1,portrait:!1,title:!1,speed:!0,transparent:0,gesture:"media",playsinline:!this.config.fullscreen.iosNative},i=Xr.buildUrlParams(n),r=t.media.getAttribute("src");Xr.is.empty(r)&&(r=t.media.getAttribute(t.config.attributes.embed.id));var o=Xr.parseVimeoId(r),a=Xr.createElement("iframe"),s=Xr.format(t.config.urls.vimeo.iframe,o,i);a.setAttribute("src",s),a.setAttribute("allowfullscreen",""),a.setAttribute("allowtransparency",""),a.setAttribute("allow","autoplay");var l=Xr.createElement("div",{class:t.config.classNames.embedContainer});l.appendChild(a),t.media=Xr.replaceElement(l,t.media),Xr.fetch(Xr.format(t.config.urls.vimeo.api,o),"json").then(function(e){if(!Xr.is.empty(e)){var n=new URL(e[0].thumbnail_large);n.pathname=n.pathname.split("_")[0]+".jpg",po.setPoster.call(t,n.href)}}),t.embed=new window.Vimeo.Player(a,{autopause:t.config.autopause,muted:t.muted}),t.media.paused=!0,t.media.currentTime=0,t.supported.ui&&t.embed.disableTextTrack(),t.media.play=function(){return yo.call(t,!0),t.embed.play()},t.media.pause=function(){return yo.call(t,!1),t.embed.pause()},t.media.stop=function(){t.pause(),t.currentTime=0};var c=t.media.currentTime;Object.defineProperty(t.media,"currentTime",{get:function(){return c},set:function(e){var n=t.embed,i=t.media,r=t.paused,o=t.volume,a=r&&!n.hasPlayed;i.seeking=!0,Xr.dispatchEvent.call(t,i,"seeking"),Promise.resolve(a&&n.setVolume(0)).then(function(){return n.setCurrentTime(e)}).then(function(){return a&&n.pause()}).then(function(){return a&&n.setVolume(o)}).catch(function(){})}});var u=t.config.speed.selected;Object.defineProperty(t.media,"playbackRate",{get:function(){return u},set:function(e){t.embed.setPlaybackRate(e).then(function(){u=e,Xr.dispatchEvent.call(t,t.media,"ratechange")}).catch(function(e){"Error"===e.name&&io.setSpeedMenu.call(t,[])})}});var f=t.config.volume;Object.defineProperty(t.media,"volume",{get:function(){return f},set:function(e){t.embed.setVolume(e).then(function(){f=e,Xr.dispatchEvent.call(t,t.media,"volumechange")})}});var d=t.config.muted;Object.defineProperty(t.media,"muted",{get:function(){return d},set:function(e){var n=!!Xr.is.boolean(e)&&e;t.embed.setVolume(n?0:t.config.volume).then(function(){d=n,Xr.dispatchEvent.call(t,t.media,"volumechange")})}});var h=t.config.loop;Object.defineProperty(t.media,"loop",{get:function(){return h},set:function(e){var n=Xr.is.boolean(e)?e:t.config.loop.active;t.embed.setLoop(n).then(function(){h=n})}});var p=void 0;t.embed.getVideoUrl().then(function(e){p=e}).catch(function(t){e.debug.warn(t)}),Object.defineProperty(t.media,"currentSrc",{get:function(){return p}}),Object.defineProperty(t.media,"ended",{get:function(){return t.currentTime===t.duration}}),Promise.all([t.embed.getVideoWidth(),t.embed.getVideoHeight()]).then(function(t){var n=Xr.getAspectRatio(t[0],t[1]);vo.setAspectRatio.call(e,n)}),t.embed.setAutopause(t.config.autopause).then(function(e){t.config.autopause=e}),t.embed.getVideoTitle().then(function(n){t.config.title=n,po.setTitle.call(e)}),t.embed.getCurrentTime().then(function(e){c=e,Xr.dispatchEvent.call(t,t.media,"timeupdate")}),t.embed.getDuration().then(function(e){t.media.duration=e,Xr.dispatchEvent.call(t,t.media,"durationchange")}),t.embed.getTextTracks().then(function(e){t.media.textTracks=e,ro.setup.call(t)}),t.embed.on("cuechange",function(e){var n=e.cues,i=(void 0===n?[]:n).map(function(e){return Xr.stripHTML(e.text)});ro.updateCues.call(t,i)}),t.embed.on("loaded",function(){(t.embed.getPaused().then(function(e){yo.call(t,!e),e||Xr.dispatchEvent.call(t,t.media,"playing")}),Xr.is.element(t.embed.element)&&t.supported.ui)&&t.embed.element.setAttribute("tabindex",-1)}),t.embed.on("play",function(){yo.call(t,!0),Xr.dispatchEvent.call(t,t.media,"playing")}),t.embed.on("pause",function(){yo.call(t,!1)}),t.embed.on("timeupdate",function(e){t.media.seeking=!1,c=e.seconds,Xr.dispatchEvent.call(t,t.media,"timeupdate")}),t.embed.on("progress",function(e){t.media.buffered=e.percent,Xr.dispatchEvent.call(t,t.media,"progress"),1===parseInt(e.percent,10)&&Xr.dispatchEvent.call(t,t.media,"canplaythrough"),t.embed.getDuration().then(function(e){e!==t.media.duration&&(t.media.duration=e,Xr.dispatchEvent.call(t,t.media,"durationchange"))})}),t.embed.on("seeked",function(){t.media.seeking=!1,Xr.dispatchEvent.call(t,t.media,"seeked")}),t.embed.on("ended",function(){t.media.paused=!0,Xr.dispatchEvent.call(t,t.media,"ended")}),t.embed.on("error",function(e){t.media.error=e,Xr.dispatchEvent.call(t,t.media,"error")}),setTimeout(function(){return po.build.call(t)},0)}};function bo(e){switch(e){case"hd2160":return 2160;case 2160:return"hd2160";case"hd1440":return 1440;case 1440:return"hd1440";case"hd1080":return 1080;case 1080:return"hd1080";case"hd720":return 720;case 720:return"hd720";case"large":return 480;case 480:return"large";case"medium":return 360;case 360:return"medium";case"small":return 240;case 240:return"small";default:return"default"}}function wo(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Xr.dispatchEvent.call(this,this.media,e?"play":"pause"))}var Eo={setup:function(){var e=this;Xr.toggleClass(this.elements.wrapper,this.config.classNames.embed,!0),Eo.setAspectRatio.call(this),Xr.is.object(window.YT)&&Xr.is.function(window.YT.Player)?Eo.ready.call(this):(Xr.loadScript(this.config.urls.youtube.sdk).catch(function(t){e.debug.warn("YouTube API failed to load",t)}),window.onYouTubeReadyCallbacks=window.onYouTubeReadyCallbacks||[],window.onYouTubeReadyCallbacks.push(function(){Eo.ready.call(e)}),window.onYouTubeIframeAPIReady=function(){window.onYouTubeReadyCallbacks.forEach(function(e){e()})})},getTitle:function(e){var t=this;if(Xr.is.function(this.embed.getVideoData)){var n=this.embed.getVideoData().title;if(Xr.is.empty(n))return this.config.title=n,void po.setTitle.call(this)}var i=this.config.keys.google;if(Xr.is.string(i)&&!Xr.is.empty(i)){var r=Xr.format(this.config.urls.youtube.api,e,i);Xr.fetch(r).then(function(e){Xr.is.object(e)&&(t.config.title=e.items[0].snippet.title,po.setTitle.call(t))}).catch(function(){})}},setAspectRatio:function(){var e=this.config.ratio.split(":");this.elements.wrapper.style.paddingBottom=100/e[0]*e[1]+"%"},ready:function(){var e=this,t=e.media.getAttribute("id");if(Xr.is.empty(t)||!t.startsWith("youtube-")){var n=e.media.getAttribute("src");Xr.is.empty(n)&&(n=e.media.getAttribute(this.config.attributes.embed.id));var i=Xr.parseYouTubeId(n),r=Xr.generateId(e.provider),o=Xr.createElement("div",{id:r});e.media=Xr.replaceElement(o,e.media);var a=function(e){return"https://img.youtube.com/vi/"+i+"/"+e+"default.jpg"};Xr.loadImage(a("maxres"),121).catch(function(){return Xr.loadImage(a("sd"),121)}).catch(function(){return Xr.loadImage(a("hq"))}).then(function(t){return po.setPoster.call(e,t.src)}).then(function(t){t.includes("maxres")||(e.elements.poster.style.backgroundSize="cover")}),e.embed=new window.YT.Player(r,{videoId:i,playerVars:{autoplay:e.config.autoplay?1:0,controls:e.supported.ui?0:1,rel:0,showinfo:0,iv_load_policy:3,modestbranding:1,disablekb:1,playsinline:1,widget_referrer:window?window.location.href:null,cc_load_policy:e.captions.active?1:0,cc_lang_pref:e.config.captions.language},events:{onError:function(t){if(!Xr.is.object(e.media.error)){var n={code:t.data};switch(t.data){case 2:n.message="The request contains an invalid parameter value. For example, this error occurs if you specify a video ID that does not have 11 characters, or if the video ID contains invalid characters, such as exclamation points or asterisks.";break;case 5:n.message="The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.";break;case 100:n.message="The video requested was not found. This error occurs when a video has been removed (for any reason) or has been marked as private.";break;case 101:case 150:n.message="The owner of the requested video does not allow it to be played in embedded players.";break;default:n.message="An unknown error occured"}e.media.error=n,Xr.dispatchEvent.call(e,e.media,"error")}},onPlaybackQualityChange:function(){Xr.dispatchEvent.call(e,e.media,"qualitychange",!1,{quality:e.media.quality})},onPlaybackRateChange:function(t){var n=t.target;e.media.playbackRate=n.getPlaybackRate(),Xr.dispatchEvent.call(e,e.media,"ratechange")},onReady:function(t){var n=t.target;Eo.getTitle.call(e,i),e.media.play=function(){wo.call(e,!0),n.playVideo()},e.media.pause=function(){wo.call(e,!1),n.pauseVideo()},e.media.stop=function(){n.stopVideo()},e.media.duration=n.getDuration(),e.media.paused=!0,e.media.currentTime=0,Object.defineProperty(e.media,"currentTime",{get:function(){return Number(n.getCurrentTime())},set:function(t){e.paused&&e.embed.mute(),e.media.seeking=!0,Xr.dispatchEvent.call(e,e.media,"seeking"),n.seekTo(t)}}),Object.defineProperty(e.media,"playbackRate",{get:function(){return n.getPlaybackRate()},set:function(e){n.setPlaybackRate(e)}}),Object.defineProperty(e.media,"quality",{get:function(){return bo(n.getPlaybackQuality())},set:function(t){var i=t;n.setPlaybackQuality(bo(i)),Xr.dispatchEvent.call(e,e.media,"qualityrequested",!1,{quality:i})}});var r=e.config.volume;Object.defineProperty(e.media,"volume",{get:function(){return r},set:function(t){r=t,n.setVolume(100*r),Xr.dispatchEvent.call(e,e.media,"volumechange")}});var o=e.config.muted;Object.defineProperty(e.media,"muted",{get:function(){return o},set:function(t){var i=Xr.is.boolean(t)?t:o;o=i,n[i?"mute":"unMute"](),Xr.dispatchEvent.call(e,e.media,"volumechange")}}),Object.defineProperty(e.media,"currentSrc",{get:function(){return n.getVideoUrl()}}),Object.defineProperty(e.media,"ended",{get:function(){return e.currentTime===e.duration}}),e.options.speed=n.getAvailablePlaybackRates(),e.supported.ui&&e.media.setAttribute("tabindex",-1),Xr.dispatchEvent.call(e,e.media,"timeupdate"),Xr.dispatchEvent.call(e,e.media,"durationchange"),clearInterval(e.timers.buffering),e.timers.buffering=setInterval(function(){e.media.buffered=n.getVideoLoadedFraction(),(null===e.media.lastBuffered||e.media.lastBuffered<e.media.buffered)&&Xr.dispatchEvent.call(e,e.media,"progress"),e.media.lastBuffered=e.media.buffered,1===e.media.buffered&&(clearInterval(e.timers.buffering),Xr.dispatchEvent.call(e,e.media,"canplaythrough"))},200),setTimeout(function(){return po.build.call(e)},50)},onStateChange:function(t){var n,i=t.target;switch(clearInterval(e.timers.playing),e.media.seeking&&[1,2].includes(t.data)&&(e.media.seeking=!1,Xr.dispatchEvent.call(e,e.media,"seeked")),t.data){case-1:Xr.dispatchEvent.call(e,e.media,"timeupdate"),e.media.buffered=i.getVideoLoadedFraction(),Xr.dispatchEvent.call(e,e.media,"progress");break;case 0:wo.call(e,!1),e.media.loop?(i.stopVideo(),i.playVideo()):Xr.dispatchEvent.call(e,e.media,"ended");break;case 1:e.media.paused&&!e.embed.hasPlayed?e.media.pause():(wo.call(e,!0),Xr.dispatchEvent.call(e,e.media,"playing"),e.timers.playing=setInterval(function(){Xr.dispatchEvent.call(e,e.media,"timeupdate")},50),e.media.duration!==i.getDuration()&&(e.media.duration=i.getDuration(),Xr.dispatchEvent.call(e,e.media,"durationchange")),io.setQualityMenu.call(e,(n=i.getAvailableQualityLevels(),Xr.is.empty(n)?n:Xr.dedupe(n.map(function(e){return bo(e)})))));break;case 2:e.muted||e.embed.unMute(),wo.call(e,!1)}Xr.dispatchEvent.call(e,e.elements.container,"statechange",!1,{code:t.data})}}})}}},ko={setup:function(){if(this.media)if(Xr.toggleClass(this.elements.container,this.config.classNames.type.replace("{0}",this.type),!0),Xr.toggleClass(this.elements.container,this.config.classNames.provider.replace("{0}",this.provider),!0),this.isEmbed&&Xr.toggleClass(this.elements.container,this.config.classNames.type.replace("{0}","video"),!0),this.isVideo&&(this.elements.wrapper=Xr.createElement("div",{class:this.config.classNames.video}),Xr.wrap(this.media,this.elements.wrapper),this.elements.poster=Xr.createElement("div",{class:this.config.classNames.poster}),this.elements.wrapper.appendChild(this.elements.poster)),this.isEmbed)switch(this.provider){case"youtube":Eo.setup.call(this);break;case"vimeo":vo.setup.call(this)}else this.isHTML5&&eo.extend.call(this);else this.debug.warn("No media element found!")}},To=function(){function e(t){var n=this;zr(this,e),this.player=t,this.publisherId=t.config.ads.publisherId,this.playing=!1,this.initialized=!1,this.elements={container:null,displayContainer:null},this.manager=null,this.loader=null,this.cuePoints=null,this.events={},this.safetyTimer=null,this.countdownTimer=null,this.managerPromise=new Promise(function(e,t){n.on("loaded",e),n.on("error",t)}),this.load()}return Gr(e,[{key:"load",value:function(){var e=this;this.enabled&&(Xr.is.object(window.google)&&Xr.is.object(window.google.ima)?this.ready():Xr.loadScript(this.player.config.urls.googleIMA.sdk).then(function(){e.ready()}).catch(function(){e.trigger("error",new Error("Google IMA SDK failed to load"))}))}},{key:"ready",value:function(){var e=this;this.startSafetyTimer(12e3,"ready()"),this.managerPromise.then(function(){e.clearSafetyTimer("onAdsManagerLoaded()")}),this.listeners(),this.setupIMA()}},{key:"setupIMA",value:function(){this.elements.container=Xr.createElement("div",{class:this.player.config.classNames.ads}),this.player.elements.container.appendChild(this.elements.container),google.ima.settings.setVpaidMode(google.ima.ImaSdkSettings.VpaidMode.ENABLED),google.ima.settings.setLocale(this.player.config.ads.language),this.elements.displayContainer=new google.ima.AdDisplayContainer(this.elements.container),this.requestAds()}},{key:"requestAds",value:function(){var e=this,t=this.player.elements.container;try{this.loader=new google.ima.AdsLoader(this.elements.displayContainer),this.loader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,function(t){return e.onAdsManagerLoaded(t)},!1),this.loader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,function(t){return e.onAdError(t)},!1);var n=new google.ima.AdsRequest;n.adTagUrl=this.tagUrl,n.linearAdSlotWidth=t.offsetWidth,n.linearAdSlotHeight=t.offsetHeight,n.nonLinearAdSlotWidth=t.offsetWidth,n.nonLinearAdSlotHeight=t.offsetHeight,n.forceNonLinearFullSlot=!1,n.setAdWillPlayMuted(!this.player.muted),this.loader.requestAds(n)}catch(e){this.onAdError(e)}}},{key:"pollCountdown",value:function(){var e=this;if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0]))return clearInterval(this.countdownTimer),void this.elements.container.removeAttribute("data-badge-text");this.countdownTimer=setInterval(function(){var t=Xr.formatTime(Math.max(e.manager.getRemainingTime(),0)),n=to("advertisement",e.player.config)+" - "+t;e.elements.container.setAttribute("data-badge-text",n)},100)}},{key:"onAdsManagerLoaded",value:function(e){var t=this,n=new google.ima.AdsRenderingSettings;n.restoreCustomPlaybackStateOnAdBreakComplete=!0,n.enablePreloading=!0,this.manager=e.getAdsManager(this.player,n),this.cuePoints=this.manager.getCuePoints(),Xr.is.empty(this.cuePoints)||this.cuePoints.forEach(function(e){if(0!==e&&-1!==e&&e<t.player.duration){var n=t.player.elements.progress;if(Xr.is.element(n)){var i=100/t.player.duration*e,r=Xr.createElement("span",{class:t.player.config.classNames.cues});r.style.left=i.toString()+"%",n.appendChild(r)}}}),this.manager.setVolume(this.player.volume),this.manager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,function(e){return t.onAdError(e)}),Object.keys(google.ima.AdEvent.Type).forEach(function(e){t.manager.addEventListener(google.ima.AdEvent.Type[e],function(e){return t.onAdEvent(e)})}),this.trigger("loaded")}},{key:"onAdEvent",value:function(e){var t=this,n=this.player.elements.container,i=e.getAd(),r=function(e){var n="ads"+e.replace(/_/g,"").toLowerCase();Xr.dispatchEvent.call(t.player,t.player.media,n)};switch(e.type){case google.ima.AdEvent.Type.LOADED:this.trigger("loaded"),r(e.type),this.pollCountdown(!0),i.isLinear()||(i.width=n.offsetWidth,i.height=n.offsetHeight);break;case google.ima.AdEvent.Type.ALL_ADS_COMPLETED:r(e.type),this.loadAds();break;case google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED:r(e.type),this.pauseContent();break;case google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED:r(e.type),this.pollCountdown(),this.resumeContent();break;case google.ima.AdEvent.Type.STARTED:case google.ima.AdEvent.Type.MIDPOINT:case google.ima.AdEvent.Type.COMPLETE:case google.ima.AdEvent.Type.IMPRESSION:case google.ima.AdEvent.Type.CLICK:r(e.type)}}},{key:"onAdError",value:function(e){this.cancel(),this.player.debug.warn("Ads error",e)}},{key:"listeners",value:function(){var e=this,t=this.player.elements.container,n=void 0;this.player.on("ended",function(){e.loader.contentComplete()}),this.player.on("seeking",function(){return n=e.player.currentTime}),this.player.on("seeked",function(){var t=e.player.currentTime;Xr.is.empty(e.cuePoints)||e.cuePoints.forEach(function(i,r){n<i&&i<t&&(e.manager.discardAdBreak(),e.cuePoints.splice(r,1))})}),window.addEventListener("resize",function(){e.manager&&e.manager.resize(t.offsetWidth,t.offsetHeight,google.ima.ViewMode.NORMAL)})}},{key:"play",value:function(){var e=this,t=this.player.elements.container;this.managerPromise||this.resumeContent(),this.managerPromise.then(function(){e.elements.displayContainer.initialize();try{e.initialized||(e.manager.init(t.offsetWidth,t.offsetHeight,google.ima.ViewMode.NORMAL),e.manager.start()),e.initialized=!0}catch(t){e.onAdError(t)}}).catch(function(){})}},{key:"resumeContent",value:function(){this.elements.container.style.zIndex="",this.playing=!1,this.player.currentTime<this.player.duration&&this.player.play()}},{key:"pauseContent",value:function(){this.elements.container.style.zIndex=3,this.playing=!0,this.player.pause()}},{key:"cancel",value:function(){this.initialized&&this.resumeContent(),this.trigger("error"),this.loadAds()}},{key:"loadAds",value:function(){var e=this;this.managerPromise.then(function(){e.manager&&e.manager.destroy(),e.managerPromise=new Promise(function(t){e.on("loaded",t),e.player.debug.log(e.manager)}),e.requestAds()}).catch(function(){})}},{key:"trigger",value:function(e){for(var t=this,n=arguments.length,i=Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];var o=this.events[e];Xr.is.array(o)&&o.forEach(function(e){Xr.is.function(e)&&e.apply(t,i)})}},{key:"on",value:function(e,t){return Xr.is.array(this.events[e])||(this.events[e]=[]),this.events[e].push(t),this}},{key:"startSafetyTimer",value:function(e,t){var n=this;this.player.debug.log("Safety timer invoked from: "+t),this.safetyTimer=setTimeout(function(){n.cancel(),n.clearSafetyTimer("startSafetyTimer()")},e)}},{key:"clearSafetyTimer",value:function(e){Xr.is.nullOrUndefined(this.safetyTimer)||(this.player.debug.log("Safety timer cleared from: "+e),clearTimeout(this.safetyTimer),this.safetyTimer=null)}},{key:"enabled",get:function(){return this.player.isVideo&&this.player.config.ads.enabled&&!Xr.is.empty(this.publisherId)}},{key:"tagUrl",get:function(){var e={AV_PUBLISHERID:"58c25bb0073ef448b1087ad6",AV_CHANNELID:"5a0458dc28a06145e4519d21",AV_URL:location.hostname,cb:Date.now(),AV_WIDTH:640,AV_HEIGHT:480,AV_CDIM2:this.publisherId};return"https://go.aniview.com/api/adserver6/vast/?"+Xr.buildUrlParams(e)}}]),e}(),So={insertElements:function(e,t){var n=this;Xr.is.string(t)?Xr.insertElement(e,this.media,{src:t}):Xr.is.array(t)&&t.forEach(function(t){Xr.insertElement(e,n.media,t)})},change:function(e){var t=this;Xr.is.object(e)&&"sources"in e&&e.sources.length?(eo.cancelRequests.call(this),this.destroy.call(this,function(){switch(t.options.quality=[],Xr.removeElement(t.media),t.media=null,Xr.is.element(t.elements.container)&&t.elements.container.removeAttribute("class"),t.type=e.type,t.provider=Xr.is.empty(e.sources[0].provider)?Jr.html5:e.sources[0].provider,t.supported=Zr.check(t.type,t.provider,t.config.playsinline),t.provider+":"+t.type){case"html5:video":t.media=Xr.createElement("video");break;case"html5:audio":t.media=Xr.createElement("audio");break;case"youtube:video":case"vimeo:video":t.media=Xr.createElement("div",{src:e.sources[0].src})}t.elements.container.appendChild(t.media),Xr.is.boolean(e.autoplay)&&(t.config.autoplay=e.autoplay),t.isHTML5&&(t.config.crossorigin&&t.media.setAttribute("crossorigin",""),t.config.autoplay&&t.media.setAttribute("autoplay",""),Xr.is.empty(e.poster)||(t.poster=e.poster),t.config.loop.active&&t.media.setAttribute("loop",""),t.config.muted&&t.media.setAttribute("muted",""),t.config.playsinline&&t.media.setAttribute("playsinline","")),po.addStyleHook.call(t),t.isHTML5&&So.insertElements.call(t,"source",e.sources),t.config.title=e.title,ko.setup.call(t),t.isHTML5&&("tracks"in e&&So.insertElements.call(t,"track",e.tracks),t.media.load()),(t.isHTML5||t.isEmbed&&!t.supported.ui)&&po.build.call(t),t.fullscreen.update()},!0)):this.debug.warn("Invalid source format")}},Ao=function(){function e(t,n){var i=this;if(zr(this,e),this.timers={},this.ready=!1,this.loading=!1,this.failed=!1,this.touch=Zr.touch,this.media=t,Xr.is.string(this.media)&&(this.media=document.querySelectorAll(this.media)),(window.jQuery&&this.media instanceof jQuery||Xr.is.nodeList(this.media)||Xr.is.array(this.media))&&(this.media=this.media[0]),this.config=Xr.extend({},so,e.defaults,n||{},function(){try{return JSON.parse(i.media.getAttribute("data-plyr-config"))}catch(e){return{}}}()),this.elements={container:null,buttons:{},display:{},progress:{},inputs:{},settings:{menu:null,panes:{},tabs:{}},captions:null},this.captions={active:null,currentTrack:-1,meta:new WeakMap},this.fullscreen={active:!1},this.options={speed:[],quality:[]},this.debug=new ao(this.config.debug),this.debug.log("Config",this.config),this.debug.log("Support",Zr),!Xr.is.nullOrUndefined(this.media)&&Xr.is.element(this.media))if(this.media.plyr)this.debug.warn("Target already setup");else if(this.config.enabled)if(Zr.check().api){var r=this.media.cloneNode(!0);r.autoplay=!1,this.elements.original=r;var o=this.media.tagName.toLowerCase(),a=null,s=null,l=null;switch(o){case"div":if(a=this.media.querySelector("iframe"),Xr.is.element(a)){if(s=a.getAttribute("src"),this.provider=Xr.getProviderByUrl(s),this.elements.container=this.media,this.media=a,this.elements.container.className="",l=Xr.getUrlParams(s),!Xr.is.empty(l)){var c=["1","true"];c.includes(l.autoplay)&&(this.config.autoplay=!0),c.includes(l.loop)&&(this.config.loop.active=!0),this.isYouTube?this.config.playsinline=c.includes(l.playsinline):this.config.playsinline=!0}}else this.provider=this.media.getAttribute(this.config.attributes.embed.provider),this.media.removeAttribute(this.config.attributes.embed.provider);if(Xr.is.empty(this.provider)||!Object.keys(Jr).includes(this.provider))return void this.debug.error("Setup failed: Invalid provider");this.type=$r.video;break;case"video":case"audio":this.type=o,this.provider=Jr.html5,this.media.hasAttribute("crossorigin")&&(this.config.crossorigin=!0),this.media.hasAttribute("autoplay")&&(this.config.autoplay=!0),this.media.hasAttribute("playsinline")&&(this.config.playsinline=!0),this.media.hasAttribute("muted")&&(this.config.muted=!0),this.media.hasAttribute("loop")&&(this.config.loop.active=!0);break;default:return void this.debug.error("Setup failed: unsupported type")}this.supported=Zr.check(this.type,this.provider,this.config.playsinline),this.supported.api?(this.listeners=new mo(this),this.storage=new Qr(this),this.media.plyr=this,Xr.is.element(this.elements.container)||(this.elements.container=Xr.createElement("div"),Xr.wrap(this.media,this.elements.container)),this.elements.container.setAttribute("tabindex",0),po.addStyleHook.call(this),ko.setup.call(this),this.config.debug&&Xr.on(this.elements.container,this.config.events.join(" "),function(e){i.debug.log("event: "+e.type)}),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&po.build.call(this),this.listeners.container(),this.listeners.global(),this.fullscreen=new fo(this),this.ads=new To(this),this.config.autoplay&&this.play()):this.debug.error("Setup failed: no support")}else this.debug.error("Setup failed: no support");else this.debug.error("Setup failed: disabled by config");else this.debug.error("Setup failed: no suitable element passed")}return Gr(e,[{key:"play",value:function(){return Xr.is.function(this.media.play)?this.media.play():null}},{key:"pause",value:function(){this.playing&&Xr.is.function(this.media.pause)&&this.media.pause()}},{key:"togglePlay",value:function(e){(Xr.is.boolean(e)?e:!this.playing)?this.play():this.pause()}},{key:"stop",value:function(){this.isHTML5?(this.pause(),this.restart()):Xr.is.function(this.media.stop)&&this.media.stop()}},{key:"restart",value:function(){this.currentTime=0}},{key:"rewind",value:function(e){this.currentTime=this.currentTime-(Xr.is.number(e)?e:this.config.seekTime)}},{key:"forward",value:function(e){this.currentTime=this.currentTime+(Xr.is.number(e)?e:this.config.seekTime)}},{key:"increaseVolume",value:function(e){var t=this.media.muted?0:this.volume;this.volume=t+(Xr.is.number(e)?e:1)}},{key:"decreaseVolume",value:function(e){var t=this.media.muted?0:this.volume;this.volume=t-(Xr.is.number(e)?e:1)}},{key:"toggleCaptions",value:function(e){if(this.supported.ui){var t=Xr.is.boolean(e)?e:!this.elements.container.classList.contains(this.config.classNames.captions.active);Xr.toggleState(this.elements.buttons.captions,t),Xr.toggleClass(this.elements.container,this.config.classNames.captions.active,t),t!==this.captions.active&&(this.captions.active=t,Xr.dispatchEvent.call(this,this.media,this.captions.active?"captionsenabled":"captionsdisabled"))}}},{key:"airplay",value:function(){Zr.airplay&&this.media.webkitShowPlaybackTargetPicker()}},{key:"toggleControls",value:function(e){if(this.supported.ui&&!this.isAudio){var t=Xr.hasClass(this.elements.container,this.config.classNames.hideControls),n=void 0===e?void 0:!e,i=Xr.toggleClass(this.elements.container,this.config.classNames.hideControls,n);if(i&&this.config.controls.includes("settings")&&!Xr.is.empty(this.config.settings)&&io.toggleMenu.call(this,!1),i!==t){var r=i?"controlshidden":"controlsshown";Xr.dispatchEvent.call(this,this.media,r)}return!i}return!1}},{key:"on",value:function(e,t){Xr.on(this.elements.container,e,t)}},{key:"off",value:function(e,t){Xr.off(this.elements.container,e,t)}},{key:"destroy",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.ready){var i=function(){document.body.style.overflow="",t.embed=null,n?(Object.keys(t.elements).length&&(Xr.removeElement(t.elements.buttons.play),Xr.removeElement(t.elements.captions),Xr.removeElement(t.elements.controls),Xr.removeElement(t.elements.wrapper),t.elements.buttons.play=null,t.elements.captions=null,t.elements.controls=null,t.elements.wrapper=null),Xr.is.function(e)&&e()):(t.listeners.clear(),Xr.replaceElement(t.elements.original,t.elements.container),Xr.dispatchEvent.call(t,t.elements.original,"destroyed",!0),Xr.is.function(e)&&e.call(t.elements.original),t.ready=!1,setTimeout(function(){t.elements=null,t.media=null},200))};switch(this.stop(),this.provider+":"+this.type){case"html5:video":case"html5:audio":clearTimeout(this.timers.loading),po.toggleNativeControls.call(this,!0),i();break;case"youtube:video":clearInterval(this.timers.buffering),clearInterval(this.timers.playing),null!==this.embed&&Xr.is.function(this.embed.destroy)&&this.embed.destroy(),i();break;case"vimeo:video":null!==this.embed&&this.embed.unload().then(i),setTimeout(i,200)}}}},{key:"supports",value:function(e){return Zr.mime.call(this,e)}},{key:"isHTML5",get:function(){return Boolean(this.provider===Jr.html5)}},{key:"isEmbed",get:function(){return Boolean(this.isYouTube||this.isVimeo)}},{key:"isYouTube",get:function(){return Boolean(this.provider===Jr.youtube)}},{key:"isVimeo",get:function(){return Boolean(this.provider===Jr.vimeo)}},{key:"isVideo",get:function(){return Boolean(this.type===$r.video)}},{key:"isAudio",get:function(){return Boolean(this.type===$r.audio)}},{key:"playing",get:function(){return Boolean(this.ready&&!this.paused&&!this.ended)}},{key:"paused",get:function(){return Boolean(this.media.paused)}},{key:"stopped",get:function(){return Boolean(this.paused&&0===this.currentTime)}},{key:"ended",get:function(){return Boolean(this.media.ended)}},{key:"currentTime",set:function(e){if(this.duration){var t=Xr.is.number(e)&&e>0;this.media.currentTime=t?Math.min(e,this.duration):0,this.debug.log("Seeking to "+this.currentTime+" seconds")}},get:function(){return Number(this.media.currentTime)}},{key:"buffered",get:function(){var e=this.media.buffered;return Xr.is.number(e)?e:e&&e.length&&this.duration>0?e.end(0)/this.duration:0}},{key:"seeking",get:function(){return Boolean(this.media.seeking)}},{key:"duration",get:function(){var e=parseFloat(this.config.duration),t=(this.media||{}).duration||0;return e||t}},{key:"volume",set:function(e){var t=e;Xr.is.string(t)&&(t=Number(t)),Xr.is.number(t)||(t=this.storage.get("volume")),Xr.is.number(t)||(t=this.config.volume),t>1&&(t=1),t<0&&(t=0),this.config.volume=t,this.media.volume=t,!Xr.is.empty(e)&&this.muted&&t>0&&(this.muted=!1)},get:function(){return Number(this.media.volume)}},{key:"muted",set:function(e){var t=e;Xr.is.boolean(t)||(t=this.storage.get("muted")),Xr.is.boolean(t)||(t=this.config.muted),this.config.muted=t,this.media.muted=t},get:function(){return Boolean(this.media.muted)}},{key:"hasAudio",get:function(){return!this.isHTML5||(!!this.isAudio||(Boolean(this.media.mozHasAudio)||Boolean(this.media.webkitAudioDecodedByteCount)||Boolean(this.media.audioTracks&&this.media.audioTracks.length)))}},{key:"speed",set:function(e){var t=null;Xr.is.number(e)&&(t=e),Xr.is.number(t)||(t=this.storage.get("speed")),Xr.is.number(t)||(t=this.config.speed.selected),t<.1&&(t=.1),t>2&&(t=2),this.config.speed.options.includes(t)?(this.config.speed.selected=t,this.media.playbackRate=t):this.debug.warn("Unsupported speed ("+t+")")},get:function(){return Number(this.media.playbackRate)}},{key:"quality",set:function(e){var t=null;if(Xr.is.empty(e)||(t=Number(e)),Xr.is.number(t)||(t=this.storage.get("quality")),Xr.is.number(t)||(t=this.config.quality.selected),Xr.is.number(t)||(t=this.config.quality.default),this.options.quality.length){if(!this.options.quality.includes(t)){var n=Xr.closest(this.options.quality,t);this.debug.warn("Unsupported quality option: "+t+", using "+n+" instead"),t=n}this.config.quality.selected=t,this.media.quality=t}},get:function(){return this.media.quality}},{key:"loop",set:function(e){var t=Xr.is.boolean(e)?e:this.config.loop.active;this.config.loop.active=t,this.media.loop=t},get:function(){return Boolean(this.media.loop)}},{key:"source",set:function(e){So.change.call(this,e)},get:function(){return this.media.currentSrc}},{key:"poster",set:function(e){this.isVideo?po.setPoster.call(this,e):this.debug.warn("Poster can only be set for video")},get:function(){return this.isVideo?this.media.getAttribute("poster"):null}},{key:"autoplay",set:function(e){var t=Xr.is.boolean(e)?e:this.config.autoplay;this.config.autoplay=t},get:function(){return Boolean(this.config.autoplay)}},{key:"currentTrack",set:function(e){ro.set.call(this,e)},get:function(){var e=this.captions,t=e.active,n=e.currentTrack;return t?n:-1}},{key:"language",set:function(e){ro.setLanguage.call(this,e)},get:function(){return(ro.getCurrentTrack.call(this)||{}).language}},{key:"pip",set:function(e){var t="picture-in-picture",n="inline";if(Zr.pip){var i=Xr.is.boolean(e)?e:this.pip===n;this.media.webkitSetPresentationMode(i?t:n)}},get:function(){return Zr.pip?this.media.webkitPresentationMode:null}}],[{key:"supported",value:function(e,t,n){return Zr.check(e,t,n)}},{key:"loadSprite",value:function(e,t){return Xr.loadSprite(e,t)}},{key:"setup",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;return Xr.is.string(t)?i=Array.from(document.querySelectorAll(t)):Xr.is.nodeList(t)?i=Array.from(t):Xr.is.array(t)&&(i=t.filter(Xr.is.element)),Xr.is.empty(i)?null:i.map(function(t){return new e(t,n)})}}]),e}();return Ao.defaults=Xr.cloneDeep(so),Ao});
//# sourceMappingURL=plyr.polyfilled.js.map
