<?php

namespace App\Http\Livewire;

use App\Models\ExchangRate;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Region;
class RateFormLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction;
    public $title = "";
    public $file_name = 'region';
    public $rate;
    protected $listeners = ['Region-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
    protected $rules = [
        "rate.rate"=>'required|numeric',
        "rate.type"=>'required',


    ];
    public function mount($id =null)
    {
        $this->title = \Lang::get('lang.add_account')  ;
        $this->rate  = $id?ExchangRate::find($id):new ExchangRate();
    }
    public function render()
    {
        //if(in_array('region_create',$this->actions_permission()) ){
            return view('dashboard/rate/form')->extends('dashboard_layout.main');
    }

    public function save(){
        $this->validate();
        \DB::beginTransaction();
        try {
            $this->rate->save();
            \DB::commit();
            $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
            return redirect()->route('dashboard.rate');
        } catch (\Exception $e) {
            \DB::rollback();
            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
        }
    }
}

