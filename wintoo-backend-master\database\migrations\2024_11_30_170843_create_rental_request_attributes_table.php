<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRentalRequestAttributesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('rental_request_attributes', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('value')->nullable()->comment('title');
			$table->integer('request_id')->nullable()->comment('product_id');
			$table->integer('attribute_id')->nullable()->comment('attribute_id');
			$table->text('payload')->nullable()->comment('payload');
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('rental_request_attributes');
	}

}
