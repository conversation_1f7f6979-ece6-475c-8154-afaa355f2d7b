<?php

namespace App\Http\Resources;

use App\Models\Favorite;
use App\Models\Follower;
use App\Models\ProductRequest;
use App\Models\Reviews;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class JobRequestSimilarResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


        $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');
        return [
            "id"=>$this->id,
            "image"=>$this->user_image?$this->user_image_url:$this->work_image_url,

            "user_image_url"=>$this->user_image?$this->user_image_url:null,
            "work_image_url"=>$this->work_image?$this->work_image_url:null,
            "offer_age_from"=>$this->offer_age_from,
            "offer_age_to"=>$this->offer_age_to,
            "gender"=>$this->gender,
            "contact_email"=>$this->contact_email,
            "contact_number"=>$this->contact_number,

            "job_type"=>new GeneralResource($this->job_type),

            "age"=>$this->type=="offer"?($this->offer_age_from." - ".$this->offer_age_to):$this->age,
            "gender_name"=>$this->gender_name,

            "customer_id"=>$this->customer_id,
            "customer"=>new CustomerResource($this->customer),

            "store_id"=>$this->store_id,
            "store"=>new StoreResource($this->store),


            "type"=>$this->type,
            "type_name"=>$this->type_name,

            "exp_years"=>$this->type=="offer"?$this->offer_exp_years_from." - ".$this->offer_exp_years_to:$this->exp_years,
            "offer_exp_years_from"=>$this->offer_exp_years_from,
            "offer_exp_years_to"=>$this->offer_exp_years_to,

            "salary"=>$this->salary,
            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource($this->currency),

            "created_at"=>$created_at,

            'country'=>new AreaResource($this->country),
            'governorate'=>new AreaResource($this->governorate),
            'city'=>new AreaResource($this->city),
            'region'=>new AreaResource($this->region),
            "full_address"=> $this->full_address,
            'target_country'=>new AreaResource($this->country),
            'target_governorate'=>new AreaResource($this->governorate),
            'target_city'=>new AreaResource($this->city),
            'target_region'=>new AreaResource($this->region),
            "target_full_address"=> $this->target_full_address,

        ];
    }
}
