<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMomenTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('momen', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('full_name')->nullable()->comment('الاسم كامل');
			$table->string('phone', 191)->nullable()->comment('رقم الهاتف');
			$table->string('address', 191)->nullable()->comment('عنوان ');
			$table->integer('category_id')->nullable()->comment('التصنيف');
			$table->boolean('status')->nullable()->comment('الحالة');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('momen');
	}

}
