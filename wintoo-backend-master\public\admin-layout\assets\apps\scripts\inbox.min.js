var AppInbox=function(){var t=$(".inbox-content"),e="",o=function(o,n){var i="app_inbox_inbox.html",a=o.attr("data-title");e=n,App.blockUI({target:t,overlayColor:"none",animate:!0}),p(o),$.ajax({type:"GET",cache:!1,url:i,dataType:"html",success:function(e){p(o),App.unblockUI(".inbox-content"),$(".inbox-nav > li.active").removeClass("active"),o.closest("li").addClass("active"),$(".inbox-header > h1").text(a),t.html(e),Layout.fixContentHeight&&Layout.fixContentHeight()},error:function(t,e,n){p(o)},async:!1}),jQuery("body").on("change",".mail-group-checkbox",function(){var t=jQuery(".mail-checkbox"),e=jQuery(this).is(":checked");jQuery(t).each(function(){$(this).prop("checked",e)})})},n=function(e,o,n){var i="app_inbox_view.html";App.blockUI({target:t,overlayColor:"none",animate:!0}),p(e);var a=e.parent("tr").attr("data-messageid");$.ajax({type:"GET",cache:!1,url:i,dataType:"html",data:{message_id:a},success:function(o){App.unblockUI(t),p(e),n&&$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("View Message"),t.html(o),Layout.fixContentHeight()},error:function(t,o,n){p(e)},async:!1})},i=function(){$(".inbox-wysihtml5").wysihtml5({stylesheets:["../assets/global/plugins/bootstrap-wysihtml5/wysiwyg-color.css"]})},a=function(){$("#fileupload").fileupload({url:"../assets/global/plugins/jquery-file-upload/server/php/",autoUpload:!0}),$.support.cors&&$.ajax({url:"../assets/global/plugins/jquery-file-upload/server/php/",type:"HEAD"}).fail(function(){$('<span class="alert alert-error"/>').text("Upload server currently unavailable - "+new Date).appendTo("#fileupload")})},c=function(e){var o="app_inbox_compose.html";App.blockUI({target:t,overlayColor:"none",animate:!0}),p(e),$.ajax({type:"GET",cache:!1,url:o,dataType:"html",success:function(o){App.unblockUI(t),p(e),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Compose"),t.html(o),a(),i(),$(".inbox-wysihtml5").focus(),Layout.fixContentHeight()},error:function(t,o,n){p(e)},async:!1})},l=function(e){var o=($(e).attr("data-messageid"),"app_inbox_reply.html");App.blockUI({target:t,overlayColor:"none",animate:!0}),p(e),$.ajax({type:"GET",cache:!1,url:o,dataType:"html",success:function(o){App.unblockUI(t),p(e),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Reply"),t.html(o),$('[name="message"]').val($("#reply_email_content_body").html()),s(),a(),i(),Layout.fixContentHeight()},error:function(t,o,n){p(e)},async:!1})},s=function(){var t=$(".inbox-compose .mail-to .inbox-cc"),e=$(".inbox-compose .input-cc");t.hide(),e.show(),$(".close",e).click(function(){e.hide(),t.show()})},r=function(){var t=$(".inbox-compose .mail-to .inbox-bcc"),e=$(".inbox-compose .input-bcc");t.hide(),e.show(),$(".close",e).click(function(){e.hide(),t.show()})},p=function(t){"undefined"!=typeof t&&(t.attr("disabled")?t.attr("disabled",!1):t.attr("disabled",!0))};return{init:function(){$(".inbox").on("click",".compose-btn",function(){c($(this))}),$(".inbox").on("click",".inbox-discard-btn",function(t){t.preventDefault(),o($(this),e)}),$(".inbox").on("click",".reply-btn",function(){l($(this))}),$(".inbox").on("click",".view-message",function(){n($(this))}),$(".inbox-nav > li > a").click(function(){o($(this),"inbox")}),$(".inbox-content").on("click",".mail-to .inbox-cc",function(){s()}),$(".inbox-content").on("click",".mail-to .inbox-bcc",function(){r()}),"view"===App.getURLParameter("a")?n():"compose"===App.getURLParameter("a")?c():$(".inbox-nav > li:first > a").click()}}}();jQuery(document).ready(function(){AppInbox.init()});