<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class FaqResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
       $res =  [] ;//parent::toArray($request);
        $res['title'] = $this->title ;
        $res['description'] = $this->description ;
        $res['video_url'] = $this->video_url ;
        $res['media_thumbnail_url'] = $this->media_thumbnail_url ;
        return  $res ;
    }
}
