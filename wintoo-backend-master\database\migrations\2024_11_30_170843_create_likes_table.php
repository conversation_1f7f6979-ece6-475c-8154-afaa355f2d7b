<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLikesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('likes', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('likeable_id')->nullable()->comment('likeable_id');
			$table->string('likeable_type', 191)->nullable()->comment('likeable_type');
			$table->integer('customer_id')->nullable()->comment('customer_id');
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('likes');
	}

}
