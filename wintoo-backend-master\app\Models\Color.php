<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Color extends Model
{
use \App\Traits\PropertyGetter;

     protected $guarded = [];
     public  static function  getColumnLang(){
    $columes=[
    'name'=>[\Lang::get('color.name') ,1,false,false,[]],
    'color_ar'=>[\Lang::get('color.color_ar') ,1,true,false,[]],
    'code'=>[\Lang::get('color.code') ,1,true,false,[]],
       'actions'=>['الخيارات',2,true,false,['edit','delete']],
    ];
     return $columes;
}
     public static function getSearchable(){
    $columes=['name'=>[\Lang::get('color.name')],
    'color_ar'=>[\Lang::get('color.color_ar')],

     ]; return $columes;
}
     public function scopeSearch($query, $data) {
             if(isset($data["name"])){
               $query->where("name","LIKE","%".$data["name"]."%");}
             if(isset($data["color_ar"])){
               $query->where("color_ar","LIKE","%".$data["color_ar"]."%");}
             if(isset($data["code"])){
               $query->where("code","LIKE","%".$data["code"]."%");}
             return $query ;
     }


     public function mainColor(){
         return $this->belongsTo(Maincolor::class,'main_color_id');
     }

}
