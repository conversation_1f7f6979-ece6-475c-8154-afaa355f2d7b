!function(o,e){var n=e(".alert-validation"),a=/^[0-9]+$/,r=e(".alert-validation-msg"),c=e(".accordion"),l=(e(".collapse-title"),e(".collapse-hover-title")),t=e(".dropdown-icon-wrapper .dropdown-item");n.on("input",function(){n.val().match(a)?r.css("display","none"):r.css("display","block")}),e(o).on("keyup",function(o){39==o.which?e('.carousel[data-keyboard="true"]').carousel("next"):37==o.which&&e('.carousel[data-keyboard="true"]').carousel("prev")}),c.attr("data-toggle-hover","true")&&l.closest(".card").on("mouseenter",function(){e(this).children(".collapse").collapse("show")}),e(".accordion-shadow .collapse-header .card-header").on("click",function(){var o=e(this);o.parent().siblings(".collapse-header.open").removeClass("open"),o.parent(".collapse-header").toggleClass("open")}),t.on("click",function(){e(".dropdown-icon-wrapper .dropdown-toggle i").remove(),e(this).find("i").clone().appendTo(".dropdown-icon-wrapper .dropdown-toggle"),e(".dropdown-icon-wrapper .dropdown-toggle .dropdown-item").removeClass("dropdown-item")}),e(".chip-closeable").on("click",function(){e(this).closest(".chip").remove()})}((window,document),jQuery);