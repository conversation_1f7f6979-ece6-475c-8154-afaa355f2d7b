<?php

namespace App\Models;

use App\Services\VideoEdit;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
use \App\Traits\PropertyGetter;
use \App\Traits\OrderByNewestRandomly;


    protected $guarded = [];
    protected $appends = ['media_url',"media_thumbnail_url","new_image_url"];
    protected $fillable = ['description', 'status', 'media', 'customer_id', 'type','store_id', 'like_count'];

    public static $HOT_SALE_TYPE = "hot_sale" ;
    public const REELS_TYPE = "reels";

    public static $NORMAL_TYPE = "normal" ;

    public static $VIDEOS_TYPE = "videos" ;

    public static $IMAGES_TYPE = "images" ;




    public  static function  getColumnLang(){
 	 	$columes=[
            'new_image_url'=>[\Lang::get('post.media') ,1,true,false, ['type'=>'image','actions'=>null] ],
 	 	   'description'=>[\Lang::get('post.description') ,1,true,false, ['type'=>'longText','actions'=>null] ],


 	 	//'media_type'=>[\Lang::get('post.media_type') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'status'=>[\Lang::get('store.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
 	 	   'actions'=>['الخيارات',3,true,false,['type'=>'button','actions'=>['edit','delete']]],

            'start_date'=>[\Lang::get('post.start_date') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'end_date'=>[\Lang::get('post.end_date') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	];
 	 	 return $columes;
  }
    public static function getSearchable(){
 	 	$columes=['description'=>[\Lang::get('post.description')],
 	 	'media'=>[\Lang::get('post.media')],
 	 	'media_type'=>[\Lang::get('post.media_type')],
 	 	'status'=>[\Lang::get('post.status')],
            'start_date'=>[\Lang::get('post.start_date')],
            'end_date'=>[\Lang::get('post.end_date')],

 	 	 ]; return $columes;
  }

    public function scopeIsActive($query) {
        $endtDate = Carbon::today();

        $query->where("status","published");
    }

    public function scopeIsHotSale($query) {
        $query->where("type",self::$HOT_SALE_TYPE);
    }

    public function scopeIsNotReels($query) {
        $query->where("type","!=",self::REELS_TYPE)->orWhereNull("type");
    }

    public function scopeIsReels($query) {
        $query->where("type",self::REELS_TYPE);
    }

    public function scopeIsNotHotSale($query) {
       // $query->whereNull("type");
        $query->where("type","!=",self::$HOT_SALE_TYPE)->orWhereNull("type");

    }


    public function scopeSearch($query, $data) {
        if(isset($data["store_id"])){
            $query->where("store_id",$data["store_id"]);
        }
 	 	 if(isset($data["description"])){
 	 	   $query->where("description","LIKE","%".$data["description"]."%");}
 	 	 if(isset($data["media"])){
 	 	   $query->where("media","LIKE","%".$data["media"]."%");}
 	 	 if(isset($data["media_type"])){
 	 	   $query->where("media_type","LIKE","%".$data["media_type"]."%");}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}

           if(isset($data["start_date"])){
            $query->where("start_date","LIKE","%".$data["start_date"]."%");}
         if(isset($data["end_date"])){
            $query->where("end_date","LIKE","%".$data["end_date"]."%");}
 	 	 return $query ;
 	 	 }
    public function getMediaUrlAttribute(){
        return $this->media?asset('storage/'.$this->media):null;

        // return $this->media ? env('AWS_URL'). '/'.ltrim($this->media, '/') : null;
       // return  $this->media?("https://d759yjlbemjei.cloudfront.net".'/storage/'.$this->media):null;
        //  return $this->last_media?asset('storage/'.$this->last_media):null;
    }
    public function getMediaThumbnailUrlAttribute(){


     /*   if ($this->media_thumbnail) {
            $ext = pathinfo($this->media, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS'];

            if (in_array($ext, $videoExtension)) {
                $this->media_thumbnail = VideoEdit::generateVideoThumbnail($this->media, 0);
            }
        }*/

    //    return $this->media_thumbnail?asset('storage/'.$this->media_thumbnail):null;
        return $this->media_thumbnail?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->media_thumbnail]):null;

    }

    public function getNewImageUrlAttribute(){
        if ($this->media_thumbnail){
            return $this->media_thumbnail_url;
        }
        return $this->media_url;
    }
    public function store(){
        return $this->belongsTo(Store::class,"store_id");
    }
    public function customer(){
        return $this->belongsTo(Customer::class,"customer_id");
    }
    public function post_media(){
        return $this->hasMany(PostMedia::class,"post_id")->orderBy("id" , "desc");
    }

    public function like(){
        return $this->morphMany(Like::class, 'likeable');
    }

    public function comments(){
        return $this->hasMany(Comment::class, 'post_id');
    }

    public function media()
    {
        return $this->hasMany(PostMedia::class);
    }
}
