<?php

namespace App\Http\Resources;

use App\Models\Coupon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderTrackerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


        return [
            'id'=>$this->id,
            'status'=>$this->status,
            'status_name'=>$this->status_name,
            'date'=>\Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'time'=>\Carbon\Carbon::parse($this->created_at)->isoFormat('h:mm a'),

        ];//parent::toArray($request);
    }
}
