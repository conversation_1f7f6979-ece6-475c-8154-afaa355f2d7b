<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCouponsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('coupons', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('coupon_code', 191)->unique();
			$table->string('coupon_value', 191)->nullable();
			$table->enum('coupon_type', array('fixed','percentage'))->nullable();
			$table->date('expired_date')->nullable();
			$table->date('used_date')->nullable();
			$table->string('coupon_status', 191)->nullable()->default('available');
			$table->bigInteger('customer_id')->unsigned()->nullable();
			$table->timestamps();
			$table->string('number_of_used')->nullable();
			$table->string('min_value', 191)->nullable();
			$table->string('max_value', 191)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('coupons');
	}

}
