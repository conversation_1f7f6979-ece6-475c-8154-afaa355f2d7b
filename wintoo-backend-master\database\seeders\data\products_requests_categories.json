[{"id": "76", "icon": null, "name": "{\"ar\":\"\\u0633\\u064a\\u0627\\u0631\\u0629\",\"en\":\"Car\"}", "order": "0", "created_at": "2023-05-16 11:41:12", "updated_at": "2024-11-14 07:13:23", "status": "1", "type": "product_request"}, {"id": "79", "icon": null, "name": "{\"ar\":\"\\u0641\\u0646\\u062f\\u0642 \",\"en\":\"Hotel\"}", "order": "1", "created_at": "2023-05-16 11:41:12", "updated_at": "2024-11-14 07:15:10", "status": "1", "type": "product_request"}, {"id": "82", "icon": null, "name": "{\"ar\":\"\\u0645\\u062d\\u0637\\u0629 \\u063a\\u0627\\u0632\",\"en\":\"gas station\"}", "order": "0", "created_at": "2023-05-16 11:41:12", "updated_at": "2023-05-16 15:43:48", "status": "1", "type": "product_request"}, {"id": "85", "icon": null, "name": "{\"ar\":\"\\u0645\\u062d\\u0644 \\u062a\\u062c\\u0627\\u0631\\u064a\",\"en\":\"Market\"}", "order": "0", "created_at": "2023-05-16 11:41:12", "updated_at": "2024-11-14 07:13:45", "status": "1", "type": "product_request"}, {"id": "94", "icon": null, "name": "{\"he\":\"\\u05d0\\u05d3\\u05de\\u05d4\",\"tr\":\"Yer\",\"en\":\"land\",\"ar\":\"\\u0627\\u0631\\u0636\"}", "order": "2", "created_at": "2023-05-21 10:56:00", "updated_at": "2023-06-19 13:27:53", "status": "1", "type": "product_request"}, {"id": "96", "icon": null, "name": "{\"tr\":\"Apartman\",\"he\":\"\\u05d3\\u05d9\\u05e8\\u05d4\",\"en\":\"Apartman\",\"ar\":\"\\u0634\\u0642\\u0629\"}", "order": "4", "created_at": "2023-05-21 11:21:51", "updated_at": "2024-11-14 07:14:47", "status": "1", "type": "product_request"}, {"id": "97", "icon": null, "name": "{\"he\":\"\\u05de\\u05b7\\u05d7\\u05e1\\u05b8\\u05df\",\"en\":\"Warehouse\",\"tr\":\"depo\",\"ar\":\"\\u0645\\u062e\\u0632\\u0646\"}", "order": "5", "created_at": "2023-05-21 11:31:28", "updated_at": "2024-11-14 07:13:09", "status": "1", "type": "product_request"}, {"id": "99", "icon": null, "name": "{\"tr\":\"ev\",\"he\":\"\\u05d1\\u05b7\\u05bc\\u05d9\\u05b4\\u05ea\",\"en\":\"House\",\"ar\":\"\\u0645\\u0646\\u0632\\u0644\"}", "order": "0", "created_at": "2023-06-16 14:01:51", "updated_at": "2023-06-16 14:01:51", "status": "1", "type": "product_request"}, {"id": "100", "icon": null, "name": "{\"he\":\"\\u05d1\\u05e0\\u05d9\\u05d9\\u05df \",\"tr\":\"bina\",\"en\":\"Building \",\"ar\":\"\\u0639\\u0645\\u0627\\u0631\\u0629\"}", "order": "0", "created_at": "2023-06-16 14:03:19", "updated_at": "2024-11-14 07:14:14", "status": "1", "type": "product_request"}, {"id": "104", "icon": null, "name": "{\"tr\":\"<PERSON><PERSON>\",\"he\":\"\\u05d3\\u05d9\\u05e8\\u05d4\",\"en\":\"Apartment\",\"ar\":\"\\u0634\\u0642\\u0629\"}", "order": "0", "created_at": "2023-08-23 15:32:42", "updated_at": "2023-08-23 15:32:42", "status": "1", "type": "rental_request"}, {"id": "105", "icon": null, "name": "{\"he\":\"\\u05de\\u05db\\u05d5\\u05e0\\u05d9\\u05ea \",\"tr\":\"<PERSON><PERSON>\",\"en\":\"Car\",\"ar\":\"\\u0633\\u064a\\u0627\\u0631\\u0629\"}", "order": "0", "created_at": "2023-08-23 15:35:12", "updated_at": "2023-08-23 15:35:12", "status": "1", "type": "rental_request"}, {"id": "106", "icon": null, "name": "{\"tr\":\"<PERSON><PERSON><PERSON> ma\\u011faza\",\"en\":\"shop\",\"he\":\"\\u05d7\\u05e0\\u05d5\\u05ea \\u05de\\u05e1\\u05d7\\u05e8\\u05d9\\u05ea\",\"ar\":\"\\u0645\\u062d\\u0644 \\u062a\\u062c\\u0627\\u0631\\u064a\"}", "order": "0", "created_at": "2023-08-23 15:36:41", "updated_at": "2023-08-23 15:36:41", "status": "1", "type": "rental_request"}, {"id": "107", "icon": null, "name": "{\"tr\":\"<PERSON><PERSON>\",\"en\":\"Warehouse\",\"he\":\"\\u05de\\u05d7\\u05e1\\u05df\",\"ar\":\"\\u0645\\u062e\\u0632\\u0646\"}", "order": "0", "created_at": "2023-08-23 15:38:34", "updated_at": "2023-08-23 15:38:34", "status": "1", "type": "rental_request"}, {"id": "108", "icon": null, "name": "{\"tr\":\"<PERSON><PERSON><PERSON>\",\"en\":\"Wedding dress\",\"he\":\"\\u05de\\u05e1\\u05d3\\u05e8\\u05ea \\u05db\\u05dc\\u05d4\",\"ar\":\"\\u0628\\u062f\\u0644\\u0629 \\u0639\\u0631\\u0648\\u0633\"}", "order": "0", "created_at": "2023-08-23 15:40:43", "updated_at": "2023-08-23 15:40:43", "status": "1", "type": "rental_request"}, {"id": "109", "icon": null, "name": "{\"tr\":\"<PERSON>tel odas\\u0131\",\"he\":\"\\u05d7\\u05d3\\u05e8 \\u05d1\\u05de\\u05dc\\u05d5\\u05df\",\"en\":\"Hotel room\",\"ar\":\"\\u063a\\u0631\\u0641\\u0629 \\u0641\\u064a \\u0641\\u0646\\u062f\\u0642\"}", "order": "0", "created_at": "2023-08-23 15:42:07", "updated_at": "2023-08-23 15:42:07", "status": "1", "type": "rental_request"}, {"id": "110", "icon": null, "name": "{\"tr\":\"<PERSON><PERSON>\",\"en\":\"<PERSON>\",\"he\":\"\\u05de\\u05e9\\u05e8\\u05d3\",\"ar\":\"\\u0645\\u0643\\u062a\\u0628\"}", "order": "0", "created_at": "2023-08-23 15:43:05", "updated_at": "2023-08-23 15:43:05", "status": "1", "type": "rental_request"}]