<?php

namespace App\Http\Resources;

use App\Models\Category;
use App\Models\Color;
use App\Models\Currency;
use App\Models\Favorite;
use App\Models\Maincolor;
use App\Models\Offer;
use App\Models\Reviews;
use App\Models\SubCategory;
use App\Models\Variation;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductStoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $product_reviews_user_count=$this->reviews?$this->reviews->count():0;

        $product_reviews=$this->reviews?$this->reviews->avg('rating'):0;

           $variation=Variation::where('product_id',$this->id)->get();
           $variation_colors_id=Variation::where('product_id',$this->id)->pluck('product_color_id');
            $colors=Color::whereIn('id',$variation_colors_id)->get();



        $colors_array = $this->getColorName();
        $this->currency =Currency::find($this->currency_id);
        return [
            'id'=>$this->id,
            'title'=>$this->title,
            "price"=> getRound($this->price),
            "is_available"=> $this->stock_quantity >0?1:0,
            "stock_quantity"=> (string)((int)$this->stock_quantity) ,
            "description"=> $this->description,
            "main_category"=> new CategoryResource($this->main_category),
            "sub_category"=> new CategoryResource($this->sub_category),
            "category"=> new CategoryResource($this->category),
            "brand"=> $this->brand,
            "is_new"=> (bool)$this->is_new,
            "created_at"=>$this->created_at,
            "updated_at"=> $this->created_at,
           // 'is_offer'=>$this->new_price?true:false,
            'is_offer'=>$this->offer_id && ($this->new_price?true:false),

            "offer" => $this->offer ,
            "offer_discount_type" => $this->offer_discount_type ,
            "percentage_value" => $this->percentage_value ,
            "new_price"=>getRound($this->new_price),

            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource(Currency::find($this->currency_id)),
            "charged_price"=> getRound(getPriceForUser($this->price,$this->currency)),
            "charged_new_price"=>getRound(getPriceForUser($this->new_price,$this->currency)),
            'charged_currency'=> new CurrencyResource(getCurrentUserCurrency()),

            'offer_discount_rate'=> $this->offer_discount_rate,
            "image_url"=> $this->product_images()->where('is_main',true)->first()->image_url,
            "product_images"=>$this->product_images?$this->product_images()->where('is_main',false)->get():null,
            'variation'=>VariationResource::collection($variation),
           'color'=>$colors?ColorVariationResource::collection($colors)===null?ColorVariationResource::collection($colors):[]:[],
           'colors_or_size'=>$colors?$this->getColorName():[],
           'identifier'=>$this->identifier,
           'identifier_name'=>$this->identifier_name,
           'is_hotsale'=>(boolean)$this->is_hotsale,

        ];
    }
    public function  getColorName(){

        $variation=Variation::where('is_in_stock','Yes')
            ->where('product_id',$this->id)
            ->where('status',1)->get();

        $has_colors = true ;

        if($this->product_type !='store_jan_product'){

            $variation_colors_id=Variation::where('is_in_stock','Yes')
                ->where('product_id',$this->id)->where('status',1)->pluck('product_color_id');
            $colors=Maincolor::whereIn('id',$variation_colors_id)->get();

        }
        else{
            $variation_colors_id=Variation::where('is_in_stock','Yes')
                ->where('product_id',$this->id)->where('status',1)->pluck('product_color');


            $colors_count=Color::whereIn('name',$variation_colors_id)->get();


            $has_colors = $colors_count > 0 ;

            if($colors_count->count()==0){
                $colors=Maincolor::where('id',23)->get();
            }else{
                $colors=Color::whereIn('name',$variation_colors_id)->get();
            }
        }

        if($colors){
            $result=[];
            foreach ($colors as $color){
                if($this->product_type =='store_jan_product'){

                    if ($color->id ==23 )
                        $size=Variation::where('is_in_stock','Yes')->where('product_id',$this->id)->where(function ($query) use ($color) {

                            $query->where('product_color','LIKE',$color->name )->orWhereNull('product_color');

                        })->get();
                    else
                        $size=Variation::where('is_in_stock','Yes')->where('product_id',$this->id)->where('product_color','LIKE',$color->name)->get();

                }else{
                    $size=Variation::where('is_in_stock','Yes')
                        ->where('product_id',$this->id)
                        ->where('product_color_id',$color->id)
                        ->get();
                }

                //dd($size);
                $result[]=[
                    'id' => $color->id,
                    'color' => $color->color_ar,
                    'human_color' => $this->checkOfHumanColor($color),//Maincolor::find($color->id)->human_name,//$color->human_name ? Maincolor::find($color->main_color_id)->human_name: '',
                    'code' => $color->code,
                    'size' => SizeVariationResource::collection($size),
                ];

            }
            return $result;

        }

    }


    public function checkOfHumanColor($color){

        if(get_class($color) =="App\Models\Color"){

            return Maincolor::find($color->main_color_id)->human_name;
        }else{
            return Maincolor::find($color->id)->human_name;
        }
    }

    public function colorsCheck($colorArray){

        if (count($colorArray) == 0){
            return false;
        }elseif (count($colorArray) == 1 && $colorArray[0]['id'] == 23){

            return false;


        }
        return true;
    }





}
