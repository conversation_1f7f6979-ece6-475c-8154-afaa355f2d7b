<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAddressesSchema extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('addresses', function (Blueprint $table) {
            // Rename country_id to id
            $table->renameColumn('address', 'email');

            // Drop city_id, region_id, and governorate_id
            $table->dropColumn(['city_id', 'regoin_id', 'governorate_id']);

            // Add new fields
            $table->string('fullname')->nullable();
            $table->string('phone')->nullable();
            $table->string('addressline1')->nullable();
            $table->string('addressline2')->nullable();
            $table->string('zipcode')->nullable();

            // Add new text fields for city, region, and governorate
            $table->string('city')->nullable();
            $table->string('state')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('addresses', function (Blueprint $table) {
            // Revert country_id back to its original name
            $table->renameColumn('email', 'address');

            // Re-add city_id, region_id, and governorate_id
            $table->integer('city_id')->nullable();
            $table->integer('regoin_id')->nullable();
            $table->integer('governorate_id')->nullable();

            // Drop the new fields
            $table->dropColumn(['addressline1', 'addressline2', 'zipcode', 'city', 'fullname', 'state', 'phone']);
        });
    }
}
