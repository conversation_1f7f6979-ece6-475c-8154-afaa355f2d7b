<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
      //  dd($this->variation->product_color_id);

        return [
            'id'=>$this->id,
            'product'=>new ProductCartResource($this->product),
            'qty'=>$this->qty,
            'variation'=>new VariationResource($this->variation),
            "has_colors"=>optional($this->variation)->product_color_id == 23 ?false:true,
        ];

       // return parent::toArray($request);
    }

    public function colorsCheck($colorArray){

        if (count($colorArray) == 0){
            return false;
        }elseif (count($colorArray) == 1 && $colorArray[0]['id'] == 23){

            return false;


        }
        return true;
    }
}
