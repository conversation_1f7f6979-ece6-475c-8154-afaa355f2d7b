<?php

namespace App\Services;

use Facade\FlareClient\Report;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\Color;
use App\Models\Brand;
use App\Models\Size;
use App\Models\Maincolor;
use App\Models\Variation;
use App\Models\Order;
use App\Models\Transaction;

class StorejanProduct
{


    public function main($products){
        try {

        file_put_contents('StoreJanServiceDataLog.log',json_encode($products).'\n',FILE_APPEND);

            foreach ($products['products'] as $product){

                if($product['status'] ==='removed'){
                    $product_id=Product::where('store_jan_product_id',$product['name'])->first();
                    if($product_id){
                        $product_id->update([
                            'store_jan_status'=>$product['status'],
                            'status'=>0
                        ]);
                    }
                    continue;
                }
                $brand = $this->createBrand($product);
                $this->createSize($product['sizes']);
                $this->createColors($product['colors']);

                $createOrUpdateProduct=   $this->createProduct($product,$brand);
                $this->createVariation($product,$createOrUpdateProduct);
                $this->createImage($product,$createOrUpdateProduct);

                if($createOrUpdateProduct->store_jan_status  != "disabled"){
                    $this->checkVariationQuantityForProduct($createOrUpdateProduct->id);
                }
            }


        } catch (\Exception $e) {
            file_put_contents('StoreJanServiceErrorLog.log',print_r($e->getMessage(),true).'\n',FILE_APPEND);
            return $e->getMessage();
        }

    }

    public function createBrand($product)
    {
        $brand =  Brand::firstOrCreate([
            'name'=>$product['brand']['name_ar']
        ]);

        return $brand;
    }

    public function createSize($sizes){
        foreach ($sizes as $size_key=> $size) {
            $find_size= Size::where('name',$size['name'])->first();
            if($find_size){
                $size_new=$find_size;
            }else{
                $size_new=  Size::firstOrCreate([
                    'name' => $size['name'],
                    'size_ar' => $size['size_ar'],
                ]);
            }
        }
    }

    public function createColors($colors){
        foreach ($colors as $color_key=> $color) {

            if(empty($color['code'])){
                $near_color='without color';

                $main_color=  Maincolor::find(23);
            }else{
                $near_color=getNearestColor($color['code']);

                $main_color= Maincolor::where('name',$near_color)->first();
            }


//                if($main_color){
//                    $new_color=$main_color;
//                }else{
            $new_color=  Color::firstOrCreate([
                'name' => $color['name'],
            ]);
            $new_color->update([
                'color_ar' => $color['color_ar'],
                'code' => $color['code'],
                'human_name' =>$near_color,
                'main_color_id' =>$main_color->id,
            ]);
//                }

        }
    }


    public function createProduct($product,$brands){
        $product_id=Product::where('store_jan_product_id',$product['name'])->first();

        if($product_id){
            Product::where('store_jan_product_id',$product['name'])->update([
                'store_jan_product_id'=>$product['name'],
                'product_type'=>'store_jan_product',
                'title'=>$product['name_ar'],
                'currency'=>$product['currency'],
                'product_code'=>$product['product_code'],
                'sku'=>$product['sku'],
                'stock_quantity'=>$product['stock_quantity'],
                'description'=>$product['description_html_ar'],
                'variant_selling_price'=>$product['variant_selling_price'],
                'selling_price'=>$product['selling_price'],
                'display_currency'=>$product['display_currency'],
                'display_symbol'=>$product['display_symbol'],
                'total_quantity'=>$product['total_quantity'],
                'vendor'=>$product['vendor'],
                'sync_status'=>$product['sync_status'],
                'specific_sale_price'=>$product['specific_sale_price'],
                'display_selling_price'=>$product['display_selling_price'],
                'price'=>$product['display_selling_price'],
                'store_selling_price'=>$product['store_selling_price'],
                'tags'=>json_encode($product['tags']),
                'brand_id'=>$brands->id,
                'store_jan_status'=>$product['status']

            ]);
            $this->fixPriceWithStorjanUpdatedProduct($product_id->id);
        } else{

            $product_id=Product::create([
                'store_jan_product_id'=>$product['name'],
                'product_type'=>'store_jan_product',
                'title'=>$product['name_ar'],
                'currency'=>$product['currency'],
                'product_code'=>$product['product_code'],
                'sku'=>$product['sku'],
                'stock_quantity'=>$product['stock_quantity'],
                'description'=>$product['description_html_ar'],
                'variant_selling_price'=>$product['variant_selling_price'],
                'selling_price'=>$product['selling_price'],
                'display_currency'=>$product['display_currency'],
                'display_symbol'=>$product['display_symbol'],
                'total_quantity'=>$product['total_quantity'],
                'vendor'=>$product['vendor'],
                'sync_status'=>$product['sync_status'],
                'specific_sale_price'=>$product['specific_sale_price'],
                'display_selling_price'=>$product['display_selling_price'],
                'price'=>$product['display_selling_price'],
                'store_selling_price'=>$product['store_selling_price'],
                'tags'=>json_encode($product['tags']),
                'brand_id'=>$brands->id,
                'store_jan_status'=>$product['status']

            ]);
        }
        return $product_id;
    }


    public function fixPriceWithStorjanUpdatedProduct($id){
        $product = Product::find($id);

        if($product->profit_type === 'fixed'){
            $product->profit_price = $product->price+$product->profit_amount;
            $product->profit_amount=$product->profit_amount;

        }
        if($product->profit_type === 'percentage'){

            $profit_amount =($product * ($product->product->profit_amount / 100));
            $product->profit_price=$product->price+($product->price * ($product->profit_amount / 100));
        }

        if($product->offer_discount_type === 'fixed'){
//                $this->product->new_price= $this->product->price-$this->product->percentage_value;
//                $this->product->discount_amount=$this->product->percentage_value;
            $product->new_price= $product->profit_price-$product->percentage_value;
            $product->discount_amount=$product->percentage_value;

        }
        if($product->offer_discount_type === 'percentage'){
            $product->discount_amount=($product->profit_price * ($product->percentage_value / 100));
            $product->new_price=$product->profit_price-($product->profit_price * ($product->percentage_value / 100));
        }

        $product->save();
    }


    public function createVariation($product,$product_id){
         Variation::where('product_id',$product_id->id)->update([
            'status'=>0,
        ]);


        // create variations
        foreach ($product['variations'] as $key=>$variations) {

            // find variation
            $find_variation= Variation::where('name' ,$variations['name'])->first();
            $size_new=  Size::where('name','LIKE','%'.$variations['product_size'].'%')->first();
            if($find_variation){
                $new_color =  Color::where('name','LIKE','%'.$variations['product_color'].'%')->first();


                 Variation::where('name' ,$variations['name'])->update([
                    'product_id' =>$product_id->id,
                    'variation_id' =>$key,
                    'name' =>$variations['name'],
                    'is_in_stock' =>$variations['is_in_stock'],
                    'product_size' =>$variations['product_size'],
                    'product_size_id' =>$size_new->id,
                    'product_color' =>$variations['product_color'],
                    'product_color_id' =>is_null($variations['product_color'])?23:$new_color->main_color_id,
                    'stock_code' =>$variations['stock_code'],
                    'currency' =>$variations['currency'],
                    'selling_price' =>$variations['selling_price'],
                    'vat_included' =>$variations['vat_included'],
                    'vat_percent' =>$variations['vat_percent'],
                    'stock_quantity' =>$variations['stock_quantity'],
                    'specific_sale_price' =>$variations['specific_sale_price'],
                    'display_selling_price' =>$variations['display_selling_price'],
                    'store_selling_price' =>$variations['store_selling_price'],
                    'display_symbol' =>$variations['display_symbol'],
                    'display_currency' =>$variations['display_currency'],
                    'size_en' =>$variations['size_en'],
                    'store_jan_product_id' =>$product['name'],
                    'status' =>1,
                ]);
                $this->fixPriceWithForVariation($find_variation->id);
            }else{

                $new_color =  Color::where('name','LIKE','%'.$variations['product_color'].'%')->first();
                $variation =  Variation::create([
                    'product_id' =>$product_id->id,
                    'variation_id' =>$key,
                    'name' =>$variations['name'],
                    'is_in_stock' =>$variations['is_in_stock'],
                    'product_size' =>$variations['product_size'],
                    'product_size_id' =>$size_new->id,
                    'product_color' =>$variations['product_color'],
                    'product_color_id' =>is_null($variations['product_color'])?23:$new_color->main_color_id,
                    'stock_code' =>$variations['stock_code'],
                    'currency' =>$variations['currency'],
                    'selling_price' =>$variations['selling_price'],
                    'vat_included' =>$variations['vat_included'],
                    'vat_percent' =>$variations['vat_percent'],
                    'stock_quantity' =>$variations['stock_quantity'],
                    'specific_sale_price' =>$variations['specific_sale_price'],
                    'display_selling_price' =>$variations['display_selling_price'],
                    'store_selling_price' =>$variations['store_selling_price'],
                    'display_symbol' =>$variations['display_symbol'],
                    'display_currency' =>$variations['display_currency'],
                    'size_en' =>$variations['size_en'],
                    'store_jan_product_id' =>$product['name'],
                    'status' =>1,
                ]);

                if($product_id->offer_discount_type === 'fixed'){

                    $variation->price_after_offer= $variation->display_selling_price - $product_id->percentage_value;
                    $variation->save();

                }
                if($product_id->offer_discount_type === 'percentage'){
                    $variation->price_after_offer=$variation->display_selling_price -($variation->display_selling_price * ($product_id->percentage_value / 100));
                    $variation->save();
                }
            }



        }
    }


    public function fixPriceWithForVariation($id){
        $variation =  Variation::find($id);
        $product = Product::find($variation->product_id);

        if($product->profit_type === 'fixed'){
            $profit_amount=$product->profit_amount;
            $profit_price=$variation['display_selling_price']+$product->profit_amount;
            $profit_type='fixed';
        }
        if($product->profit_type === 'percentage'){
            $profit_amount=($variation['display_selling_price'] * ($product->profit_amount / 100));
            $profit_price=$variation['display_selling_price']+($variation['display_selling_price']* ($product->profit_amount / 100));
            $profit_type='percentage';
        }

        if($product->offer_discount_type === 'fixed'){

            $variation_price_after_offer= $profit_price- $product->percentage_value;
            $variation_offer_type='fixed';
            $offer_price=$product->percentage_value;
        }
        if($product->offer_discount_type === 'percentage'){

            $variation_price_after_offer=$profit_price-($profit_price * ($product->percentage_value / 100));
            $variation_offer_type='percentage';
            $offer_price=$profit_price * ($product->percentage_value / 100);
        }

        $variation->update([
            'offer_type'=>$variation_offer_type,
            'price_after_offer'=>$variation_price_after_offer,
            'offer_price'=>$offer_price,
            'profit_type'=>$profit_type,
            'profit_price'=>$profit_price,
            'profit_amount'=>$profit_amount]);
    }


    public function createImage($product,$product_id){
        foreach ($product['images'] as $image_key=>$image) {

            $url =$image['image_url'];

            $url = mb_convert_encoding($url, 'HTML-ENTITIES', "UTF-8");
            $source = file_get_contents($url);
            $extension = pathinfo($image['image_url'], PATHINFO_EXTENSION); // get ext from path
            $filename  = pathinfo($image['image_url'], PATHINFO_FILENAME);
            $destinationPath = storage_path() . '/app/product';
            file_put_contents($destinationPath.'/'.$filename.'.'.$extension, $source);
            $cv_file_filename =$filename.'.'.$extension;


            ProductImage::firstOrCreate([
                'store_jan_product_id' => $image['parent'],
                'product_id' => $product_id->id,
                'name' => $image['name'],
                'image' => $cv_file_filename,
                'is_main'=>$image['image_url']===$product['main_image']?1:0
            ]);

        }
    }

    public function checkVariationQuantityForProduct($id){

        $product = Product::find($id);
        $variations_sum  = $product->variations->sum('stock_quantity');
        if($variations_sum == 0){
            $product->status = 0;
            $product->is_available = 0;
            $product->save();
        }else{

            $product->status = 1;

            $product->is_available = 1;

            $product->save();
        }

    }


    public function updateOrderStatus($orders){
        try {
//            file_put_contents('StoreJanServicUpdateOrderStatus.log', print_r($orders, true) . '\n', FILE_APPEND);
            foreach ($orders as $key => $data) {

                $order =  Order::find($key);
                foreach ($data as $status) {
                     Transaction::updateOrCreate([
                        'order_id' => $order->id,
                        'from_status' => $status['from'],
                        'to_status' => $status['to'],
                        'action_taked_at' => $status['action_taked_at'],
                    ]);
                    $to_status = $status['to'];
                    $to_action_taked_at = $status['action_taked_at'];

                    if ($to_status == 'cancelled') {
                        $order->store_jan_status = -1;
                    }
                    if ($to_status == 'pending') {
                        $order->store_jan_status = 0;
                    }
                    if ($to_status == 'in_progress') {
                        $order->store_jan_status = 1;
                    }
                    if ($to_status == 'picked_up') {
                        $order->store_jan_status = 2;
                    }
                    if ($to_status == 'shipped') {
                        $order->store_jan_status = 3;
                    }
                    if ($to_status == 'delivered') {
                        $order->store_jan_status = 4;
                    }

                }
                $order->save();
            }
            return $order;
        }catch (\Exception $e) {
                file_put_contents('StoreJanServicUpdateOrderStatusError.log',print_r($e->getMessage(),true).'\n',FILE_APPEND);
            }
    }



}