<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubCategoriesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('sub_categories', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('parent_id')->unsigned()->nullable();
			$table->integer('main_category_id')->unsigned()->nullable();
			$table->text('name');
			$table->string('image', 191)->nullable();
			$table->boolean('status')->nullable()->default(1)->comment('status');
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('sub_categories');
	}

}
