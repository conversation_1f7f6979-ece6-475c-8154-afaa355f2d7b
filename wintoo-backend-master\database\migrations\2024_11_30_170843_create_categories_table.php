<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCategoriesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('categories', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('parent_id')->unsigned()->nullable();
			$table->text('name');
			$table->timestamps();
			$table->string('image', 191)->nullable();
			$table->string('banner')->nullable();
			$table->boolean('status')->nullable()->default(1)->comment('status');
			$table->float('is_main', 10, 0)->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('categories');
	}

}
