<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reviews extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected  $guarded=[];
    protected $casts = [
        'rating' => 'float',
    ];
    public function customer()
    {
        return $this->belongsTo(Customer::class,'customer_id');
    }



    public function products()
    {
        return $this->belongsTo(Product::class,'product_id');
    }

    public  function setstatus(){

        $this->status =!$this->status;
        $this->save();

    }

}
