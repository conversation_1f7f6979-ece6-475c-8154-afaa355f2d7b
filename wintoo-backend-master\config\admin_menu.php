<?php return array (
    'home' =>
        array (
            'name' => 'الرئيسية',
            'icon' => "feather icon-home",
            'url' => 'dashboard',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
   "address"=>array(
        'name' => 'الدول والمحافظات والمناطق',
        'icon' => "feather icon-map-pin",
        'url' => '#',
        'order' => 1,
        'sub' =>
            array (
                'Country' =>
                    array (
                        'name' => 'country.model_name',
                        'icon' => "feather icon-map-pin",
                        'url' => 'dashboard.country',
                        'order' => 9,
                        'sub' =>
                            array (
                            ),
                    ),
                "governorate"=>array(
                    'name' => 'governorate.model_name',
                    'icon' => "feather icon-map-pin",
                    'url' => 'dashboard.governorate',
                    'order' => 1,
                    'sub' =>array()
                ),
                "city"=>array(
                    'name' => 'city.model_name',
                    'icon' => "feather icon-map-pin",
                    'url' => 'dashboard.city',
                    'order' => 1,
                    'sub' =>array()
                ),
                "region"=>array(
                    'name' => 'region.model_name',
                    'icon' => "feather icon-map-pin",
                    'url' => 'dashboard.region',
                    'order' => 1,
                    'sub' =>array()
                ),
            ),
    ),
   'gallary_banner' =>
        array (
            'name' => 'البنارات الاعلانية',
            'icon' => "fa fa-file-image-o",
            'url' => 'dashboard.gallary_banner',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
   'sliders' =>
        array (
            'name' => 'السلايدر',
            'icon' => "feather icon-image",
            'url' => 'dashboard.sliders',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
   "storeCategory"=>array(
       'name' => 'تصنيف المتاجر',
       'icon' => "feather icon-image",
       'url' => 'dashboard.storeCategory',
       'order' => 1,
       'sub' =>
           array (
           ),
   ),
    "jobType"=>array(
       'name' => 'أنواع الوظائف',
       'icon' => "feather icon-image",
       'url' => 'dashboard.jobType',
       'order' => 1,
       'sub' =>
           array (
           ),
   ),
    "store"=>array(
        'name' => 'المتاجر',
        'icon' => "fa fa-store",
        'url' => 'dashboard.store',
        'order' => 1,
        'sub' =>
            array (
            ),
    ),
   'brands' =>
        array (
            'name' => 'العلامات التجارية',
            'icon' => "feather icon-bookmark",
            'url' => 'dashboard.brands',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'sponsor' =>
        array (
            'name' => 'sponsor.model_name',
            'icon' => "fa fa-award",
            'url' => 'dashboard.sponsor',
            'order' => 5,
            'sub' =>
                array (
                ),
        ),
    'systemDraw' =>
        array (
            'name' => 'systemDraw.model_name',
            'icon' => "fa fa-award",
            'url' => 'dashboard.systemDraw',
            'order' => 3,
            'sub' =>
                array (
                ),
        ),
    'offer' =>
        array (
            'name' => 'offer.model_name',
            'icon' => "fa fa-award",
            'url' => 'dashboard.offer',
            'order' => 6,
            'sub' =>
                array (
                ),
        ),
    'categories' =>
        array (
            'name' => 'تصنيفات',
            'icon' => "fa fa-database",
            'url' => '#',
            'order' => 1,
            'sub' =>
                array (
                    'categories' =>
                        array (
                            'name' => 'التصنيفات الرئيسية',
                            'icon' => "",
                            'url' => 'dashboard.categories',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                    'sub_category' =>
                        array (
                            'name' => 'التصنيف الفرعي من التصنيف الرئيسي',
                            'icon' => "",
                            'url' => 'dashboard.sub_category',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                    'sub_sub_category' =>
                        array (
                            'name' => 'التصنيف الفرعي من التصنيف الفرعي',
                            'icon' => "",
                            'url' => 'dashboard.sub_sub_category',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                ),
        ),
    'maincolor' =>
        array (
//            'name' => 'إرسال رسائل إلى المستخدمين',
//            'icon' => "feather icon-bookmark",
            'name' => 'إرسال رسائل إلى المستخدمين',
            'icon' => "feather icon-send",
            'url' => 'dashboard.maincolor',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'size' =>
        array (
            'name' => 'المقاسات',
            'icon' => "feather icon-bookmark",
            'url' => 'dashboard.size',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'products' =>
        array (
            'name' => 'المنتجات',
            'icon' => "feather icon-box",
            'url' => 'dashboard.products',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    "productRequestCategory"=>array(
        'name' => 'تصنيف طلبات المنتج',
        'icon' => "feather icon-image",
        'url' => 'dashboard.productsRequestsCategory',
        'order' => 1,
        'sub' =>
            array (
            ),
    ),
    "rentalRequestCategory"=>array(
        'name' => 'تصنيف طلبات الايجار',
        'icon' => "feather icon-image",
        'url' => 'dashboard.rentalRequestsCategory',
        'order' => 1,
        'sub' =>
            array (
            ),
    ),
    'productRequest' =>
        array (
            'name' => 'طلب المنتج',
            'icon' => "feather icon-box",
            'url' => 'dashboard.productRequest',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'customer' =>
        array (
            'name' => 'customer.model_name',
            'icon' => "feather icon-users",
            'url' => 'dashboard.customer',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
//    'coupon' =>
//        array (
//            'name' => 'coupon.model_name',
//            'icon' => "feather icon-dollar-sign",
//            'url' => 'dashboard.coupon',
//            'order' => 1,
//            'sub' =>
//                array (
//                ),
//        ),
//    'promo_coupon' =>
//        array (
//            'name' => 'الكابونات الدعائية',
//            'icon' => "feather icon-dollar-sign",
//            'url' => 'dashboard.promo_coupon',
//            'order' => 1,
//            'sub' =>
//                array (
//                ),
//        ),
    'order' =>
        array (
            'name' => 'order.model_name',
            'icon' => "feather icon-truck",
            'url' => 'dashboard.order',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'notification' =>
        array (
            'name' => 'الاشعارات',
            'icon' => "feather icon-bell",
            'url' => 'dashboard.notification',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'notification_list' =>
        array (
            'name' => ' سجل الاشعارات',
            'icon' => "feather icon-bell",
            'url' => 'dashboard.notification_list',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'page' =>
        array (
            'name' => 'الصفحات',
            'icon' => "feather icon-file",
            'url' => 'dashboard.page',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'faq' =>
        array (
            'name' => 'faq.model_name',
            'icon' => "feather icon-help-circle",
            'url' => 'dashboard.faq',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'join_black' =>
        array (
            'name' => 'طلبات الاشتراك في البلاك فريدي',
            'icon' => "feather icon-help-circle",
            'url' => 'dashboard.join_black',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'contact_us' =>
        array (
            'name' => 'الرسائل',
            'icon' => "feather icon-message-circle",
            'url' => 'dashboard.ContactUS',
            'order' => 1,
            'sub' =>
                array (
                ),
        ),
    'settings' =>
        array (
            'name' => 'الاعدادات العامة',
            'icon' => "feather icon-settings",
            'url' => '#',
            'order' => 1,
            'sub' =>
                array (
                    'settings' =>
                        array (
                            'name' => 'الاعدادات العامة',
                            'icon' => "feather icon-settings",
                            'url' => 'dashboard.settings',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                    'role' =>
                        array (
                            'name' => 'صلاحيات',
                            'icon' => "fa fa-cogs",
                            'url' => 'dashboard.role',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                    'user_management' =>
                        array (
                            'name' => 'ادارة مستخدمين الادارة',
                            'icon' => "feather icon-users",
                            'url' => 'dashboard.user_management',
                            'order' => 1,
                            'sub' =>
                                array (
                                ),
                        ),
                ),
        ),
    "currency"=>array(
        'name' => 'العملات',
        'icon' => "feather icon-image",
        'url' => 'dashboard.currency',
        'sub' =>
            array (
            ),
    ),


);
