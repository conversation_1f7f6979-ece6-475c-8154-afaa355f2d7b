<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSlidersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('sliders', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('image', 191)->nullable();
			$table->string('link', 191)->nullable();
			$table->text('description')->nullable();
			$table->bigInteger('order')->nullable();
			$table->integer('is_main')->nullable()->default(1);
			$table->timestamps();
			$table->text('type')->nullable();
			$table->text('product_or_category_id')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('sliders');
	}

}
