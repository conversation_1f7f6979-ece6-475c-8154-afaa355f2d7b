<?php

namespace App\Services;

use App\Models\DrawCandidate;
use Illuminate\Support\Facades\Log;

class CometChat{

    public $APP_ID = "262589f02521225d";
    public $REGION = "us";
    public $VERSION = "v3";
    public $AUTHKEY = "f8f294307477bbbc4f3ab5d4aa028fbec900bd4e";
    
    public function create_user($user, $update = false, $uid = null){

        $url = 'https://'.$this->APP_ID.'.api-'.$this->REGION.'.cometchat.io/'.$this->VERSION.'/users'.($update ? "/{$uid}" : "");
        $data = [
            'uid' => $uid,
            'name' => $user->name ? $user->name : $user->username,
            'avatar' => $user->image_url,
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        if ($update) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        }

        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'apiKey:'.$this->AUTHKEY,
            'Content-Type:application/json',
            'Accept:application/json'
        ]);

        $response = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $response = json_decode($response, true);

        // Log the request and response for debugging
        Log::channel('telescope')->info('CometChat API Request', [
            'url' => $url,
            'data' => $data,
            'response' => $response,
            'httpcode' => $httpcode
        ]);

        if ($httpcode == 200) {
            $user->is_chat_register = true;
            $this->create_user_auth($user, $uid);
            $user->save();
        } else {
            // Handle the error response
            Log::channel('telescope')->error('CometChat API Error', [
                'url' => $url,
                'data' => $data,
                'response' => $response,
                'httpcode' => $httpcode
            ]);
        }

        curl_close($ch);
    }

    public function create_user_auth($user, $uid){
        $url = 'https://'.$this->APP_ID.'.api-'.$this->REGION.'.cometchat.io/'.$this->VERSION.'/users/'.$uid.'/auth_tokens';
        $data = [
            'force' => true,
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'apiKey:'.$this->AUTHKEY,
            'Content-Type:application/json',
            'Accept:application/json'
        ]);

        $response = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $response = json_decode($response, true);

        // Log the request and response for debugging
        Log::channel('telescope')->info('CometChat Auth API Request', [
            'url' => $url,
            'data' => $data,
            'response' => $response,
            'httpcode' => $httpcode
        ]);

        if ($httpcode == 200) {
            $user->chat_auth = $response['data']['authToken'];
            $user->save();
        } else {
            // Handle the error response
            Log::channel('telescope')->error('CometChat Auth API Error', [
                'url' => $url,
                'data' => $data,
                'response' => $response,
                'httpcode' => $httpcode
            ]);
        }

        curl_close($ch);
    }

}

?>
