<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Governorate;
class GovernorateFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'governorate';
      public $governorate;
       protected $rules = [

               "governorate.name"=>'required',
               "governorate.name.ar"=>'required',
               "governorate.name.tr"=>'required',
               "governorate.name.en"=>'required',
               "governorate.name.he"=>'required',

                "governorate.country_id"=>'nullable',
                "governorate.status"=>'nullable',
                "governorate.olivery_area_code"=>'nullable',
       ];
    protected $validationAttributes;
    public function __construct($id = null)
    {
        parent::__construct($id);
        $this->validationAttributes = $this->getColNameForValidation(Governorate::getColumnLang());
    }

    public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->governorate  = $id?Governorate::find($id):new Governorate();
          }
      public function render()
          {
              if(in_array('governorate_create',$this->actions_permission()) ) {
                  return view('dashboard/governorate/form')->extends('dashboard_layout.main');
              }else{
                  return view('dashboard.not-authorized')->extends('dashboard_layout.main');
              }


          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->governorate->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.governorate');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


