<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJobsRequestsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('jobs_requests', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('customer_id', 191)->nullable();
			$table->string('store_id', 191)->nullable();
			$table->string('name', 191)->nullable();
			$table->string('user_image', 191)->nullable();
			$table->string('work_image', 191)->nullable();
			$table->string('age', 191)->nullable();
			$table->string('offer_age_from', 191)->nullable();
			$table->string('offer_age_to', 191)->nullable();
			$table->string('gender', 191)->nullable();
			$table->string('contact_email', 191)->nullable();
			$table->string('contact_number', 191)->nullable();
			$table->string('exp_years', 191)->nullable();
			$table->string('offer_exp_years_from', 191)->nullable();
			$table->string('offer_exp_years_to', 191)->nullable();
			$table->string('salary', 191)->nullable();
			$table->string('currency_id', 191)->nullable();
			$table->string('type', 191)->nullable();
			$table->integer('job_type_id')->nullable();
			$table->integer('country_id')->nullable();
			$table->integer('city_id')->nullable();
			$table->integer('region_id')->nullable();
			$table->integer('governorate_id')->nullable();
			$table->integer('target_country_id')->nullable();
			$table->integer('target_city_id')->nullable();
			$table->integer('target_region_id')->nullable();
			$table->integer('target_governorate_id')->nullable();
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('jobs_requests');
	}

}
