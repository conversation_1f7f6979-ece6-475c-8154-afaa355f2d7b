<?php

namespace Database\Factories;

use App\Models\Governorate;
use Illuminate\Database\Eloquent\Factories\Factory;

class GovernorateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Governorate::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
//        Faker\Factory::create('');
       // $random_number = Governorate::latest()->first();
        $random_number =random_int(0,9999);
        return [
            'name'=>"{$random_number}محافظة_",
            'status'=>$this->faker->randomElement([1,0]),
        ];
    }
}
