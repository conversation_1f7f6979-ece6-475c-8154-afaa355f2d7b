<?php

namespace App\Http\Livewire;

use App\Models\Country;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\City;
class CountryLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Country-livewire:conformDelete' => 'conformDelete'];
        public $file_name = 'country';
        public function mount()
            {
                $searchable = Country::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Country::getColumnLang();
                $this->searchable =Country::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =Country::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
               if(in_array('country_create',$this->actions_permission()) ){
                     return view('dashboard/country/index',[ 'data'=>$data])->extends('dashboard_layout.main');
               }else{
                   return view('dashboard.not-authorized')->extends('dashboard_layout.main');
               }
           }

        public function search(){ $this->resetPage();}
         public function resetSearch(){
            $this->search_array=[];
         }

            public function edit($id){
                 return redirect()->route('dashboard.country.edit',$id);
             }

             public function delete($id){
                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'City-livewire:conformDelete',['id'=>$id]);
             }

             public function conformDelete($id){

                     $county= Country::find($id['id']);

                 if(in_array('country_delete',$this->actions_permission())) {
                     if ($county->city->isEmpty()) {

                         Country::find($id['id'])->delete();;

                         $this->showModal(\Lang::get('lang.saved_done'), \Lang::get('lang.saved_changed'), 'success');
                     } else {
                         $this->showModal('لا يمكن الحذف', 'لا يمكن حذف المدينة بسبب وجود مناطق مرتبطة بها', 'error');
                     }
                 }else{
                     return view('dashboard.not-authorized')->extends('dashboard_layout.main');
                 }

             }


             public function setStatus(){
                    
             }
}

