<?php

namespace App\Http\Livewire;

use App\Models\Brand;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
class BrandFormLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction;
    public $title = "";
    public $image = null;
    public $selected= [];
    public $brands=[
        'name'=>'',
        'order'=>'',
        'status'=>true,
        'image'=>'https://www.gravatar.com/avatar/d41d8cd98f00b204e9800998ecf8427e',
    ];

    public function mount($id =null)
    {
        $this->title = \Lang::get('lang.add_brand')  ;
        $brand =   Brand::find($id);
        $this->brands   = $id?Brand::find($id)->toArray():$this->brands ;

        if($id){
            $this->brands['image']=$brand->brandUrl();
            $this->title = \Lang::get('lang.edit_brand')  ;
        }

    }

    public function render()
    {
        if(in_array('brands_create'||'brands_edit',$this->actions_permission()) ) {
            return view('dashboard.brands.form')->extends('dashboard_layout.main');
        }else{
            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }

    }

    public function save(){
        $this->validate(
            [
                'brands.name' => 'required',
                'brands.order' => 'required|numeric|unique:sliders,order',
            ]);

        $filename = $this->image?$this->image->store('/','brand'):null;

        $brand = Brand::create([
            'name'=>$this->brands['name'],
            'order'=>$this->brands['order'],
            'image' =>$filename,
            'status' =>$this->brands['status'],
        ]);

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
        return redirect()->route('dashboard.brands');
    }


    public function update($id){

        $brand = Brand::find($id);
        $this->validate(
            [
                'brands.name' => 'required',
                'brands.order' => 'required|numeric|unique:brands,order,'.$brand->id,
            ]);

        $filename = $this->image?$this->image->store('/','brand'):$brand->image;
        $this->brands['image']=$filename;
        $brand->update([
            'name'=>$this->brands['name'],
            'order'=> $this->brands['order'],
            'image'=>$filename,
            'status' =>$this->brands['status'],
        ]);
        $this->brands['image']=$brand->brandUrl();
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    }


}
