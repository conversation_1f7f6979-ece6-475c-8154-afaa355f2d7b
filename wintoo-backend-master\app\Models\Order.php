<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;

class Order extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
       protected $guarded = [];
       protected $appends = ['status_name','status_name_translations','store_jan_status_name'];

        public  static function  getColumnLang(){
         $columes=[
            'order_id'=>['رقم الطلبية' ,1,true,false,[]],
            'customer_id'=>[\Lang::get('order.customer_id') ,1,true,false,[]],
            'phone'=>[\Lang::get('order.phone') ,1,true,false,[]],
            'payment_method'=>[\Lang::get('order.payment_method') ,1,false,false,[]],
            'ship_address'=>[\Lang::get('order.ship_address') ,1,false,false,[]],
            'address_id'=>[\Lang::get('order.address_id') ,1,true,false,[]],
            'status'=>['الحالة' ,1,true,false,[]],
            'sub_total'=>[\Lang::get('order.sub_total') ,1,true,false,[]],
            'total'=>[\Lang::get('order.total') ,1,true,false,[]],
            'created_at'=>['تاريخ الانشاء' ,1,true,false,[]],
            'actions'=>['الخيارات',8,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=[
 	 	    'customer_id'=>[\Lang::get('order.customer_id')],
 	 	    'order_id'=>['رقم الطلبية'],
            'phone'=>[\Lang::get('order.phone')],
 	 	    'status'=>[\Lang::get('order.status'),[
            'type'=>'select',
            'model'=>[
                ['name'=>'مرفوض','value'=>'-2'],
                ['name'=>'ملغي','value'=>'-1'],
                ['name'=>'جديد','value'=>'0'],
                ['name'=>'قيد التجهيز','value'=>'1'],
                ['name'=>'مكتمل','value'=>'2'],
            ],
            'name'=>'name',
            'value'=>'value'
        ]],

 	 	 ]; return $columes;
  }
     public function scopeSearch($query, $data) {
 	 	 if(isset($data["customer_id"])){
 	 	     $search=$data["customer_id"];
             $query->whereHas('customer', function($q) use($search){
                 $q->where('username', 'LIKE', '%'.$search.'%');
             });
 	 	 } 	 if(isset($data["phone"])){
 	 	     $search=$data["phone"];
             $query->whereHas('customer', function($q) use($search){
                 $q->where('phone', 'LIKE', '%'.$search.'%');
             });
 	 	 }
 	 	 if(isset($data["payment_method"])){
 	 	   $query->where("payment_method","LIKE","%".$data["payment_method"]."%");}
 	 	 if(isset($data["ship_address"])){
 	 	   $query->where("ship_address","LIKE","%".$data["ship_address"]."%");}
 	 	 if(isset($data["address_id"])){
 	 	   $query->where("address_id","LIKE","%".$data["address_id"]."%");}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 if(isset($data["coupon_value"])){
 	 	   $query->where("coupon_value","LIKE","%".$data["coupon_value"]."%");}
 	 	 if(isset($data["coupon_id"])){
 	 	   $query->where("coupon_id","LIKE","%".$data["coupon_id"]."%");}
 	 	 if(isset($data["sub_total"])){
 	 	   $query->where("sub_total","LIKE","%".$data["sub_total"]."%");}
 	 	   if(isset($data["order_id"])){
 	 	   $query->where("id",$data["order_id"]);}
 	 	 if(isset($data["total"])){
 	 	   $query->where("total","LIKE","%".$data["total"]."%");}
         if(isset($data["store_id"])){
             $query->where("store_id",$data["store_id"]);}
 	 	 return $query ;
 	 	 }

    protected static function boot()
    {
        parent::boot();
        Order::deleting(function ($model) {
            OrderItem::where('order_id',$model->id)->delete();
        });
    }
    public function order_items()
    {
        return $this->hasMany(OrderItem::class )->has('product')->with("product");
    }

    public function order_trackers()
    {
        return $this->hasMany(OrderTracker::class );
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class );
    }

    public function store()
    {
        return $this->belongsTo(Store::class );
    }
    public function address()
    {
        return $this->belongsTo(Address::class );
    }

    public function getStatusNameAttribute(){
        if($this->status == -2){

            return Lang::get("lang.order_status_rejected");
        }
        if($this->status == -1){

            return Lang::get("lang.order_status_canceled");
        }
        if($this->status == 0){

            return Lang::get("lang.order_status_new");
        }
        if($this->status == 1){

            return Lang::get("lang.order_status_in_progress");
        }
        if($this->status == 2){

            return Lang::get("lang.order_status_on_delivery");
        }
        if($this->status == 3){

            return Lang::get("lang.order_status_delivered");
        }
        if($this->status == 4){

            return Lang::get("lang.order_status_completed");
        }
        if($this->status == 5){

            return Lang::get("lang.order_status_sent_to_delivery");
        }

        return Lang::get("lang.unknown");

    }
    public function getStatusNameTranslationsAttribute(){

        $key = ("lang.unknown");
        if($this->status == -2){

            $key = ("lang.order_status_rejected");
        }
        if($this->status == -1){

            $key = ("lang.order_status_canceled");
        }
        if($this->status == 0){

            $key = ("lang.order_status_new");
        }
        if($this->status == 1){

            $key = ("lang.order_status_in_progress");
        }
        if($this->status == 2){

            $key = ("lang.order_status_on_delivery");
        }
        if($this->status == 3){

            $key = ("lang.order_status_delivered");
        }
        if($this->status == 4){

            $key = ("lang.order_status_completed");
        }
        if($this->status == 5) {

            $key = ("lang.order_status_sent_to_delivery");
        }

        return formatInAllLocales($key);

    }


    public function getStoreJanStatusNameAttribute(){
        if($this->store_jan_status == -2){

            return "مرفوض";
        }
        if($this->store_jan_status == -1){

            return "ملغي";
        }
        if($this->store_jan_status == 0){

            return "جديد";
        }
        if($this->store_jan_status == 1){

            return "قيد التجهيز";
        }
        if($this->store_jan_status == 2){

            return "جاري التوصيل";
        }
        if($this->store_jan_status == 3){

            return "تم الشحن";
        }
        if($this->store_jan_status == 4){

            return "مكتمل";
        }
        return 'غير معرف';

    }
    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');

    }

}
