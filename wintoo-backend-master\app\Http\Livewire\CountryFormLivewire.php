<?php

namespace App\Http\Livewire;

use App\Models\Country;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\City;
class CountryFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'country';
      public $city;
       protected $rules = [
           "country.name.ar"=>'required',
           "country.name.tr"=>'required',
           "country.name.en"=>'required',
           "country.name.he"=>'required',
            "country.status"=>'nullable',
       ];
    protected $validationAttributes;
    public function __construct($id = null)
    {
        parent::__construct($id);
        $this->validationAttributes = $this->getColNameForValidation(Country::getColumnLang());
    }

    public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->country  = $id?Country::find($id):new Country();
          }
      public function render()
          {
              if(in_array('country_create',$this->actions_permission()) ){
//                  wreturn view('dashboard/city/index',[ 'data'=>$data])->extends('dashboard_layout.main');
                  return view('dashboard/country/form')->extends('dashboard_layout.main');
              }else{
                  return view('dashboard.not-authorized')->extends('dashboard_layout.main');
              }


          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->country->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.country');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


