<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Maincolor;
use App\Models\Size;
use Illuminate\Http\Resources\Json\JsonResource;

class ColorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'=>$this->id,
            'color'=>$this->color_ar,
            'human_color'=>$this->human_name?Maincolor::where('name',$this->human_name)->first()->human_name:'',
            'code'=>$this->code,
        ];

    }
}
