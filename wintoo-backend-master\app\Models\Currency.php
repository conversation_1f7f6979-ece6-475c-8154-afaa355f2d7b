<?php

namespace App\Models;

use <PERSON>run<PERSON><PERSON>\LaravelUserActivity\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Currency extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    use Loggable,HasTranslations;
    public $translatable = ['name'];
     public $guarded = [] ;
    protected $connection = "mysql";


    public function scopeIsActive($query){
        return $query->where('status',1);
    }
    public  static function  getColumnLang(){

        $columes=[
            'name'=>['العملة',1,true,false,[]],
            'symbol'=>['الرمز',2,true,true,[]],
            'iso_code'=>['Iso',3,false,false,[]],
            'status'=>['الحالة',3,true,false,[]],
            'actions'=>['الخيارات',4,true,false,['edit','delete']],
        ];

        return $columes;


    }
    public static function getSearchable(){
        $columes=[
            'name'=>[\Lang::get('storeCategory.name')],

        ]; return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["name"])){
            $query->where("name","LIKE","%".$data["name"]."%");}

        return $query ;
    }
    public function scopeActive($query) {

            $query->where("status",true);

        return $query ;
    }

    public function setStatus()
    {
        $this->status = !$this->status;
    }

    public static function usd(){
    return static::find(1);
    }

    public static function ils(){
    return static::find(2);
    }



}
