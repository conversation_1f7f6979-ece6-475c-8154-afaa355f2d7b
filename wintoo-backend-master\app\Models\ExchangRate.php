<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExchangRate extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded = [];
    protected $appends = ["type_name"];



    public  static function  getColumnLang(){
        $columes=[
            'id'=>['#' ,1,true,false,[]],
            'rate'=>['سعر الصرف' ,1,true,false,[]],
            'type'=>['نوع' ,1,true,false,[]],
            'created_at'=>['تاريخ الانشاء' ,1,true,false,[]],
            'actions'=>['الخيارات',2,false,false,['edit','delete']],
        ];
        return $columes;
    }
    public static function getSearchable(){
        $columes=['rate'=>['المعدل'],
            //'status'=>[\Lang::get('region.status')],
           // 'city_id'=>[\Lang::get('region.city_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'City']],

        ]; return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["rate"])){
            $query->where("rate","LIKE","%".$data["rate"]."%");}

        return $query ;
    }

    public function getTypeNameAttribute() {
        if ($this->type == "storejan"){
            return "منتجات ستور جان";
        }elseif ($this->type == "manual_product"){
            return "المنتجات المدخلة يدويا ";
        }
        return "كل المنتجات ";
    }

}
