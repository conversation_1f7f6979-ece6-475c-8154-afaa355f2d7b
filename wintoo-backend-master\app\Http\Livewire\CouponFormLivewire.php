<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Coupon;
class CouponFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'coupon';
      public $coupon;
      protected $listeners = ['Coupon-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "coupon.coupon_code"=>'nullable',
                "coupon.coupon_value"=>'nullable',
                "coupon.coupon_type"=>'nullable',
                "coupon.expired_date"=>'nullable',
                "coupon.used_date"=>'nullable',
                "coupon.coupon_status"=>'nullable',
                "coupon.customer_id"=>'nullable',
                "coupon.coupon_used"=>'nullable',

       ];
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->coupon  = $id?Coupon::find($id):new Coupon();
          }
      public function render()
          {
              return view('dashboard/coupon/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->coupon->coupon_code=$this->generateRandomString(10);
               $this->coupon->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.coupon');
            } catch (\Exception $e) {
                    \DB::rollback();
                $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

    public  function generateRandomString($length = 20) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

}


