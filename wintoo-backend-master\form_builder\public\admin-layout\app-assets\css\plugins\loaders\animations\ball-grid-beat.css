/*========================================================
        DARK LAYOUT
=========================================================*/
@-webkit-keyframes ball-grid-beat {
  50% {
    opacity : 0.7;
  }
  100% {
    opacity : 1;
  }
}
@keyframes ball-grid-beat {
  50% {
    opacity : 0.7;
  }
  100% {
    opacity : 1;
  }
}

.ball-grid-beat {
  width : 57px;
}
.ball-grid-beat > div:nth-child(1) {
  -webkit-animation-delay : -0.01s;
          animation-delay : -0.01s;
  -webkit-animation-duration : 1.24s;
          animation-duration : 1.24s;
}
.ball-grid-beat > div:nth-child(2) {
  -webkit-animation-delay : 0s;
          animation-delay : 0s;
  -webkit-animation-duration : 0.93s;
          animation-duration : 0.93s;
}
.ball-grid-beat > div:nth-child(3) {
  -webkit-animation-delay : 0.31s;
          animation-delay : 0.31s;
  -webkit-animation-duration : 1.11s;
          animation-duration : 1.11s;
}
.ball-grid-beat > div:nth-child(4) {
  -webkit-animation-delay : 0.08s;
          animation-delay : 0.08s;
  -webkit-animation-duration : 0.65s;
          animation-duration : 0.65s;
}
.ball-grid-beat > div:nth-child(5) {
  -webkit-animation-delay : 0.55s;
          animation-delay : 0.55s;
  -webkit-animation-duration : 0.74s;
          animation-duration : 0.74s;
}
.ball-grid-beat > div:nth-child(6) {
  -webkit-animation-delay : 0.59s;
          animation-delay : 0.59s;
  -webkit-animation-duration : 1.29s;
          animation-duration : 1.29s;
}
.ball-grid-beat > div:nth-child(7) {
  -webkit-animation-delay : 0.73s;
          animation-delay : 0.73s;
  -webkit-animation-duration : 0.9s;
          animation-duration : 0.9s;
}
.ball-grid-beat > div:nth-child(8) {
  -webkit-animation-delay : 0.55s;
          animation-delay : 0.55s;
  -webkit-animation-duration : 1.27s;
          animation-duration : 1.27s;
}
.ball-grid-beat > div:nth-child(9) {
  -webkit-animation-delay : -0.03s;
          animation-delay : -0.03s;
  -webkit-animation-duration : 1.42s;
          animation-duration : 1.42s;
}
.ball-grid-beat > div {
  background-color : #B8C2CC;
  width : 15px;
  height : 15px;
  border-radius : 100%;
  margin : 2px;
  -webkit-animation-fill-mode : both;
          animation-fill-mode : both;
  display : inline-block;
  float : left;
  -webkit-animation-name : ball-grid-beat;
          animation-name : ball-grid-beat;
  -webkit-animation-iteration-count : infinite;
          animation-iteration-count : infinite;
  -webkit-animation-delay : 0;
          animation-delay : 0;
}