<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DRIVER', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been setup for each driver as an example of the required options.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'avatars' => [
            'driver' => 'local',
            'root' => storage_path('app/avatars'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'gallary_banner' => [
            'driver' => 'local',
            'root' => storage_path('app/gallary_banner'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'sliders' => [
            'driver' => 'local',
            'root' => storage_path('app/sliders'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'brand' => [
            'driver' => 'local',
            'root' => storage_path('app/brand'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'pages' => [
            'driver' => 'local',
            'root' => storage_path('app/pages'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'product' => [
            'driver' => 'local',
            'root' => storage_path('app/product'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        'customer' => [
            'driver' => 'local',
            'root' => storage_path('app/customer'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],
        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
        public_path('storage/avatars') => storage_path('app/avatars'),
        public_path('storage/gallary_banner') => storage_path('app/gallary_banner'),
        public_path('storage/sliders') => storage_path('app/sliders'),
        public_path('storage/pages') => storage_path('app/pages'),
        public_path('storage/brand') => storage_path('app/brand'),
        public_path('storage/product') => storage_path('app/product'),
        public_path('storage/customer') => storage_path('app/customer'),
    ],

];
