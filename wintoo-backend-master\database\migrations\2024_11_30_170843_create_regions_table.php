<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRegionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('regions', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('name')->nullable()->comment('name');
			$table->boolean('status')->nullable()->comment('status');
			$table->integer('city_id')->nullable()->comment('city_id');
			$table->timestamps();
			$table->string('olivery_area_code', 191)->nullable()->comment('olivery_area_code');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('regions');
	}

}
