<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded=[];


    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    public function variation(){
        return $this->belongsTo(Variation::class);
    }
}
