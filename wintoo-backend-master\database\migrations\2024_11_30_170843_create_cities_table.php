<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCitiesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('cities', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('name')->nullable()->comment('name');
			$table->integer('governorate_id')->nullable()->comment('governorate');
			$table->boolean('status')->nullable()->comment('status');
			$table->timestamps();
			$table->integer('country_id')->nullable();
			$table->string('olivery_area_code', 191)->nullable()->comment('olivery_area_code');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('cities');
	}

}
