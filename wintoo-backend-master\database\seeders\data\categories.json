[{"id": "105", "parent_id": null, "name": "{\"ar\": \"مجوهرات\", \"en\": \"Jewelry\", \"he\": \"תכשיטים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "104", "parent_id": null, "name": "{\"ar\": \"ساعات\", \"en\": \"Watches\", \"he\": \"שעונים\", \"tr\": \"<PERSON>at<PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "103", "parent_id": null, "name": "{\"ar\": \"مستلزمات رحلات\", \"en\": \"Travel Accessories\", \"he\": \"אביזרים למטיילים\", \"tr\": \"<PERSON>yahat Aksesuarları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "101", "parent_id": null, "name": "{\"ar\": \"قطع سيارات\", \"en\": \"Car Parts\", \"he\": \"חלקי רכב\", \"tr\": \"Araba Parçaları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "102", "parent_id": null, "name": "{\"ar\": \"عجلات سيارات\", \"en\": \"Car Tires\", \"he\": \"צמיגים\", \"tr\": \"<PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "100", "parent_id": null, "name": "{\"ar\": \"اجهزة كمبيوتر\", \"en\": \"Computers\", \"he\": \"מחשבים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "99", "parent_id": null, "name": "{\"ar\": \"ديكورات\", \"en\": \"Decorations\", \"he\": \"קישוטים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "97", "parent_id": null, "name": "{\"ar\": \"اوراق جدران\", \"en\": \"Wallpapers\", \"he\": \"טפטים\", \"tr\": \"<PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "96", "parent_id": null, "name": "{\"ar\": \"ادوات صحية\", \"en\": \"Healthcare Products\", \"he\": \"מוצרי בריאות\", \"tr\": \"Sağlık Ürünleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "95", "parent_id": null, "name": "{\"ar\": \"منتجات عناية بالبشرة\", \"en\": \"Personal Care Products\", \"he\": \"מוצרי טיפוח אישי\", \"tr\": \"Kişisel Bakım Ürünleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "86", "parent_id": null, "name": "{\"ar\": \"شنط ستاتي\", \"en\": \"Women's Bags\", \"he\": \"תיקי נשים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON> Çantaları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "87", "parent_id": null, "name": "{\"ar\": \"اثاث منزلي\", \"en\": \"Home Furniture\", \"he\": \"רהיטים לבית\", \"tr\": \"<PERSON>v <PERSON>bilyaları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "88", "parent_id": null, "name": "{\"ar\": \"الهواتف الذكية\", \"en\": \"Smartphones\", \"he\": \"סמארטפונים\", \"tr\": \"Akıllı Telefonlar\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "89", "parent_id": null, "name": "{\"ar\": \"ادوات منزلية\", \"en\": \"Home Tools\", \"he\": \"כלי בית\", \"tr\": \"Ev Aletleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "90", "parent_id": null, "name": "{\"ar\": \"اجهزة كهربائية\", \"en\": \"Electrical Appliances\", \"he\": \"מ<PERSON><PERSON><PERSON><PERSON><PERSON> חשמל\", \"tr\": \"Elektrikli Ev Aletleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "91", "parent_id": null, "name": "{\"ar\": \"ملابس رياضة\", \"en\": \"Sportswear\", \"he\": \"ביגוד ספורט\", \"tr\": \"Spor Giyim\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "92", "parent_id": null, "name": "{\"ar\": \"ماكنات حياطة\", \"en\": \"Gardening Tools\", \"he\": \"כלי גינון\", \"tr\": \"Bahçe Aletleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "93", "parent_id": null, "name": "{\"ar\": \"ماكنات عصير\", \"en\": \"Juice Machines\", \"he\": \"מכונות מיץ\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "94", "parent_id": null, "name": "{\"ar\": \"ادوات رياضة\", \"en\": \"Sports Equipment\", \"he\": \"ציוד ספורט\", \"tr\": \"Spor Ekipmanları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "82", "parent_id": null, "name": "{\"ar\": \"عصائر طبيعية\", \"en\": \"Natural Juices\", \"he\": \"מיצים טבעיים\", \"tr\": \"Doğal Sular\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "85", "parent_id": null, "name": "{\"ar\": \"شنط سفر\", \"en\": \"Travel Bags\", \"he\": \"תיקי נסיעה\", \"tr\": \"<PERSON>yah<PERSON> Çantaları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "84", "parent_id": null, "name": "{\"ar\": \"مني ماركت\", \"en\": \"Mini Market\", \"he\": \"מיני מרקט\", \"tr\": \"Mini Market\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "83", "parent_id": null, "name": "{\"ar\": \"سوبرماركت\", \"en\": \"Supermarket\", \"he\": \"סופרמרקט\", \"tr\": \"Süpermarket\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "76", "parent_id": null, "name": "{\"ar\": \"عقارات\", \"en\": \"Real Estate\", \"he\": \"נדל״ן\", \"tr\": \"Gayrimenkul\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "77", "parent_id": null, "name": "{\"ar\": \"اكسسوارات رجالية\", \"en\": \"Men's Accessories\", \"he\": \"אביזרי גברים\", \"tr\": \"Erkek Aksesuarları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "98", "parent_id": null, "name": "{\"ar\": \"مكتبة ولوازم\", \"en\": \"Stationery and Supplies\", \"he\": \"ספרים וציוד למשרד\", \"tr\": \"Kırtasiye ve Malzemeleri\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "81", "parent_id": null, "name": "{\"ar\": \"فضة\", \"en\": \"Silver\", \"he\": \"כסף\", \"tr\": \"<PERSON>ü<PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "80", "parent_id": null, "name": "{\"ar\": \"ذهب\", \"en\": \"Gold\", \"he\": \"זהב\", \"tr\": \"<PERSON>ın\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "79", "parent_id": null, "name": "{\"ar\": \"تحف وهدايا\", \"en\": \"Antiques and Gifts\", \"he\": \"אוספיות ומתנות\", \"tr\": \"<PERSON><PERSON> ve Hediyeler\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "78", "parent_id": null, "name": "{\"ar\": \"اكسسوارات ستاتي\", \"en\": \"Women's Accessories\", \"he\": \"אביזרי נשים\", \"tr\": \"<PERSON><PERSON>ı<PERSON> Aksesuarları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "75", "parent_id": null, "name": "{\"ar\": \"عطور\", \"en\": \"Perfu<PERSON>\", \"he\": \"בשמים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "74", "parent_id": null, "name": "{\"ar\": \"احذية ستاتي\", \"en\": \"Women's Shoes\", \"he\": \"נעלי נשים\", \"tr\": \"<PERSON><PERSON>ı<PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "73", "parent_id": null, "name": "{\"ar\": \"احذية رجالي\", \"en\": \"Men's Shoes\", \"he\": \"נעלי גברים\", \"tr\": \"Erkek Ayakkabıları\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "70", "parent_id": null, "name": "{\"ar\": \"ملابس رجالي\", \"en\": \"Men's Clothing\", \"he\": \"ביגוד גברים\", \"tr\": \"<PERSON>rk<PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "71", "parent_id": null, "name": "{\"ar\": \"ملابس ستاتي\", \"en\": \"Women's Clothing\", \"he\": \"ביגוד נשים\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "72", "parent_id": null, "name": "{\"ar\": \"ملابس اطفال\", \"en\": \"Children's Clothing\", \"he\": \"ביגוד לילדים\", \"tr\": \"Çocuk Giyim\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "1", "parent_id": null, "name": "{\"ar\": \"أراضٍ\", \"en\": \"Land\", \"he\": \"אדמה\", \"tr\": \"<PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "2", "parent_id": null, "name": "{\"ar\": \"شقة\", \"en\": \"Apartment\", \"he\": \"דירה\", \"tr\": \"Daire\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "3", "parent_id": null, "name": "{\"ar\": \"منزل مستقل\", \"en\": \"Detached House\", \"he\": \"בית פרטי\", \"tr\": \"Müstakil Ev\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "4", "parent_id": null, "name": "{\"ar\": \"فيلا\", \"en\": \"Villa\", \"he\": \"וילה\", \"tr\": \"Villa\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "5", "parent_id": null, "name": "{\"ar\": \"شاليهات\", \"en\": \"Cha<PERSON>\", \"he\": \"שליחות\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "6", "parent_id": null, "name": "{\"ar\": \"مخازن\", \"en\": \"Warehouses\", \"he\": \"מחסנים\", \"tr\": \"Depolar\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "7", "parent_id": null, "name": "{\"ar\": \"محل تجاري\", \"en\": \"Commercial Shop\", \"he\": \"חנות مركزية\", \"tr\": \"<PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "8", "parent_id": null, "name": "{\"ar\": \"عمارة تجارية\", \"en\": \"Commercial Building\", \"he\": \"בניין مصرفي\", \"tr\": \"<PERSON><PERSON><PERSON>\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "9", "parent_id": null, "name": "{\"ar\": \"عمارة سكنية\", \"en\": \"Residential Building\", \"he\": \"בנيان مسكوني\", \"tr\": \"Konut Binasi\"}", "image": "image_path", "banner": "banner_path", "status": "1", "is_main": "0"}, {"id": "106", "parent_id": null, "name": "{\"tr\":\"Saat al\\u0131\\u015fveri\\u015fi yap\\u0131n.\",\"he\":\"\\u05d7\\u05e0\\u05d5\\u05ea \\u05e9\\u05e2\\u05d5\\u05e0\\u05d9\\u05dd.\",\"en\":\"Shop Watches.\",\"ar\":\"\\u0645\\u062d\\u0644 \\u0633\\u0627\\u0639\\u0627\\u062a\"}", "image": "", "banner": "", "status": "1", "is_main": "0"}, {"id": "107", "parent_id": null, "name": "{\"he\":\"\\u05de\\u05b4\\u05e9\\u05c1\\u05e7\\u05b8\\u05e4\\u05b7\\u05d9\\u05b4\\u05dd\",\"tr\":\"g\\u00f6zl\\u00fck\",\"en\":\"glasses\",\"ar\":\"\\u0646\\u0638\\u0627\\u0631\\u0627\\u062a \\u0634\\u0645\\u0633\\u064a\\u0629\"}", "image": "", "banner": "", "status": "1", "is_main": "0"}]