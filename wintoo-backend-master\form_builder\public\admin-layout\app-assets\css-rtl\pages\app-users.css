/*========================================================
        DARK LAYOUT
=========================================================*/
.dataTables_wrapper .dataTables_length {
  display : inline-block;
}
.dataTables_wrapper .dataTables_length .custom-select {
  width : 6.714rem !important;
  height : 3rem;
  border-radius : 1.428rem;
  border : 1px solid #DAE1E7;
  font-size : 1rem;
  margin-bottom : 1.5rem;
  background-position : calc(100% - (100% - 8px)) 13px, calc(100% - (100% - 13px)) 13px, 0 0 !important;
}
.dataTables_wrapper .dataTables_length .custom-select:focus {
  box-shadow : none;
}

.dataTables_wrapper .dataTables_filter {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : end;
  -webkit-justify-content : flex-end;
  -ms-flex-pack : end;
          justify-content : flex-end;
}
.dataTables_wrapper .dataTables_filter .form-control {
  border-radius : 1.428rem;
  border : 1px solid #DAE1E7;
  font-size : 1rem;
  width : 197px;
  height : 40px;
  padding-right : 2.2rem;
}
.dataTables_wrapper .dataTables_filter label {
  position : relative;
}
.dataTables_wrapper .dataTables_filter label:after {
  content : '\e8bd';
  font-family : 'feather';
  position : absolute;
  right : 1.428rem;
  top : 10px;
  font-size : 1.1rem;
  color : #DAE1E7;
}

.dataTables_wrapper .dt-checkboxes-select-all input, .dataTables_wrapper .dt-checkboxes-select input {
  width : 0;
  position : relative;
}
.dataTables_wrapper .dt-checkboxes-select-all input:before, .dataTables_wrapper .dt-checkboxes-select input:before {
  border : 2px solid #B4B4B4;
  content : '';
  width : 1.071rem;
  height : 1.071rem;
  padding : 0;
  border-radius : 2px;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  -webkit-transform : rotate(90deg);
      -ms-transform : rotate(90deg);
          transform : rotate(90deg);
  overflow : hidden;
          transition : all 0.2s ease;
  position : absolute;
}
.dataTables_wrapper .dt-checkboxes-select-all input:after, .dataTables_wrapper .dt-checkboxes-select input:after {
  background-color : #7367F0;
  border : 2px solid #7367F0;
  font-family : 'feather';
  content : '\e83f';
  font-size : 0.75rem;
  line-height : 1.2;
  color : #FFFFFF;
  opacity : 0;
  position : absolute;
  width : 0.928rem;
  height : 1rem;
  -webkit-transform : translate(-100%);
      -ms-transform : translate(-100%);
          transform : translate(-100%);
  -webkit-transform-origin : left;
      -ms-transform-origin : left;
          transform-origin : left;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  overflow : hidden;
}
.dataTables_wrapper .dt-checkboxes-select-all input:active:checked:after, .dataTables_wrapper .dt-checkboxes-select input:active:checked:after {
  -webkit-transform : translate(-3px);
      -ms-transform : translate(-3px);
          transform : translate(-3px);
}
.dataTables_wrapper .dt-checkboxes-select-all input:checked:before, .dataTables_wrapper .dt-checkboxes-select input:checked:before {
  border : 2px solid #7367F0;
  -webkit-transform : rotate(0deg);
      -ms-transform : rotate(0deg);
          transform : rotate(0deg);
}
.dataTables_wrapper .dt-checkboxes-select-all input:checked:after, .dataTables_wrapper .dt-checkboxes-select input:checked:after {
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  opacity : 1;
  -webkit-transform : translate(0);
      -ms-transform : translate(0);
          transform : translate(0);
}

.dataTables_wrapper .dataTable thead tr th:first-child {
  padding-left : 2rem;
}

.dataTables_wrapper .dataTable tbody .select-checkbox:before, .dataTables_wrapper .dataTable tbody .select-checkbox:after {
  display : none !important;
}

.dataTables_wrapper .dataTable tbody tr.selected {
  border-radius : 0;
}

.dataTables_wrapper .dataTable tbody tr td:first-child {
  padding-left : 2rem;
}

.dataTables_wrapper .dataTable tbody tr td .user-info {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : start;
  -webkit-justify-content : flex-start;
  -ms-flex-pack : start;
          justify-content : flex-start;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
}

.dataTables_wrapper .dataTables_paginate .pagination {
  -webkit-box-pack : center !important;
  -webkit-justify-content : center !important;
  -ms-flex-pack : center !important;
          justify-content : center !important;
}

@media (max-width: 1199.98px) {
  .dataTables_wrapper .dataTable tbody tr td:nth-child(2) {
    display : -webkit-box;
    display : -webkit-flex;
    display : -ms-flexbox;
    display :         flex;
    -webkit-flex-wrap : wrap;
        -ms-flex-wrap : wrap;
            flex-wrap : wrap;
  }
}

_:-ms-lang(x) tbody tr td.dt-checkboxes-cell input, _:-ms-lang(x) tbody tr th.dt-checkboxes-cell input, _:-ms-lang(x) thead tr td.dt-checkboxes-cell input, _:-ms-lang(x) thead tr th.dt-checkboxes-cell input, .dataTable tbody tr td.dt-checkboxes-cell input, .dataTable tbody tr th.dt-checkboxes-cell input, .dataTable thead tr td.dt-checkboxes-cell input, .dataTable thead tr th.dt-checkboxes-cell input {
  width : auto !important;
}

@supports (-moz-osx-font-smoothing: auto) {
  .dataTable input {
    width : auto !important;
  }
}

@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) {
    .dataTable tbody td.dt-checkboxes-cell input:after {
      right : -0.485rem;
    }
    .dataTable .dt-checkboxes-cell input {
      -webkit-appearance : none;
      top : -10px;
    }
  }
}

.dataTable .mac-checkbox {
  -webkit-appearance : none;
  outline : none;
  width : auto;
}