<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SystemDrawDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'=>$this->id,
            "title" => $this->title,
            "status" => $this->status,
            "created_at" => $this->created_at,
            "date" => toLocalDate($this->date),
            "datetime"=>toLocalDate($this->draw_date_format , 'Y/m/d H:i:s'),
            "is_old"=>$this->is_old,
            "is_end"=>$this->is_end,

            "winner"=>null,
            "prize_name"=>$this->prize_name,
        ];
    }
}
