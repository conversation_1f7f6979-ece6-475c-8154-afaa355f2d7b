$(window).on("load",function(){var e="#7367F0",t="#EA5455",r="#FF9F43",o="#9c8cfc",a="#FFC085",s="#f29292",i="#b9c3cd",l="#e7eef7",n={chart:{type:"donut",height:315,toolbar:{show:!1}},dataLabels:{enabled:!1},series:[58.6,34.9,6.5],legend:{show:!1},comparedResult:[2,-3,8],labels:["Desktop","Mobile","Tablet"],stroke:{width:0},colors:[e,r,t],fill:{type:"gradient",gradient:{gradientToColors:[o,a,s]}}};new ApexCharts(document.querySelector("#session-chart"),n).render();var d={chart:{height:325,type:"radialBar"},colors:[e,r,t],fill:{type:"gradient",gradient:{shade:"dark",type:"vertical",shadeIntensity:.5,gradientToColors:[o,a,s],inverseColors:!1,opacityFrom:1,opacityTo:1,stops:[0,100]}},stroke:{lineCap:"round"},plotOptions:{radialBar:{size:150,hollow:{size:"20%"},track:{strokeWidth:"100%",margin:15},dataLabels:{name:{fontSize:"18px"},value:{fontSize:"16px"},total:{show:!0,label:"Total",formatter:function(e){return 42459}}}}},series:[70,52,26],labels:["Finished","Pending","Rejected"]};new ApexCharts(document.querySelector("#product-order-chart"),d).render();var h={chart:{type:"pie",height:325,dropShadow:{enabled:!1,blur:5,left:1,top:1,opacity:.2},toolbar:{show:!1}},labels:["New","Returning","Referrals"],series:[690,258,149],dataLabels:{enabled:!1},legend:{show:!1},stroke:{width:5},colors:[e,r,t],fill:{type:"gradient",gradient:{gradientToColors:[o,a,s]}}};new ApexCharts(document.querySelector("#customer-chart"),h).render();var c={chart:{height:300,type:"radar",dropShadow:{enabled:!0,blur:8,left:1,top:1,opacity:.2},toolbar:{show:!1}},toolbar:{show:!1},series:[{name:"Sales",data:[90,50,86,40,100,20]},{name:"Visit",data:[70,75,70,76,20,85]}],stroke:{width:0},colors:[e,"#00cfe8"],plotOptions:{radar:{polygons:{strokeColors:[i,"transparent","transparent","transparent","transparent","transparent"],connectorColors:"transparent"}}},fill:{type:"gradient",gradient:{shade:"dark",gradientToColors:["#9f8ed7","#1edec5"],shadeIntensity:1,type:"horizontal",opacityFrom:1,opacityTo:1,stops:[0,100,100,100]}},markers:{size:0},legend:{show:!1},labels:["Jan","Feb","Mar","Apr","May","Jun"],dataLabels:{style:{colors:[i,i,i,i,i,i]}},yaxis:{show:!1},grid:{show:!1}};new ApexCharts(document.querySelector("#sales-chart"),c).render();var p={chart:{height:270,type:"radialBar",sparkline:{enabled:!1}},plotOptions:{radialBar:{size:150,offsetY:20,startAngle:-150,endAngle:150,hollow:{size:"65%"},track:{background:"#fff",strokeWidth:"100%"},dataLabels:{value:{offsetY:30,color:"#99a2ac",fontSize:"2rem"}}}},colors:[t],fill:{type:"gradient",gradient:{shade:"dark",type:"horizontal",shadeIntensity:.5,gradientToColors:[e],inverseColors:!0,opacityFrom:1,opacityTo:1,stops:[0,100]}},stroke:{dashArray:8},series:[83],labels:["Completed Tickets"]};new ApexCharts(document.querySelector("#support-tracker-chart"),p).render();var y={chart:{height:260,toolbar:{show:!1},type:"line"},stroke:{curve:"smooth",dashArray:[0,8],width:[4,2]},grid:{borderColor:l},legend:{show:!1},colors:[s,i],fill:{type:"gradient",gradient:{shade:"dark",inverseColors:!1,gradientToColors:[e,i],shadeIntensity:1,type:"horizontal",opacityFrom:1,opacityTo:1,stops:[0,100,100,100]}},markers:{size:0,hover:{size:5}},xaxis:{labels:{style:{colors:i}},axisTicks:{show:!1},categories:["01","05","09","13","17","21","26","31"],axisBorder:{show:!1},tickPlacement:"on"},yaxis:{tickAmount:5,labels:{style:{color:i},formatter:function(e){return 999<e?(e/1e3).toFixed(1)+"k":e}}},tooltip:{x:{show:!1}},series:[{name:"This Month",data:[45e3,47e3,44800,47500,45500,48e3,46500,48600]},{name:"Last Month",data:[46e3,48e3,45500,46600,44500,46500,45e3,47e3]}]};new ApexCharts(document.querySelector("#revenue-chart"),y).render();var g={chart:{height:250,type:"radialBar",sparkline:{enabled:!0},dropShadow:{enabled:!0,blur:3,left:1,top:1,opacity:.1}},colors:["#00db89"],plotOptions:{radialBar:{size:110,startAngle:-150,endAngle:150,hollow:{size:"77%"},track:{background:i,strokeWidth:"50%"},dataLabels:{name:{show:!1},value:{offsetY:18,color:i,fontSize:"4rem"}}}},fill:{type:"gradient",gradient:{shade:"dark",type:"horizontal",shadeIntensity:.5,gradientToColors:["#00b5b5"],inverseColors:!0,opacityFrom:1,opacityTo:1,stops:[0,100]}},series:[83],stroke:{lineCap:"round"}};new ApexCharts(document.querySelector("#goal-overview-chart"),g).render();var u={chart:{type:"bar",height:200,sparkline:{enabled:!0},toolbar:{show:!1}},states:{hover:{filter:"none"}},colors:[l,l,e,l,l,l],series:[{name:"Sessions",data:[75,125,225,175,125,75,25]}],grid:{show:!1,padding:{left:0,right:0}},plotOptions:{bar:{columnWidth:"45%",distributed:!0,endingShape:"rounded"}},tooltip:{x:{show:!1}},xaxis:{type:"numeric"}};new ApexCharts(document.querySelector("#avg-session-chart"),u).render();var b={chart:{height:270,toolbar:{show:!1},type:"line",dropShadow:{enabled:!0,top:20,left:2,blur:6,opacity:.2}},stroke:{curve:"smooth",width:4},grid:{borderColor:l},legend:{show:!1},colors:["#df87f2"],fill:{type:"gradient",gradient:{shade:"dark",inverseColors:!1,gradientToColors:[e],shadeIntensity:1,type:"horizontal",opacityFrom:1,opacityTo:1,stops:[0,100,100,100]}},markers:{size:0,hover:{size:5}},xaxis:{labels:{style:{colors:i}},axisTicks:{show:!1},categories:["Jan","Feb","Mar","Apr","May","Jun","July","Aug","Sep","Oct","Nov","Dec"],axisBorder:{show:!1},tickPlacement:"on"},yaxis:{tickAmount:5,labels:{style:{color:i},formatter:function(e){return 999<e?(e/1e3).toFixed(1)+"k":e}}},tooltip:{x:{show:!1}},series:[{name:"Sales",data:[140,180,150,205,160,295,125,255,205,305,240,295]}]};new ApexCharts(document.querySelector("#sales-line-chart"),b).render();var w={chart:{stacked:!0,type:"bar",toolbar:{show:!1},height:290},plotOptions:{bar:{columnWidth:"10%"}},colors:[e,t],series:[{name:"New Clients",data:[175,125,225,175,160,189,206,134,159,216,148,123]},{name:"Retained Clients",data:[-144,-155,-141,-167,-122,-143,-158,-107,-126,-131,-140,-137]}],grid:{borderColor:l,padding:{left:0,right:0}},legend:{show:!0,position:"top",horizontalAlign:"left",offsetX:0,fontSize:"14px",markers:{radius:50,width:10,height:10}},dataLabels:{enabled:!1},xaxis:{labels:{style:{colors:i}},axisTicks:{show:!1},categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisBorder:{show:!1}},yaxis:{tickAmount:5,labels:{style:{color:i}}},tooltip:{x:{show:!1}}};new ApexCharts(document.querySelector("#client-retention-chart"),w).render()});