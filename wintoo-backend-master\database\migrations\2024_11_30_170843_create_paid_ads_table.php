<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaidAdsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('paid_ads', function(Blueprint $table)
		{
			$table->bigInteger('id', true);
			$table->timestamps();
			$table->integer('customer_id')->nullable();
			$table->string('title', 191)->nullable();
			$table->string('Description', 191)->nullable();
			$table->string('media', 191)->nullable();
			$table->string('city', 191)->nullable();
			$table->string('country', 191)->nullable();
			$table->boolean('deleted')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('paid_ads');
	}

}
