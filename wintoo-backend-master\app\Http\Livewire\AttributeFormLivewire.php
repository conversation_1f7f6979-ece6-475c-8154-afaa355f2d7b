<?php

namespace App\Http\Livewire;

use App\Models\AttributeOption;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Attribute;
class AttributeFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $cat_id = "";
      public $file_name = 'attribute';
      public $attribute;
      protected $listeners = ['Attribute-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
      public  $categorie_ids=null;
       protected $rules = [
                "attribute.title.ar"=>'required',
                "attribute.title.en"=>'required',
                "attribute.title.he"=>'required',
                "attribute.title.tr"=>'required',
                "attribute.type"=>'required',
                "attribute.store_category_id"=>'nullable',
                "attribute.status"=>'nullable',
                "attribute.json_data"=>'nullable',
                "attribute.is_required"=>'nullable',
                "categorie_ids"=>'required',

       ];
       public $inputs = [];


       protected $validationAttributes;
       public function __construct( )
       {
           $this->validationAttributes = $this->getColNameForValidation(Attribute::getColumnLang());
       }

      public function mount($cat_id = null,  $id =null)
          {


              $this->title = \Lang::get('lang.add_account');
              $this->attribute  = $id?Attribute::find($id):new Attribute();
              $this->cat_id = $cat_id ;
              if($id){
                  $this->inputs =  $this->attribute->attribute_options->toArray();
                  $this->categorie_ids = [$cat_id];

              }else{
                  $this->inputs[]=new AttributeOption();
                  $this->categorie_ids = [$cat_id];

              }




          }
      public function render()
          {
              return view('dashboard/attribute/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->attribute->save();

               $this->attribute->categories()->sync($this->categorie_ids);

               foreach ($this->inputs as $key => $input){
                    if(isset($input["id"])){
                        $option = AttributeOption::find($input["id"]);
                        $option->update(["title"=>$input["title"],"attribute_id"=>$this->attribute->id]);
                    }else{
                        AttributeOption::create(["title"=>$input["title"],"attribute_id"=>$this->attribute->id]);
                    }
               }
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.storeCategoryAttribute',$this->cat_id);
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }
    public function add()
    {
        $this->inputs[]= new AttributeOption();
    }

    public function remove($i)
    {
        unset($this->inputs[$i]);
        $this->inputs = array_values($this->inputs);
    }

}


