<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePersonalAccessTokensIfNotExistedTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		if (!Schema::hasTable('personal_access_tokens')) {
			Schema::create('personal_access_tokens', function(Blueprint $table)
			{
				$table->bigInteger('id', true)->unsigned();
				$table->string('tokenable_type', 191);
				$table->bigInteger('tokenable_id')->unsigned();
				$table->string('name', 191);
				$table->string('token', 64)->unique();
				$table->text('abilities')->nullable();
				$table->dateTime('last_used_at')->nullable();
				$table->timestamps();
				$table->index(['tokenable_type','tokenable_id']);
			});
		}
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('personal_access_tokens');
	}

}
