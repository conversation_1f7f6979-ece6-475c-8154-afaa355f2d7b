<?php

namespace App\Http\Livewire;

use App\Models\Customer;
use App\Models\ExchangRate;
use App\Services\Delivery;
use App\Services\SendNotification;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Order;
class OrderFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'order';
      public $order;
      public $status;
      protected $listeners = ['Order-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "order.customer_id"=>'nullable',
                "order.payment_method"=>'nullable',
                "order.ship_address"=>'nullable',
                "order.address_id"=>'nullable',
                "order.status"=>'nullable',
                "order.coupon_value"=>'nullable',
                "order.coupon_id"=>'nullable',
                "order.sub_total"=>'nullable',
                "order.total"=>'nullable',

       ];
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->order  = $id?Order::find($id):new Order();

          }
      public function render()
          {
              $order = Order::find($this->order->id);

              $rate = ExchangRate::find($this->order->exchang_rate_id);
//              dd($order,$rate,$order->order_items);
              return view('dashboard/order/details',['rate'=>$rate])->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->order->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.order');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }


       public function  show_model (){

              $this->showDataModel('change_status_model','show');
       }

       public function statusUpdate(){

           $this->order->status= $this->status;
           $this->order->save();
           $user = Customer::findOrFail($this->order->customer_id);
         /*   if($this->status == -2){
                $msg ='عزيزي المستخدم';
                $msg .="\n";
                $msg .= "تم رفض طلبك";
                $msg .=" رقم ";
                $msg .=$this->order->id;
                $msg .="";
                $data=[
                    'title'=>$msg,
                    'description'=>'',
                    'type'=>'order',
                    'id'=>$this->order->id
                ];
                fcmNotification($user,$data);
                \Notification::send($user,
                    new \App\Notifications\GeneralNotification(
                        $msg,
                        'order',
                        $data
                    ));
           }elseif($this->status == -1){
                $msg ='عزيزي المستخدم';
                $msg .="\n";
                $msg .= "تم الغاء طلبك";
                $msg .=" رقم ";
                $msg .=$this->order->id;
                $msg .="";
                $data=[
                    'title'=>$msg,
                    'description'=>'',
                    'type'=>'order',
                    'id'=>$this->order->id
                ];
                fcmNotification($user,$data);
                \Notification::send($user,
                    new \App\Notifications\GeneralNotification(
                        $msg,
                        'order',
                        $data
                    ));
           }elseif($this->status == 1){
                $msg ='عزيزي المستخدم';
                $msg .="\n";
                $msg .= "جاري تجهيز طلبك";
                $msg .=" رقم ";
                $msg .=$this->order->id ;
                $msg .="";
                $data=[
                    'title'=>$msg,
                    'description'=>'',
                    'type'=>'order',
                    'id'=>$this->order->id
                ];
                fcmNotification($user,$data);
                \Notification::send($user,
                    new \App\Notifications\GeneralNotification(
                        $msg,
                        'order',
                        $data
                    ));
           }elseif($this->status == 5){


                $delivery_service = new Delivery();
                $delivery_service->create_order($this->order);

                $msg ='عزيزي المستخدم';
                $msg .="\n";
                $msg .= "تم ارسال طلبك ";
                $msg .=" رقم ";
                $msg .=$this->order->id;
                $msg .=" لشركة التوصيل ";
                $data=[
                    'title'=>$msg,
                    'description'=>'',
                    'type'=>'order',
                    'id'=>$this->order->id
                ];
                fcmNotification($user,$data);
                \Notification::send($user,
                    new \App\Notifications\GeneralNotification(
                        $msg,
                        'order',
                        $data
                    ));

           }*/

           if($this->status == 5 && is_null($this->order->delivery_reference_id)) {
               $delivery_service = new Delivery();
             $delivery_status =   $delivery_service->create_order($this->order);
             if (!$delivery_status){

                 $this->showModal('حصل خطأ ما','فشل الإرسال لشركة التوصيل , راسل الإدارة','error');
                 return;

             }
           }elseif ($this->status == 5 && !is_null($this->order->delivery_reference_id)){
               $this->showModal('حصل خطأ ما','تم الإرسال مسبقا لشركة التوصيل','error');
            return;
           }


           SendNotification::storeChangeOrderStatus($this->order);

           $this->showDataModel('change_status_model','hide');
           $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');


       }
}


