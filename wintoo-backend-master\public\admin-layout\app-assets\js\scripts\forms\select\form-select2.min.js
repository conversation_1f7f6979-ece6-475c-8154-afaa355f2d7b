!function(o){"use strict";function t(t){t.element;return t.id?"<i class='"+o(t.element).data("icon")+"'></i>"+t.text:t.text}o(".select2").select2({dropdownAutoWidth:!0,width:"100%"}),o(".select2-icons").select2({dropdownAutoWidth:!0,width:"100%",minimumResultsForSearch:1/0,templateResult:t,templateSelection:t,escapeMarkup:function(t){return t}}),o(".max-length").select2({dropdownAutoWidth:!0,width:"100%",maximumSelectionLength:2,placeholder:"Select maximum 2 items"});var e=o(".js-example-programmatic").select2({dropdownAutoWidth:!0,width:"100%"}),s=o(".js-example-programmatic-multi").select2();s.select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Programmatic Events"}),o(".js-programmatic-set-val").on("click",function(){e.val("CA").trigger("change")}),o(".js-programmatic-open").on("click",function(){e.select2("open")}),o(".js-programmatic-close").on("click",function(){e.select2("close")}),o(".js-programmatic-init").on("click",function(){e.select2()}),o(".js-programmatic-destroy").on("click",function(){e.select2("destroy")}),o(".js-programmatic-multi-set-val").on("click",function(){s.val(["CA","AL"]).trigger("change")}),o(".js-programmatic-multi-clear").on("click",function(){s.val(null).trigger("change")});function i(t,e){return 0===e.toUpperCase().indexOf(t.toUpperCase())}o(".select2-data-array").select2({dropdownAutoWidth:!0,width:"100%",data:[{id:0,text:"enhancement"},{id:1,text:"bug"},{id:2,text:"duplicate"},{id:3,text:"invalid"},{id:4,text:"wontfix"}]}),o(".select2-data-ajax").select2({dropdownAutoWidth:!0,width:"100%",ajax:{url:"https://api.github.com/search/repositories",dataType:"json",delay:250,data:function(t){return{q:t.term,page:t.page}},processResults:function(t,e){return e.page=e.page||1,{results:t.items,pagination:{more:30*e.page<t.total_count}}},cache:!0},placeholder:"Search for a repository",escapeMarkup:function(t){return t},minimumInputLength:1,templateResult:function(t){if(t.loading)return t.text;var e="<div class='select2-result-repository clearfix'><div class='select2-result-repository__avatar'><img src='"+t.owner.avatar_url+"' /></div><div class='select2-result-repository__meta'><div class='select2-result-repository__title'>"+t.full_name+"</div>";t.description&&(e+="<div class='select2-result-repository__description'>"+t.description+"</div>");return e+="<div class='select2-result-repository__statistics'><div class='select2-result-repository__forks'><i class='icon-code-fork mr-0'></i> "+t.forks_count+" Forks</div><div class='select2-result-repository__stargazers'><i class='icon-star5 mr-0'></i> "+t.stargazers_count+" Stars</div><div class='select2-result-repository__watchers'><i class='icon-eye mr-0'></i> "+t.watchers_count+" Watchers</div></div></div></div>"},templateSelection:function(t){return t.full_name||t.text}}),o.fn.select2.amd.require(["select2/compat/matcher"],function(t){o(".select2-customize-result").select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Search by 'r'",matcher:t(i)})}),o(".select2-theme").select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Classic Theme",theme:"classic"}),o(".select2-size-lg").select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:"select-lg"}),o(".select2-size-sm").select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:"select-sm"}),o(".select2-bg").each(function(t,e){var s="",i="",r=o(this).data("bgcolor");s=o(this).data("bgcolor-variation"),""!==(i=o(this).data("text-variation"))&&(i=" "+i),""!==s&&(s=" bg-"+s);var a="bg-"+r+s+" "+o(this).data("text-color")+i+" border-"+r+" border-darken-2 ";o(this).select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:a})}),o(".select2-border").each(function(t,e){var s="",i="",r=o(this).data("border-color");""!==(i=o(this).data("text-variation"))&&(i=" "+i),""!==(s=o(this).data("border-variation"))&&(s=" border-"+s);var a="border-"+r+" "+s+" "+o(this).data("text-color")+i;o(this).select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:a})}),o(".select2-full-bg").each(function(t,e){var s="",i="",r=o(this).data("bgcolor");""!==(s=o(this).data("bgcolor-variation"))&&(s=" bg-"+s),""!==(i=o(this).data("text-variation"))&&(i=" "+i);var a="bg-"+r+s+" "+o(this).data("text-color")+i+" border-"+r+" border-darken-2 ";o(this).select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:a,dropdownCssClass:a})}),o("select[data-text-color]").each(function(t,e){var s,i=o(this).data("text-color");""!==(s=o(this).data("text-variation"))&&(s=" "+s),o(this).next(".select2").find(".select2-selection__rendered").addClass(i+s)})}((window,document,jQuery));