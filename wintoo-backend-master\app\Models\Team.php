<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
use \App\Traits\PropertyGetter;

 protected $guarded = [];
 public  static function  getColumnLang(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('team.name') ,1,true,false,[]],
 	 	'user'=>[\Lang::get('team.user') ,1,true,false,[]],
 	 	'status'=>[\Lang::get('team.status') ,1,false,false,[]],
 	 	   'actions'=>['الخيارات',2,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('Team.name')],
 	 	'user'=>[\Lang::get('Team.user')],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["user"])){
 	 	   $query->where("user","LIKE","%".$data["user"]."%");}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 return $query ;
 	 	 }

}
