<?php

namespace App\Http\Livewire;

use App\Models\BlackFridayRequest;
use App\Models\Color;
use App\Models\Maincolor;
use App\Models\Offer;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\Reviews;
use App\Models\Size;
use App\Models\Variation;
use Illuminate\Support\Str;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\TemporaryUploadedFile;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use Storage;
class ProductFormLivewire extends Component
{
 use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $product;
    public $sub_categories,$main_categories,$categories;
    public $product_reviews;
    public $offer_options=[];
    public $profit_options=[];
    public $selected,$main_categories_selected,$sub_categories_selected=[];
    public $image_main=null;
    public $image1=null;
    public $image2;
    public $image3;
    public $uploader_images;
    public $images = [];
    public $offers = [];
    public $new_price=null;
    public $offer_type=null;
    public $i = 1;
    public $tab = 'account-tab';

    public $inputs = [['product_size'=>'','product_color'=>'','is_in_stock'=>'','stock_quantity'=>'','display_selling_price'=>'','variation_id'=>'']];
    protected $rules = [
        'product.title'=>'required',
        'product.category_id'=>'required',
        'product.offer_id'=>'nullable',
        'product.brand_id'=>'nullable',
        'product.description'=>'nullable',
        'product.price'=>'nullable',
        'product.new_price'=>'nullable',
        'product.offer_discount_type'=>'nullable',
        'product.discount_amount'=>'nullable',
        'product.percentage_value'=>'nullable',
        'image_main'=>'required',
        'image1'=>'nullable',
        'image2'=>'nullable',
        'image3'=>'nullable',
        'product.is_new'=>'nullable',
        'product.product_type'=>'nullable',
        'product.is_available'=>'nullable',
        'product.bar_code'=>'nullable',
        'product.sub_category_id'=>'nullable',
        'product.main_category_id'=>'nullable',
        'product.profit_type'=>'nullable',
        'product.profit_amount'=>'nullable',
        'product.identifier'=>'nullable',
        'product.store_id'=>'nullable',
        'images.*'=>'nullable',
    ];

    public $listeners = [
        "single_file_uploaded" => 'singleFileUploaded',
        "add_file" => 'add_image',
        "clear_file" => 'removeImage',
    ];
    public $images_uploaders=[null];
    public function mount($id=null) {
        ini_set('memory_limit', '-1');
        $this->product = $id?Product::find($id):new Product();
        $this->product_reviews = $id?Reviews::where('product_id',$id)->get():'';
        $rate = getRateBaseOnProductType($this->product->product_type);
        $price_after_rate = $rate * $this->product->price;
        $new_price = $rate * $this->product->new_price;

        $this->offer_type = $id?!is_null($this->product->new_price)?'offer':'without_offer':'';
    //    $this->selected= $id?Product::find($id)->pluck('category_id')->toArray():[];
        $this->main_categories_selected= $id?Product::find($id)->pluck('main_category_id'):[];
//        $this->sub_categories_selected= $id?Product::find($id)->pluck('sub_category_id'):[];
        $this->getProductImages($id);


        $variation_result = array();
        $variation_array =$id?Variation::where('product_id',$id)->get()->toArray():[];
      //  dd($variation_array);
        $index = 0;
        //dd( $this->${'image_'.$index}->temporaryUrl());

        foreach ($variation_array as $key => $value) {
            $id =$key;
            if (!isset($variation_result[$id])) {
                $variation_result[$id] = [];
            }


            $variation_result[$id]['product_size'] =is_null($value['product_size'])?Size::where('id',$value['product_size_id'])->first()->size_ar:Size::where('name',$value['product_size'])->first()->size_ar;
            $variation_result[$id]['product_color'] = is_null($value['product_color'])?optional(Maincolor::find($value['product_color_id']))->id:Color::where('name',$value['product_color'])->first()->code;
            $variation_result[$id]['is_in_stock'] = $value['is_in_stock']==="Yes"?1:0;
            $variation_result[$id]['stock_quantity'] =$value['stock_quantity'];
            $variation_result[$id]['display_selling_price'] =$value['display_selling_price'];
            $variation_result[$id]['variation_id'] =$value['id'];
            $variation_result[$id]['status'] =$value['status'];


        }
        //dd($variation_result,$variation_array);
      // dd(  $this->inputs,$variation_result);
        $this->inputs= $variation_result;
        $this->image_1=collect();
        $this->uploader_images=collect();
        $this->offers = Offer::all();
        if (auth()->user()->type=="STORE"){
            $store = auth()->user()->store;
            if ($this->checkIfStoreHasActive($store->id)){
                $this->offers = Offer::where("store_id",auth()->user()->store_id)->orWhere("is_black",true)->get();
            }else{
                $this->offers =  Offer::where("store_id",auth()->user()->store_id)->get();
            }


        }

    }

    public function render()
       {

           $this->main_categories=\App\Models\Category::isMain()->get();

           $this->sub_categories=$this->product->main_category_id?\App\Models\SubCategory::where('main_category_id',$this->product->main_category_id)->whereNull('parent_id')->get():\App\Models\SubCategory::whereNull('parent_id')->get();
           $this->categories=$this->product->category_id?\App\Models\SubCategory::where('parent_id',$this->product->sub_category_id)->whereNotNull('parent_id')->get():\App\Models\SubCategory::whereNotNull('parent_id')->get();


           $this->offer_options=[
               ['value_id'=>'fixed','value'=>'fixed' ,'id'=>'fixed','name'=>'نسبة ثابتة'],
               ['value_id'=>'percentage','value'=>'percentage' ,'id'=>'percentage','name'=>'نسبة مئوية'],

           ];

           if(in_array('products_create'||'products_edit',$this->actions_permission()) ) {
               return view('dashboard.products.form')->extends('dashboard_layout.main');
           }else{
               return view('dashboard.not-authorized')->extends('dashboard_layout.main');
           }
       }

    public function add()
    {

        $values=['product_size'=>'','product_color'=>'','is_in_stock'=>'','stock_quantity'=>'','display_selling_price'=>''];
        $this->inputs[]=$values;
    }

    public function remove($i)
    {
        unset($this->inputs[$i]);
        $this->inputs = array_values($this->inputs);
    }
    public function save()
    {

       // dd($this->inputs);
        $this->validate();
        \DB::beginTransaction();
        try {
            $this->product->product_type="normal_product";
            if (auth()->user()->type=="STORE"){
                $this->product->store_id = auth()->user()->store->id;
            }

            if($this->offer_type == 'without_offer'){
                $this->product->new_price=null;

            }
            if($this->product->offer_discount_type === 'fixed'){
                $this->product->new_price= $this->product->profit_price-$this->product->percentage_value;
                $this->product->discount_amount=$this->product->percentage_value;
            }
            if($this->product->offer_discount_type === 'percentage'){
                $this->product->discount_amount=($this->product->profit_price * ($this->product->percentage_value / 100));
                $this->product->new_price=$this->product->profit_price-($this->product->profit_price * ($this->product->percentage_value / 100));
            }
            $this->product['is_new']?true:false;
            $this->product['is_available']?true:false ;
            $this->product->save();
            $this->uploadFiles();


             $variations_ids = (collect($this->inputs)->pluck("variation_id")->filter(function ($item){return $item!= null ;})->toArray());
              $toDeleteVariations =   \App\Models\Variation::where('product_id' ,$this->product->id);
              if ($variations_ids)
                  $toDeleteVariations->WhereNotIn("id",$variations_ids);

                $toDeleteVariations->delete();


            if($this->inputs){


                foreach ($this->inputs as $input){
                    if ($this->product->identifier == 'لون فقط'){
                        $input['product_size'] = null;
                    }
                    //create size
                    $find_size=\App\Models\Size::where('size_ar',$input['product_size'])->first();


                    if($find_size){
                        $size=$find_size;
                    }else{
                        $size= \App\Models\Size::firstOrCreate([
                            'size_ar' => $input['product_size'],
                        ]);
                    }

                    //create color

                    if(empty($input['product_color'])){
                        $near_color='without color';

                        $main_color= \App\Models\Maincolor::find(23);
                    }else{
                        $main_color= \App\Models\Maincolor::where('id',$input['product_color'])->first();


                    }

                    $color= \App\Models\Color::firstOrCreate([
                        'name' => $main_color->name,
                        'color_ar' => $main_color->human_name,
                        'code' => $main_color->code,
                        'human_name' => $main_color->human_name,
                        'main_color_id' =>$main_color->id,
                    ]);

                    $variation_price_after_offer= null;
                    $variation_offer_type=null;
                    $offer_price=null;
                    //find variation

                    if($this->product->offer_discount_type === 'fixed'){

                        $variation_price_after_offer= $input['display_selling_price']- $this->product->percentage_value;
                        $variation_offer_type='fixed';
                        $offer_price=$this->product->percentage_value;
                    }
                    if($this->product->offer_discount_type === 'percentage'){

                        $variation_price_after_offer=$input['display_selling_price']-($input['display_selling_price'] * ($this->product->percentage_value / 100));
                        $variation_offer_type='percentage';
                        $offer_price=$input['display_selling_price'] * ($this->product->percentage_value / 100);
                    }

                if (isset($input["variation_id"]) )
                    $find_variation = Variation::find($input["variation_id"]);
                else
                    $find_variation = null ;

                    if($find_variation){

                        $find_variation->update([
                            'product_id' => $this->product->id,
                            'category_id' => $this->product->category_id,
                            'product_size_id' => $size->id,
                            'product_color_id' => $main_color->id,
                            'is_in_stock' => $input['is_in_stock'] == 1 ? "Yes" : "No",
                            'stock_quantity' => $input['stock_quantity'],
                            'display_selling_price' => $input['display_selling_price'],
                            'offer_type' => $variation_offer_type,
                            'price_after_offer' => $variation_price_after_offer,
                            'offer_price' => $offer_price,
                        ]);

                    }else {
                        Variation::create([
                            'product_id' => $this->product->id,
                            'category_id' => $this->product->category_id,
                            'product_size_id' => $size->id,
                            'product_color_id' => $main_color->id,
                            'is_in_stock' => $input['is_in_stock'] == 1 ? "Yes" : "No",
                            'stock_quantity' => $input['stock_quantity'],
                            'display_selling_price' => $input['display_selling_price'],
                            'offer_type' => $variation_offer_type,
                            'price_after_offer' => $variation_price_after_offer,
                            'offer_price' => $offer_price,
                        ]);
                    }
//                    }

                }
            }

           \DB::commit();
            checkVariationQuantityForProduct($this->product->id);

        $this->showModal('تم الحفظ','تم حفظ التغيرات بنجاح','success',route('dashboard.products'));
        } catch (\Exception $e) {

            \DB::rollback();

            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');

        }
    }


    private function  ImageSaveAndGetFileName($object){
        $class_name=["Livewire\TemporaryUploadedFile"];

        if(in_array(get_class($object), $class_name)){
            $filename = $object->store('/','product');
        }else{

            $filename= isset($object['image'])?$object['image']:null;
        }

        return $filename;
    }
    private function getProductImages($id){

        if($id){
            $images = $this->product->product_images()->where('is_main','!=',true)->get();

            $this->image_main = $this->product->product_images()->where('is_main',true)->first();
            $this->images_uploaders =  [];
            foreach ($images as $image){
                $this->images_uploaders[]=['id'=>$image->id,'image_url'=>$image->image_url];//'data:image/jpg;base64,'.base64_encode(file_get_contents($image->image_url));;
            }


        }else{
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];


        }


    }

    private function saveProductImage(){


    $this->product->product_images?$this->product->product_images()->delete():null;

        if($this->image_main){
            $filename = $this->ImageSaveAndGetFileName($this->image_main);
            $this->product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>true,
            ]);
        }


        // image 1
        if($this->image1){
            $filename = $this->ImageSaveAndGetFileName($this->image1);
            $this->product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>false,
            ]);
        }
        // image 2
        if($this->image2){
            $filename =$this->ImageSaveAndGetFileName($this->image2);
            $this->product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>false,
            ]);
        }

        //image 3
        if($this->image3){
            $filename = $this->ImageSaveAndGetFileName($this->image3);
            $this->product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>false,
            ]);
        }
    }

    public function review_status($id){
        $review=Reviews::find($id);
        $review->status =!$review->status;
        $review->save();

    }


    public function updated($name,$value){


       if($name === 'product.main_category_id'){

           $this->sub_categories=\App\Models\SubCategory::where('main_category_id',$value)->whereNull('parent_id')->get();

       }
        if($name === 'product.sub_category_id'){

            $this->categories=\App\Models\SubCategory::where('parent_id',$value)->whereNotNull('parent_id')->get();


        }
    }


    public function removeImage($index){

//        $current_object =  $this->images_uploaders[$index];
//        dd($current_object,$this->images_uploaders);
//        if($current_object){
//            $current_object->delete();
//        }

//        unset($this->images_uploaders[$index]);
//        $this->images_uploaders = array_values($this->images_uploaders);
        array_splice( $this->images_uploaders, $index, 1);
       // dd($this->images_uploaders);
    }

    public function add_image(){
        $this->images_uploaders[] = ['id'=>0,'image_url'=>''];
    }

    public function singleFileUploaded($file){

        try {

            if($this->getFileInfo($file[0])["file_type"] == "image"){

                $this->images_uploaders[$file[1]]=['id'=>0,'image_url'=>$file[0]];

            }else{
                session()->flash("error", "Uploaded file must be an image");
            }
        } catch (Exception $e) {

        }
    }

    public function getFileInfo($file)
    {
        $info = [
            "decoded_file" => null,
            "file_meta" => null,
            "file_mime_type" => null,
            "file_type" => null,
            "file_extension" => null,
        ];

        try {
            $info['decoded_file'] = base64_decode(substr($file, strpos($file, ',') + 1));
            $info['file_meta'] = explode(';', $file)[0];
            $info['file_mime_type'] = explode(':', $info['file_meta'])[1];
            $info['file_type'] = explode('/', $info['file_mime_type'])[0];
            $info['file_extension'] = explode('/', $info['file_mime_type'])[1];
        } catch(Exception $e) {

        }

        return $info;
    }

    public function uploadFiles()
    {
      $ids = array_column($this->images_uploaders,'id');

       ProductImage::whereNotIn('id',$ids)->where('product_id',$this->product->id)->delete();
        if($this->image_main){
            $filename = $this->ImageSaveAndGetFileName($this->image_main);
            $this->product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>true,
            ]);
        }

        // multiple files upload
        foreach($this->images_uploaders as $value) {

            if($value['image_url']){
                if(str_starts_with($value['image_url'],'data:image')){
                    $file_data = $this->getFileInfo($value['image_url']);
                    $file_name = Str::random(20).'.'.$file_data['file_extension'];
                    $result = Storage::disk('product')->put($file_name, $file_data['decoded_file']);
                    $this->product->product_images()->create([
                        'image' =>$file_name ,
                        'is_main'=>false,
                    ]);

                }

            }
        }

    }

    public function checkIfStoreHasActive($store_id){
            return BlackFridayRequest::isActive($store_id)->first();
    }


}



