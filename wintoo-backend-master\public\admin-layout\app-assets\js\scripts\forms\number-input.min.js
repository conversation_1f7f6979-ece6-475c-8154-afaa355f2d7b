!function(i){"use strict";i("html");i(".touchspin").TouchSpin({buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),i(".touchspin-icon").TouchSpin({buttondown_txt:'<i class="feather icon-chevron-down"></i>',buttonup_txt:'<i class="feather icon-chevron-up"></i>'});var t=i(".touchspin-min-max");0<t.length&&t.TouchSpin({min:15,max:21}).on("touchspin.on.startdownspin",function(){var t=i(this);i(".bootstrap-touchspin-up").removeClass("disabled-max-min"),15==t.val()&&i(this).siblings().find(".bootstrap-touchspin-down").addClass("disabled-max-min")}).on("touchspin.on.startupspin",function(){var t=i(this);i(".bootstrap-touchspin-down").removeClass("disabled-max-min"),21==t.val()&&i(this).siblings().find(".bootstrap-touchspin-up").addClass("disabled-max-min")}),i(".touchspin-step").TouchSpin({step:5}),i(".touchspin-color").each(function(t){var n="btn btn-primary",s="btn btn-primary",o=i(this);o.data("bts-button-down-class")&&(n=o.data("bts-button-down-class")),o.data("bts-button-up-class")&&(s=o.data("bts-button-up-class")),o.TouchSpin({mousewheel:!1,buttondown_class:n,buttonup_class:s,buttondown_txt:'<i class="feather icon-minus"></i>',buttonup_txt:'<i class="feather icon-plus"></i>'})})}((window,document,jQuery));