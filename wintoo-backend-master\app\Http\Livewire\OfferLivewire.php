<?php

namespace App\Http\Livewire;

use App\Models\SystemDraw;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Offer;
class OfferLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Offer-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'offer';
        public function mount()
            {
                $searchable = Offer::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Offer::getColumnLang();
                $this->searchable =Offer::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }
        public function render()
           {
               if (auth()->user()->type == "STORE"){
                   $this->search_array["store_id"]=auth()->user()->store->id;
               }else{
                   $this->search_array["is_black"] = true ;
               }
               $data =Offer::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
               return view('dashboard/offer/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }
        public function search(){
                    $this->resetPage();
        }
        public function resetSearch(){
            $this->search_array=[];
         }
        public function edit($id){
                 return redirect()->route('dashboard.offer.edit',$id);
             }

        public function delete($id){
                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Offer-livewire:conformDelete',['id'=>$id]);
             }
        public function conformDelete($id){
                 $offer = Offer::find($id['id']);
                 if($offer->is_black){
                     $this->showModal('لا يمكن الحذف','لا يمكن حذف black friday','error');
                     return  true;
                 }
                 Offer::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
        public function setStatus($id){
        $object = Offer::find($id);
        $object->status =!$object->status;
        $object->save();
    }

}

