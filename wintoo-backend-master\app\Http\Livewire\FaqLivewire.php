<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Faq;
class FaqLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Faq-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'faq';
        public function mount()
            {
                $searchable = Faq::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Faq::getColumnLang();
                $this->searchable =Faq::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =Faq::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);

               if(in_array('faq_show',$this->actions_permission()) ){
                   return view('dashboard/faq/index',[ 'data'=>$data])->extends('dashboard_layout.main');
               }else{
                   return view('dashboard.not-authorized')->extends('dashboard_layout.main');
               }


           }

        public function search(){}
         public function resetSearch(){
            $this->search_array=[];
         }

            public function edit($id){
                if(!in_array('faq_edit',$this->actions_permission()) ){
                    abort(403, 'غير مصرح لك');
                }
                 return redirect()->route('dashboard.faq.edit',$id);
             }

             public function delete($id){
                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Faq-livewire:conformDelete',['id'=>$id]);
             }

             public function conformDelete($id){

                 if(!in_array('faq_delete',$this->actions_permission()) ){
                     abort(403, 'غير مصرح لك');
                 }
                 Faq::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
}

