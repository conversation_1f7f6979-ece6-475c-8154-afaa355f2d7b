<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactUS extends Model
{
use \App\Traits\PropertyGetter;
    protected $fillable=['email','description','phone'];
     protected $guarded = [];
     public  static function  getColumnLang(){
 	 	$columes=[
 	 	'email'=>[\Lang::get('ContactUS.email') ,1,true,false,[]],
 	 	'phone'=>[\Lang::get('ContactUS.phone') ,1,true,false,[]],
 	 	'description'=>[\Lang::get('ContactUS.description') ,1,true,false,[]],
 	 	   'actions'=>['الخيارات',2,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
     }
     public static function getSearchable(){
 	 	$columes=[
 	 	    'email'=>[\Lang::get('ContactUS.email')],
 	 	    'phone'=>[\Lang::get('ContactUS.phone')],

 	 	 ]; return $columes;
  }
     public function scopeSearch($query, $data) {
 	 	 if(isset($data["email"])){
 	 	   $query->where("email","LIKE","%".$data["email"]."%");}
 	 	 if(isset($data["phone"])){
 	 	   $query->where("phone","LIKE","%".$data["phone"]."%");}
 	 	 if(isset($data["description"])){
 	 	   $query->where("description","LIKE","%".$data["description"]."%");}
 	 	 return $query ;
 	 	 }

}
