<?php

namespace App\Http\Resources;

use App\Models\Customer;
use App\Models\Store;
use Illuminate\Http\Resources\Json\JsonResource;

class SystemDrawStoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $store = Store::find($this->winner_store_id);
        $user = Customer::find($this->winner_customer_id) ;
        return [
            'id'=>$this->id,
            "title" => $this->title,
            "winner"=>[
                "store"=>new StoreResource($store),
                "user"=>new CustomerResource($user),
            ],
            "prize_name"=>$this->prize_name,
            "datetime"=>toLocalDate($this->draw_date_format , 'Y/m/d H:i:s'),
            "date"=>toLocalDate($this->date),
            "is_end"=>$this->is_end,
            "store"=>new StoreResource($this->store),
        ];
    }
}
