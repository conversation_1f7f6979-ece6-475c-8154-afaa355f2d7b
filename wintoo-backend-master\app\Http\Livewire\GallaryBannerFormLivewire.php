<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
class GallaryBannerFormLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction;
    public $title = "اضافة بنر اعلاني ";
    public $gallary_banner;
    public $selected_category;
    public $image;
    public $options=[];
    public $selected_advertisement_category;

    protected $rules = [
        'gallary_banner.title'=>'required',
        'gallary_banner.link'=>'nullable',
        'gallary_banner.type'=>'nullable',
        'gallary_banner.product_or_category_id'=>'nullable',
        'gallary_banner.is_active'=>'required',

    ];
    public function mount($id =null)
    {
        $this->gallary_banner = $id?\App\Models\GallaryBanner::find($id):new \App\Models\GallaryBanner();
        $this->gallary_banner->is_active = $id?$this->gallary_banner->is_active:false;
        if($id){

            $this->title = 'تعديل البنارات';
        }
    }

    public function render()
    {
        $this->options=[
            ['value_id'=>'category_page','value'=>'category_page' ,'id'=>'category_page','name'=>'صفحة التصنيفات'],
            ['value_id'=>'product_page','value'=>'product_page' ,'id'=>'product_page','name'=>'صفحة المنتجات'],
            ['value_id'=>'other_page','value'=>'other_page' ,'id'=>'other_page','name'=>'صفحة اخرى'],
        ];
        if(in_array('gallary_banner_create'||'gallary_banner_edit',$this->actions_permission()) ) {
            return view('dashboard.gallary-banner.form')->extends('dashboard_layout.main');
        }else{
            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }

    }
    public function change() {





    }
    public function save(){

        $this->validate();

        \DB::beginTransaction();
        try {

          //  dd($this->gallary_banner,$this->image);

            $this->gallary_banner->image=$this->image?$this->image->store('/','gallary_banner'):$this->gallary_banner->image;
            $this->gallary_banner->save();

            \DB::commit();
            $this->showModal('تم الحفظ','تم حفظ التغيرات بنجاح','success',route('dashboard.gallary_banner'));
        } catch (\Exception $e) {

            \DB::rollback();
            //   dd($e);
            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            // Handle errors
            return true;
        }



    }


    public function update($id){
        $gallary_banner= \App\Models\GallaryBanner::find($id);
        $this->validate();
        dd($this->gallary_banner);
        \DB::beginTransaction();
        try {
            $filename = $this->image?$this->image->store('/','gallary_banner'):$gallary_banner->image;
            $this->gallary_banner->image=$filename;
            $gallary_banner->update([
                'type'=>$this->gallary_banner->type,
                'title'=>$this->gallary_banner->title,
                'link'=>$this->gallary_banner->link,
                'image'=>$filename,
                'product_or_category_id'=>$this->gallary_banner->product_or_category_id,
            ]);


            \DB::commit();
            $this->showModal('تم الحفظ','تم حفظ التغيرات بنجاح','success',route('dashboard.gallary_banner'));
        } catch (\Exception $e) {

            \DB::rollback();
            //   dd($e);
            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            // Handle errors
            return true;
        }



    }
}
