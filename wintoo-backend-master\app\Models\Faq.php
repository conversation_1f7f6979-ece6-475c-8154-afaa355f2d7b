<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
class Faq extends Model
{
use \App\Traits\PropertyGetter;
     use HasFactory,HasTranslations;

     protected $guarded = [];
     public $translatable = ['title','description'];
    protected $appends = ["video_url","media_thumbnail_url"];

    public  static function  getColumnLang(){
 	 	$columes=[
 	 	'title'=>[\Lang::get('faq.title') ,1,true,false,[]],
 	 	'description'=>[\Lang::get('faq.description') ,1,false,false,[]],
 	 	   'actions'=>['الخيارات',1,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 public static function getSearchable(){
 	 	$columes=['title'=>[\Lang::get('faq.title')],

 	 	 ]; return $columes;
  }
 	 public function scopeSearch($query, $data) {
 	 	 if(isset($data["title"])){
 	 	   $query->where("title","LIKE","%".$data["title"]."%");}
 	 	 if(isset($data["description"])){
 	 	   $query->where("description","LIKE","%".$data["description"]."%");}
 	 	 return $query ;
 	 	 }

    public function getVideoUrlAttribute(){
        return $this->video ? asset("storage/".$this->video):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->video))) ;

    }
    public function getMediaThumbnailUrlAttribute(){
        return $this->media_thumbnail?asset('storage/'.$this->media_thumbnail):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->media_thumbnail)));
    }
}
