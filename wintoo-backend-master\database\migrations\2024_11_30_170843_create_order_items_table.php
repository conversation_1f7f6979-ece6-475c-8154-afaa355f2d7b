<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrderItemsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('order_items', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('product_id')->unsigned();
			$table->integer('order_id')->unsigned();
			$table->float('price', 10, 0)->nullable();
			$table->integer('qty')->nullable();
			$table->timestamps();
			$table->string('variation_id', 191)->nullable();
			$table->text('variation_object')->nullable();
			$table->text('product_object')->nullable();
			$table->text('order')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('order_items');
	}

}
