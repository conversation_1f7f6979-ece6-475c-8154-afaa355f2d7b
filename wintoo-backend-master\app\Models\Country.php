<?php

namespace App\Models;

use <PERSON>run<PERSON><PERSON>\LaravelUserActivity\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Country extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    use Loggable,HasTranslations;

     protected $guarded = [];
     protected $appends = ["status_name"];
     public $translatable = ['name'];

    public  static function  getColumnLang(){
$columes=[
    'name'=>[\Lang::get('country.name') ,1,true,false, ['type'=>'string','actions'=>null] ],
    'status_name'=>[\Lang::get('country.status') ,1,true,false, ['type'=>'string','actions'=>null] ],
    'actions'=>['الخيارات',1,true,false,['type'=>'button','actions'=>['edit','delete']]],
];
 return $columes;
}
    public static function getSearchable(){
        $columes=[
            'name'=>[\Lang::get('Country.name')],
         ]; return $columes;
    }
    public function scopeSearch($query, $data) {
         if(isset($data["name"])){
           $query->where("name","LIKE","%".$data["name"]."%");}
         if(isset($data["status"])){
           $query->where("status","LIKE","%".$data["status"]."%");}
         return $query ;
    }
   public function cities(){
        return $this->hasMany(City::class,"country_id");
   }

    public function getStatusNameAttribute(){
        if($this->status){
            return ' مفعل';
        }
        return 'غير مفعل';
    }

}
