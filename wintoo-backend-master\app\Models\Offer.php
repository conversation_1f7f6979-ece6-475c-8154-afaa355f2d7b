<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use PhpParser\Node\Expr\FuncCall;

class Offer extends Model
{
use \App\Traits\PropertyGetter;


        protected $guarded = [];
        protected $appends=['image_url'];
        protected $casts =["status"=>"boolean"];

        public  static function  getColumnLang(){
 	 	$columes=[
 	 	'title'=>[\Lang::get('offer.title') ,1,true,false, ['type'=>'longText','actions'=>null] ],
// 	 	'store_id'=>[\Lang::get('offer.store_id') ,1,true,false, ['type'=>'integer','actions'=>null] ],
 	 	'status'=>[\Lang::get('offer.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
 	 	'start_date'=>[\Lang::get('offer.start_date') ,1,true,false, ['type'=>'string','actions'=>null] ],

 	 	'end_date'=>[\Lang::get('offer.end_date') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	   'actions'=>['الخيارات',4,true,false,['type'=>'button','actions'=>['edit','delete'],"condition"=>["is_black"=>false]]],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['title'=>[\Lang::get('offer.title')],
 	 	'store_id'=>[\Lang::get('offer.store_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'Store']],
 	 	'status'=>[\Lang::get('offer.status')],
 	 	'start_date'=>[\Lang::get('offer.start_date')],
 	 	'end_date'=>[\Lang::get('offer.end_date')],

 	 	 ]; return $columes;
  }
        public function scopeSearch($query, $data) {

 	 	 if(isset($data["title"])){
 	 	   $query->where("title","LIKE","%".$data["title"]."%");}
 	 	 if(isset($data["store_id"])){
 	 	   $query->where("store_id",$data["store_id"]);}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 if(isset($data["start_date"])){
 	 	   $query->where("start_date","LIKE","%".$data["start_date"]."%");}
 	 	 if(isset($data["is_black"])){
 	 	   $query->where("is_black",$data["is_black"]);}
 	 	 if(isset($data["end_date"])){
 	 	   $query->where("end_date","LIKE","%".$data["end_date"]."%");}
 	 	 return $query ;
 	 	 }

    public function store(){
        return $this->belongsTo(Store::class,"store_id");
    }
    public function scopeIsActive($query)
    {
        $endtDate = Carbon::today();

        return $query->where('status',true)
            ->where('is_black',false)
            ->whereHas('store')

            ->whereDate('start_date', '<=', $endtDate)
            ->whereDate('end_date', '>=', $endtDate);
    }


    public function scopeHasProducts($query)
    {
        return $query->whereHas("products");
    }




    public function scopeIsBlack($query)
    {
        $endtDate = Carbon::today();

        return $query
            ->where('status',true)
            ->where('is_black',true)
            ->whereDate('start_date', '<=', $endtDate)
            ->whereDate('end_date', '>=', $endtDate)

            ;
    }



    public function scopeHasActiveBlack($query)
    {
        $endtDate = Carbon::today();

        return $query
            ->where('status',true)
            ->where('is_black',true)
            ->whereDate('start_date', '<=', $endtDate)
            ->whereDate('end_date', '>=', $endtDate)

            ;
    }



    public function products(){
//        return $this->belongsToMany
//        (Product::class,
//            "product_offers",
//            "product_id",
//            "offer_id"); //parent_id
        return $this->hasMany(Product::class,"offer_id");
    }




    public function getImageUrlAttribute(){
        return $this->image ? asset("storage/".$this->image):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image))) ;
    }

}
