<?php

namespace App\Services;


use App\Models\DrawCandidate;

class CandidateTable{

    public static function save($object,$type){
        $data = [
            "order_id"=>$object->id,
            "qr_id"=>null,
            "customer_id"=>$object->customer_id,
            "store_id"=>$object->store_id,
            "system_draw"=>false,
            "store_draw"=>false,
        ];
        if ($type =="qr"){
            $data = [
                "order_id"=>null,
                "qr_id"=>$object->id,
                "customer_id"=>$object->customer_id,
                "store_id"=>$object->store_id,
                "system_draw"=>false,
                "store_draw"=>false,
            ];
        }
        $create =  DrawCandidate::create($data);

        return $create;
    }


}
?>
