<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('products', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('title', 191)->nullable()->index('title');
			$table->float('price', 10, 0)->nullable();
			$table->boolean('is_available')->nullable()->default(0);
			$table->text('description')->nullable();
			$table->integer('category_id')->unsigned()->nullable()->index('products_category_id_foreign');
			$table->integer('brand_id')->unsigned()->nullable()->index('products_brand_id_foreign');
			$table->timestamps();
			$table->boolean('is_new')->nullable()->default(0);
			$table->string('type', 191)->nullable()->default('product');
			$table->float('new_price', 10, 0)->nullable();
			$table->string('sell_number', 191)->nullable();
			$table->string('bar_code')->nullable();
			$table->boolean('status')->nullable()->default(1)->comment('status');
			$table->string('product_type', 191)->nullable()->default('normal_product');
			$table->text('tags')->nullable();
			$table->string('store_jan_product_id', 191)->nullable();
			$table->string('currency_2', 191)->nullable();
			$table->string('product_code', 191)->nullable();
			$table->string('sku', 191)->nullable();
			$table->string('stock_quantity', 191)->nullable();
			$table->string('variant_selling_price', 191)->nullable();
			$table->string('selling_price', 191)->nullable();
			$table->string('display_currency', 191)->nullable();
			$table->string('display_symbol', 191)->nullable();
			$table->string('total_quantity', 191)->nullable();
			$table->string('vendor', 191)->nullable();
			$table->string('sync_status', 191)->nullable();
			$table->string('specific_sale_price', 191)->nullable();
			$table->string('display_selling_price', 191)->nullable();
			$table->string('store_selling_price', 191)->nullable();
			$table->string('offer_discount_type', 191)->nullable();
			$table->string('percentage_value', 191)->nullable();
			$table->string('discount_amount', 191)->nullable();
			$table->bigInteger('main_category_id')->unsigned()->nullable();
			$table->bigInteger('sub_category_id')->unsigned()->nullable();
			$table->string('profit_type', 191)->nullable();
			$table->float('profit_amount', 10, 0)->nullable();
			$table->float('profit_price', 10, 0)->nullable()->default(0);
			$table->string('store_jan_status')->nullable();
			$table->string('identifier', 191)->nullable();
			$table->integer('store_id')->nullable();
			$table->integer('offer_id')->nullable();
			$table->boolean('is_hotsale')->default(0);
			$table->integer('currency_id')->unsigned()->nullable()->default(1)->index('products_currency_id_foreign');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('products');
	}

}
