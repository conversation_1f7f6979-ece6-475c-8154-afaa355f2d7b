$(document).ready(function(){var t={columnDefs:[{headerName:"First Name",field:"firstname",editable:!0,sortable:!0,filter:!0,width:175,checkboxSelection:!0,headerCheckboxSelectionFilteredOnly:!0,headerCheckboxSelection:!0},{headerName:"Last Name",field:"lastname",editable:!0,sortable:!0,filter:!0,width:175},{headerName:"Company",field:"company",editable:!0,sortable:!0,filter:!0,width:250},{headerName:"City",field:"city",editable:!0,sortable:!0,filter:!0,width:125},{headerName:"Country",field:"country",editable:!0,sortable:!0,filter:!0,width:150},{headerName:"State",field:"state",editable:!0,sortable:!0,filter:!0,width:125},{headerName:"Zip",field:"zip",editable:!0,sortable:!0,filter:!0,width:125},{headerName:"Email",field:"email",editable:!0,sortable:!0,filter:!0,width:260,pinned:"left"},{headerName:"Followers",field:"followers",editable:!0,sortable:!0,filter:!0,width:150}],rowSelection:"multiple",floatingFilter:!0,filter:!0,pagination:!0,paginationPageSize:20,pivotPanelShow:"always",colResizeDefault:"shift",animateRows:!0,resizable:!0},e=document.getElementById("myGrid");agGrid.simpleHttpRequest({url:"../../../app-assets/data/ag-grid-data.json"}).then(function(e){t.api.setRowData(e)}),$(".ag-grid-filter").on("keyup",function(){var e;e=$(this).val(),t.api.setQuickFilter(e)}),$(".sort-dropdown .dropdown-item").on("click",function(){var e,i=$(this);e=i.text(),t.api.paginationSetPageSize(Number(e)),$(".filter-btn").text("1 - "+i.text()+" of 500")}),$(".ag-grid-export-btn").on("click",function(e){t.api.exportDataAsCsv()}),new agGrid.Grid(e,t),$(window).width()<768?t.columnApi.setColumnPinned("email",null):t.columnApi.setColumnPinned("email","left"),$(window).on("resize",function(){$(window).width()<768?t.columnApi.setColumnPinned("email",null):t.columnApi.setColumnPinned("email","left")})});