<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'=>$this->id,
            "coupon_value"=>$this->coupon_value,
            "coupon_type"=>$this->coupon_type,
            "couponAmount"=>$this->coupon_type,
        ];//parent::toArray($request);
    }
}
