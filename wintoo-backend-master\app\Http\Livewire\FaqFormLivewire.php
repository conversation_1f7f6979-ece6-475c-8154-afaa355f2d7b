<?php

namespace App\Http\Livewire;

use App\Services\VideoEdit;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Faq;
class FaqFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'faq';
      public $faq;
    public $video;

    protected $listeners = ['Faq-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh', "single_file_uploaded" => 'singleFileUploaded',];
       protected $rules = [
                "faq.title.ar"=>'nullable',
                "faq.title.en"=>'nullable',
                "faq.description.ar"=>'nullable',
                "faq.description.en"=>'nullable',
                "faq.media_thumbnail"=>'nullable',

       ];
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->faq  = $id?Faq::find($id):new Faq();
          }
      public function render()
          {
              if(in_array('faq_edit',$this->actions_permission())){
                  return view('dashboard/faq/form')->extends('dashboard_layout.main');
              }else{
                  return view('dashboard.not-authorized')->extends('dashboard_layout.main');
              }
          }

      public function save(){
            $this->validate($this->rules);
           \DB::beginTransaction();
           try {

             //  dd($this->video , $this->faq->video) ;

               $video_online = $this->video ? $this->video->store('/','public') : $this->faq->video;
               $this->faq->video = $video_online;
               if ($video_online){

                   $this->faq->media_thumbnail = VideoEdit::generateVideoThumbnail($video_online);
               }
               //dd($this->faq);
               $this->faq->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.faq');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


