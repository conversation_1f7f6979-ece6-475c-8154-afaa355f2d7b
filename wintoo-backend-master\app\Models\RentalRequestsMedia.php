<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RentalRequestsMedia extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded = [];
    protected $appends = ['media_url'];
    public function getMediaUrlAttribute(){
     //   return $this->image?asset('storage/'.$this->image):null;
        return $this->image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->image]):null;

    }
}
