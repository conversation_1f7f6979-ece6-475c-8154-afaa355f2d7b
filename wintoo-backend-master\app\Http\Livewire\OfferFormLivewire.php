<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Offer;
class OfferFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'offer';
      public $offer;
      public $image;
      public $product_ids;
      protected $listeners = ['Offer-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
      protected $rules = [
                "offer.title"=>'required',
                "offer.store_id"=>'nullable',
                "offer.status"=>'nullable',
                "offer.start_date"=>'nullable',
                "offer.end_date"=>'nullable',
                "offer.image"=>'nullable',
       ];
      protected $validationAttributes;
      public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Offer::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->offer  = $id?Offer::find($id):new Offer();
          }
      public function render()
          {
              return view('dashboard/offer/form')->extends('dashboard_layout.main');
          }
      public function save(){

            $this->validate();
           \DB::beginTransaction();
            try {
                if (auth()->user()->type=="STORE"){
                    $this->offer->store_id = auth()->user()->store->id;
                }
                $filename = $this->image?$this->image->store('/','public'):$this->offer->logo;
                $this->offer->image = $filename;
                $this->offer->save();
//               $this->offer->products()->sync($this->product_ids);
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.offer');
            }
            catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }
}


