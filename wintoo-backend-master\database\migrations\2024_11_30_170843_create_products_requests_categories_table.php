<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsRequestsCategoriesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('products_requests_categories', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('icon', 191)->nullable()->comment('icon');
			$table->text('name')->nullable()->comment('name');
			$table->integer('order')->nullable()->comment('order');
			$table->timestamps();
			$table->boolean('status')->nullable()->comment('status');
			$table->string('type')->nullable()->default('product_request');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('products_requests_categories');
	}

}
