<?php

namespace App\Http\Resources;

use App\Models\Favorite;
use App\Models\Follower;
use App\Models\ProductRequest;
use App\Models\Reviews;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class ProductRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {




    $get_same_product = $this->getMatchingProductsRequestsAndNotify(false);


        $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');
        return [
            "id"=>$this->id,
            "store_category_id"=>$this->store_category_id,
            "image_url"=>$this->media[0]?$this->media[0]->media_url:null,
            "product_name"=>$this->product_name,
            "product_title"=>$this->product_title,
            "store_category"=>new StoreCategoryResource($this->store_category),
            "customer_id"=>$this->customer_id,
            "customer"=>new CustomerResource($this->customer),
            "store"=>new StoreResource($this->store),
            "hash"=>$this->hash,
            "type"=>$this->type,
            //"type"=>$this->type_name,
            "type_name"=>$this->type_name,
            "sell_price"=>$this->sell_price,
            "buy_price_from"=>$this->buy_price_from,
            "buy_price_to"=>$this->buy_price_to,
            "similarـrequest_count"=>count($get_same_product),
            "created_at"=>$created_at,
            "similarـrequests" => count($get_same_product)>0?ProductRequesSimilarResource::collection($get_same_product):[],
            "attributes"=>AttributeResource::collection($this->attributes),
            "media" => ProductRequestMediaResource::collection($this->media??[]) ,
            'country'=>new AreaResource($this->country),
            'governorate'=>new AreaResource($this->governorate),
            'city'=>new AreaResource($this->city),
            'region'=>new AreaResource($this->region),
            "full_address"=> $this->full_address,
            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource($this->currency),

        ];
    }
}
