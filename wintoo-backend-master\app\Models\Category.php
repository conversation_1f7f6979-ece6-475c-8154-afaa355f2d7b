<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
use Spatie\Translatable\HasTranslations;
class Category extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    use Loggable,HasTranslations;
    protected $connection = "mysql";
    protected $fillable = ['parent_id', 'name','image','banner'];
    protected $appends = ['image_url','banner_url'];

    public $translatable = ['name'];


    public function getImageUrlAttribute(){
        return $this->image?asset('storage/'.$this->image):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image)));

    }
    public function getBannerUrlAttribute(){

        return $this->banner?asset('storage/'.$this->banner):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->banner)));

    }
    public function parent()
    {
        return $this->belongsTo('App\Models\Category', 'parent_id');
    }

    public function children()
    {
        return $this->hasMany('App\Models\SubCategory', 'main_category_id'); //parent_id
    }

    public function active_children()
    {
        return $this->hasMany('App\Models\SubCategory', 'main_category_id')->where('status', 1); //parent_id
    }

    public function products()
    {
        return $this->hasMany(Product::class,'main_category_id');
    }

    public function scopeIsMain($q){
        return $q->whereNull('parent_id');
    }

    public function scopeIsActive($query){
        return $query->where('status',1);
    }
    public function scopeInStoreCategory($query,$id){
        //return $query->whereHas('status',1);

        return $query->whereHas('store_category', function($q) use($id){
            $q->where('store_categories.id',$id );
        });

      /*  return $query->whereHas('products',function ($query)use($id){
            $query->where("store_id" ,$id );
        });
*/

    }
    public function scopeWithStoreProducts($query,$id){
        //return $query->whereHas('status',1);


       return $query->whereHas('products',function ($query)use($id){
            $query->where("store_id" ,$id );
        });


    }


    public  static function  getColumnLang(){

        $columes=[
            'image'=>['الشعار',1,true,false,[]],
            'name'=>['الاسم',2,true,true,[]],
            'id'=>['رقم التصنف',2,true,true,[]],
            'category_type'=>['رئيسي/فرعي',3,false,false,[]],
            'belong_to'=>['التصنيف الرئيسي',3,false,false,[]],
            'status'=>['الحالة',3,true,false,[]],
            'is_main'=>['عرض في الرئيسية',3,true,false,[]],
            'actions'=>['الخيارات',4,true,false,['edit','delete']],
        ];

        return $columes;


    }

    public function setStatus()
    {
        $this->status = !$this->status;
    }

    public function store_category(){
        return $this->belongsToMany(StoreCategory::class,"category_store_categories","category_id","store_category_id"); //parent_id

    }

}
