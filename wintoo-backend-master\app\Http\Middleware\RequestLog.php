<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RequestLog
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $data = [
            'url'=>request()->fullUrl(),
            'data'=>request()->all()
        ];
        file_put_contents('RequestLog.log',print_r($data,true).'\n',FILE_APPEND);
        return $next($request);
    }
}
