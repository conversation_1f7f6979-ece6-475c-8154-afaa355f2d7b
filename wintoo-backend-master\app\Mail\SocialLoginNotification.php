<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SocialLoginNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $provider;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $provider)
    {
        $this->user = $user;
        $this->provider = ucfirst($provider); // Capitalize provider name
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Welcome to ' . config('app.name'))
            ->view('emails.social-login-notification')
            ->with([
                'user' => $this->user,
                'provider' => $this->provider,
            ]);
    }
}
