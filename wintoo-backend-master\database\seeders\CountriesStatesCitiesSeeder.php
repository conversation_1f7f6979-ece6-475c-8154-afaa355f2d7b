<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;

class CountriesStatesCitiesSeeder extends Seeder
{
    public function run()
    {
        $filePath = database_path('seeders/data/cities.csv');
        if (!file_exists($filePath)) {
            $this->command->error("CSV file not found at $filePath");
            return;
        }

        // Open the CSV file using League\Csv
        $csv = Reader::createFromPath($filePath, 'r');
        $csv->setHeaderOffset(0); // The first row contains the header

        $records = $csv->getRecords(); // Get an iterable object

        $allowedCountryCodes = [
            'EG', // Egypt
            'AE', // UAE
            'JO', // Jordan
            'IL', // Israel
            'SA', // Saudi Arabia
            'DE', // Germany
            'US', // United States
            'TR', // Turkey
            'RU', // Russia
            'CA', // CA
            'MX', // Mexico
            'CN', // China
        ];
        foreach ($records as $data) {
            $countryCode = $data['country_code'] ?? null;
            if (!in_array($countryCode, $allowedCountryCodes)) {
                continue; // Skip to the next iteration
            }
            // Process Country
            $countryNameJson = json_encode([
                'en' => $data['country_name'], // Add translations if available
                'tr' => $data['country_name'], // Add translations if available
                'he' => $data['country_name'], // Add translations if available
                'ar' => !empty($data['country_name_ar']) ? $data['country_name_ar']: $data['country_name'], // Adjust as per your data
            ]);

            DB::table('countries')->updateOrInsert(
                ['key' => $data['country_code']],
                ['name' => $countryNameJson, 'status' => 1, 'key' => $data['country_code']]
            );
            $country = DB::table('countries')->where('key', $data['country_code'])->first();

            $countryId = $country ?-> id;

            
            // Process State (Governorate)
            $stateNameJson = json_encode([
                'en' => $data['state_name'], // Add translations if available
                'tr' => $data['state_name'], // Add translations if available
                'he' => $data['state_name'], // Add translations if available
                'ar' => !empty($data['state_name_ar']) ? $data['state_name_ar'] : $data['state_name'], // Adjust as per your data
            ]);

            DB::table('governorates')->updateOrInsert(
                ['key' => $data['state_code']],
                [
                    'name' => $stateNameJson,
                    'country_id' => $countryId,
                    'key' => $data['state_code']
                ]
            );
            $state = DB::table('governorates')->where('key', $data['state_code'])->first();

            $stateId = $state ?-> id;

            // Process City
            $cityNameJson = json_encode([
                'en' => $data['name'], // Add translations if available
                'ar' => !empty($data['city_name_ar']) ? $data['city_name_ar'] : $data['name'], // Adjust as per your data
                'tr' => $data['name'], // Adjust as per your data
                'he' => $data['name'], // Adjust as per your data
            ]);

            DB::table('cities')->updateOrInsert(
                ['key' => $data['name'], 'governorate_id' => $stateId, 'country_id' => $countryId],
                [
                    'name' => $cityNameJson,
                    'governorate_id' => $stateId,
                    'country_id' => $countryId,
                    'key' => $data['name']
                ]
            );
        }

        $this->command->info('Countries, states, and cities have been seeded successfully.');
    }
}
