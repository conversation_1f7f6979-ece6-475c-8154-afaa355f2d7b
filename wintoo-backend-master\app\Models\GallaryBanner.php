<?php

namespace App\Models;

use App\Http\Resources\CategoryResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
class GallaryBanner extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    use Loggable;
    protected $guarded=[];
    protected $appends=['image_url','category'];


    public function getImageUrlAttribute(){

        return $this->image?asset('storage/gallary_banner/'.$this->image):asset('images/gallary_baner.png');
    }
    public function getCategoryAttribute(){

        if($this->type =='category_page'){
            return new CategoryResource(Category::find($this->product_or_category_id)) ;
        }
    }

    public function scopeIsActive($query)
    {
        return $query->where('is_active', 1);
    }

    public  static function  getColumnLang(){

        $columes=[
            'image'=>['صورة الاعلان',1,true,false,[]],
            'title'=>['اسم الاعلان',2,true,true,[]],
            // 'page_type'=>['الصفحة',3,true,false,[]],
            'created_at'=>['تاريخ الانشاء',4,true,false,[]],
            // 'status'=>['الحالة',5,true,true,[]],
            'actions'=>['الخيارات',6,true,false,['edit','show','delete']],
        ];

        return $columes;


    }
}
