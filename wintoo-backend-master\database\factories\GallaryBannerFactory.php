<?php

namespace Database\Factories;

use App\Models\GallaryBanner;
use Illuminate\Database\Eloquent\Factories\Factory;

class GallaryBannerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = GallaryBanner::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'is_active'=>$this->faker->randomElement([1,0]),
            'title'=>$this->faker->domainName,
            'image'=>'hHUX4Vm2uryHyh0BqHyhJpwnYu2viBzXlfwtXup2.png'
        ];
    }
}
