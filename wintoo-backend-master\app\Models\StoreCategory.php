<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
class StoreCategory extends Model
{
use \App\Traits\PropertyGetter;


 use HasTranslations;
     protected $guarded = [];
     protected $appends = ["icon_url"];
     public $translatable = ['name'];
     public  static function  getColumnLang(){
 	 	$columes=[
 	 	'icon_url'=>[\Lang::get('storeCategory.icon') ,1,true,false, ['type'=>'image','actions'=>null] ],
 	 	'name'=>[\Lang::get('storeCategory.name') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	'order'=>[\Lang::get('storeCategory.order') ,1,true,false, ['type'=>'integer','actions'=>null] ],
 	 	'status'=>[\Lang::get('storeCategory.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
 	 	   'actions'=>['الخيارات',2,true,false,['type'=>'button','actions'=>['edit','delete']]],
 	 	];

 	 	 return $columes;
  }
     public static function getSearchable(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('storeCategory.name')],

 	 	 ]; return $columes;
  }
     public function scopeSearch($query, $data) {
 	 	 if(isset($data["icon"])){
 	 	   $query->where("icon","LIKE","%".$data["icon"]."%");}
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["order"])){
 	 	   $query->where("order","LIKE","%".$data["order"]."%");}
 	 	 return $query ;
 	 	 }
     public function getIconUrlAttribute(){
        return asset('storage/' . $this->icon);
    }

    public function attributes(){
        return $this->belongsToMany(Attribute::class,"attribute_category","category_id","attribute_id");
    }

}
