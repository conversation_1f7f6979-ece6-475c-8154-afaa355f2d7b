$(document).ready(function(){"use strict";var e="ltr";"rtl"==$("html").data("textdirection")&&(e="rtl");var t=$(".sidebar-shop"),s=$(".shop-content-overlay"),i=$(".shop-sidebar-toggler"),o=$(".price-options"),n=$(".grid-view-btn"),a=$(".list-view-btn"),r=$("#ecommerce-products"),l=$(".cart"),c=$(".wishlist");i.on("click",function(){t.toggleClass("show"),s.toggleClass("show")}),$(".shop-content-overlay, .sidebar-close-icon").on("click",function(){t.removeClass("show"),s.removeClass("show")});var d=document.getElementById("price-slider");d&&noUiSlider.create(d,{start:[51,5e3],direction:e,connect:!0,tooltips:[!0,!0],format:wNumb({decimals:0}),range:{min:51,max:5e3}}),0<o.length&&o.select2({minimumResultsForSearch:-1,dropdownAutoWidth:!0,width:"100%"}),n.on("click",function(){r.removeClass("list-view").addClass("grid-view"),a.removeClass("active"),n.addClass("active")}),a.on("click",function(){r.removeClass("grid-view").addClass("list-view"),n.removeClass("active"),a.addClass("active")}),l.on("click",function(){var e=$(this),t=e.find(".add-to-cart"),s=e.find(".view-in-cart");if(t.is(":visible"))t.addClass("d-none"),s.addClass("d-inline-block");else{var i=s.attr("href");window.location.href=i}}),$(".view-in-cart").on("click",function(e){e.preventDefault()}),c.on("click",function(){var e=$(this);e.find("i").toggleClass("fa-heart-o fa-heart"),e.toggleClass("added")});var v=$(".checkout-tab-steps"),h=v.show();0<v.length&&($(v).steps({headerTag:"h6",bodyTag:"fieldset",transitionEffect:"fade",titleTemplate:'<span class="step">#index#</span> #title#',enablePagination:!1,onStepChanging:function(e,t,s){return s<t||(t<s&&(h.find(".body:eq("+s+") label.error").remove(),h.find(".body:eq("+s+") .error").removeClass("error")),1===t&&Number($(".form-control.required").val().length)<1&&toastr.warning("Error","Please Enter Valid Details",{positionClass:"toast-bottom-right"}),h.validate().settings.ignore=":disabled,:hidden",h.valid())}}),$(".place-order, .delivery-address").on("click",function(){$(".checkout-tab-steps").steps("next",{})}),$(".btn-cvv").on("click",function(){3==$(".input-cvv").val().length?toastr.success("Success","Payment received Successfully",{positionClass:"toast-bottom-right"}):toastr.warning("Error","Please Enter Valid Details",{positionClass:"toast-bottom-right"})}));var m=$(".quantity-counter");0<m.length&&m.TouchSpin({min:1,max:10}).on("touchspin.on.startdownspin",function(){var e=$(this);$(".bootstrap-touchspin-up").removeClass("disabled-max-min"),1==e.val()&&$(this).siblings().find(".bootstrap-touchspin-down").addClass("disabled-max-min")}).on("touchspin.on.startupspin",function(){var e=$(this);$(".bootstrap-touchspin-down").removeClass("disabled-max-min"),10==e.val()&&$(this).siblings().find(".bootstrap-touchspin-up").addClass("disabled-max-min")}),$(".remove-wishlist , .move-cart").on("click",function(){$(this).closest(".ecommerce-card").remove()})}),$(window).on("resize",function(){991<=$(window).outerWidth()&&($(".sidebar-shop").removeClass("show"),$(".shop-content-overlay").removeClass("show"))});