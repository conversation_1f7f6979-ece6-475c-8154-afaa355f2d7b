<?php

namespace App\Utils;

use Exception;

class AppleUtils
{
    public static function base64UrlDecode($input)
    {
        // Replace URL-safe characters and add padding
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $input .= str_repeat('=', 4 - $remainder);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }

    public static function extractDataFromAppleIdToken($idToken)
    {
        try {
            // Split the JWT into its components
            list($header, $payload, $signature) = explode('.', $idToken);

            // Decode the payload
            $decodedPayload = self::base64UrlDecode($payload);

            // Parse the payload into a PHP array
            $data = json_decode($decodedPayload, true);

            if (!$data) {
                throw new Exception("Unable to decode payload.");
            }

            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function extractData($idToken)
    {
        // Example usage
        $userData = self::extractDataFromAppleIdToken($idToken);
        return $userData;
    }
}
