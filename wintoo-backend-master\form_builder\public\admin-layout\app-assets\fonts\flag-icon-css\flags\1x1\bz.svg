<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="512" width="512">
  <defs>
    <clipPath id="a">
      <path fill-opacity=".67" d="M124.02 0h496.06v496.06H124.02z"/>
    </clipPath>
  </defs>
  <g clip-path="url(#a)" transform="translate(-128) scale(1.032)">
    <path fill-rule="evenodd" fill="#c60000" d="M0 0h744.09v496.06H0z"/>
    <path fill-rule="evenodd" fill="#003bb2" d="M0 70.866h744.09v354.33H0z"/>
    <path d="M513.77 248.028c0 78.276-63.456 141.73-141.73 141.73-78.276 0-141.73-63.458-141.73-141.73 0-78.277 63.458-141.73 141.73-141.73 78.278 0 141.73 63.457 141.73 141.73z" fill-rule="evenodd" fill="#fff"/>
    <g stroke="#000" fill-rule="evenodd">
      <path d="M0 318.04c6.927 1.02 14.39 1.206 23.312 0 7.405 1.994 12.863 5.647 18.317 8.326 3.056 5.665 7.338 14 11.655 18.316 1.874 6.872 4.64 9.755 8.326 16.652.92 6.795 3.183 13.617 0 19.98-3.795 4.746-9.757 7.516-14.985 9.992-6.902 3.688-10.937 5.046-19.982 6.66-7.444 1.33-15.03 1.666-23.312 1.666-8.012-1.43-14.19-2.757-21.646-3.33-7.414-1.324-15.898-2.007-23.312-3.33-4.813-2.816-11.026-5.127-16.65-6.66-3.908-3.726-9.56-7.954-14.987-11.657-4.19-7.502-8.004-10.206-11.656-16.652-3.318-4.876-4.892-11.17-4.997-19.98 2.625-5.345 6.96-10.53 9.99-14.987 5.198-3.433 11.073-7.99 18.317-11.656 9.784-.346 13.604-3.214 23.312-3.33h24.977c5.65.565 8.44 5.583 13.32 9.99z" transform="matrix(.206 0 0 .176 398.135 146.308)" stroke-width="3.75" fill="#030"/>
      <path d="M273.08 659.39c-6.66-38.298-16.652-133.21-16.652-174.84 1.665-31.64 121.56-139.87 124.89-151.53 3.33-11.656 64.94-99.91 74.93-126.55-24.976 18.316-66.605 98.243-74.93 106.57-8.326 8.326 18.316-83.257 9.99-111.56-8.325 33.303-21.646 118.22-31.637 126.55-4.994 4.996-91.582 104.9-101.57 101.57-9.99-3.33-36.632-19.98-31.636-29.972 4.995-9.99 71.6-71.6 74.93-93.248s8.327-73.266 14.987-78.26c6.662-4.997 16.653-29.974 24.978-49.956-21.646 9.99-24.977 39.964-33.302 41.63-8.326 1.664-28.308-33.304-28.308-33.304s13.32 38.298 14.986 46.624c1.666 8.326-6.66 63.275-11.656 69.936-4.995 6.66-41.628 53.284-48.29 54.95-6.66 1.665-24.976-76.597-24.976-91.583s13.32-73.266 0-76.597c-8.326 0-9.99 68.27-14.986 68.27s-16.652-68.27-16.652-68.27-6.66 31.638-3.33 53.284c-9.99 1.666-36.633-39.962-36.633-39.962s49.955 86.587 59.947 89.917c9.99 4.994 19.98 68.27 18.316 71.6s-49.954-16.65-59.945-23.312c-9.99-6.66-29.973-79.927-29.973-79.927s9.99 71.602-1.665 61.612c-11.656-9.99-29.972-18.317-39.963-33.303-9.99-14.987-4.995-76.598-4.995-76.598s-16.65 54.95-23.312 54.95c-6.66 0-28.307-73.266-28.307-73.266s3.33 79.927 13.32 84.922c9.99 4.995 159.85 104.9 161.52 114.9 1.666 9.99 19.983 48.288 29.974 59.944-1.666 21.647 6.66 161.52 9.99 194.82l29.973 9.99z" transform="matrix(.206 0 0 .176 329.595 130.477)" stroke-width="1pt" fill="#520900"/>
      <path d="M-338.02 133.21c1.85 6.416 2.528 12.146 11.656 14.986 5.054 1.925 15.007 4.53 19.982 6.66 5.242 2.484 11.14 5.594 18.316 6.66 5.394 1.8 14.58 3.65 21.647 4.997 7.444 1.33 15.03 1.665 23.313 1.665 9.557-1.707 13.81-3.43 19.98-8.326 1.852-6.786 4.344-9.832 4.997-18.317.567-7.944 1.665-14.82 1.665-23.31-1.54-4.624-1.79-15.36-3.33-19.983-5.577-5.466-7.05-8.65-16.652-11.656-7.476-3.382-14.307-3.33-23.312-3.33-6.96 2.32-15.343 1.665-23.31 1.665h-23.312c-6.973 1.517-12.292 2.763-18.317 6.66-8.132 3.674-9.337 6.318-13.32 14.988-2.934 8.8-3.036 15.415 0 26.642z" transform="matrix(.206 0 0 .176 396.08 140.444)" stroke-width="3.75" fill="#030"/>
      <path d="M-329.7 191.49c0-2.78-6.02 2.69-8.326 4.995-5.392 3.774-7.647 8.815-8.326 18.317-2.502 5.19 0 13.714 0 21.646 0 9.18.062 15.585 4.996 19.982 5.598 3.445 8.29 4.995 18.316 4.995h46.624c5.896-1.965 13.09-2.953 19.982-4.995 7.705-4.085 13.07-4.517 21.646-6.66 4.883-4.238 11.635-8.6 19.982-9.99 9.547-.456 15.257-3.79 21.647-8.327 8.217-2.054 15.636-6.365 21.648-9.99 4.837-3.29 8.31-7.416 11.656-13.322 3.396-7.924 4.482-12.894-1.665-21.646-12.144-4.415-6.11-9.19-14.988-13.32-4.062-2.454-13.524-5.674-18.316-8.327-8.293.615-14.192 2.68-23.312 3.33-4.71 2.356-14.368 1.666-21.647 1.666h-26.642c-7.968 0-16.35-.656-23.312 1.665-6.667 1.818-9.985 4.842-16.65 6.66-4.394 2.987-11.508 5.41-16.653 8.326l16.652-8.326c-4.393 2.987-11.507 5.41-23.312 13.322z" transform="matrix(.206 0 0 .176 395.05 142.203)" stroke-width="3.75" fill="#030"/>
      <path d="M-186.5 49.95c3.314 0-5.2 3.98-8.326 6.66-4.01 7.063-4.474 13.422-6.66 19.98-2.566 5.773-5.727 13.067-6.66 19.983-3.14 7.494-4.88 10.226-4.997 19.982 0 7.677-.09 16.38 1.665 21.646 5.05 6.363 9.9 9.977 18.317 14.987 7.206 2.495 12.49 3.984 19.98 4.996h23.313c5.267 1.756 13.97 1.665 21.646 1.665 8.492-3.172 11.708-7.208 19.98-8.326 9.116-.828 10.688-4.642 14.987-11.656 3.998-5.168 5.674-11.013 6.66-18.316 0-8.4-.75-15.628-1.665-23.312-4.006-4.494-7.052-10.696-9.99-18.316-5.146-8.82-5.152-12.74-13.32-16.65-3.977-5.43-10.85-8.07-14.988-11.657-8.65-.618-15.218-1.694-21.646-4.995-7.678 0-16.38-.09-21.647 1.665-6.03 0-10.446.57-16.65 1.665z" transform="matrix(.206 0 0 .15 396.764 145.755)" stroke-width="3.75" fill="#030"/>
      <path d="M-58.28 64.935c-.987 0-4.077 4.824-6.66 9.99-4.344 6.916-4.925 13.316-8.326 18.317-1.33 7.443-1.665 15.028-1.665 23.312.754 8.314 3.195 11.323 8.324 14.987 7.713 4.38 11.367 6.412 19.982 8.326 7.447.573 12.8 1.665 21.647 1.665 7.968 0 16.35.657 23.312-1.664 8.283 0 15.87-.336 23.312-1.665 3.823-3.24 12.04-6.14 16.65-8.324 5.483-6.642 8.107-13.245 11.657-18.317 0-9.823.078-15.565-4.995-23.313-3.622-6.388-7.962-9.32-13.323-14.986-6.718-2.336-12.38-7.195-19.982-8.325H-58.28z" transform="matrix(.206 0 0 .176 395.393 139.858)" stroke-width="3.75" fill="#030"/>
      <path d="M76.596 76.59c-6.286 2.62-8.59 3.3-11.656 9.992-4.62 7.115-7.342 10.462-11.656 14.986-.99 6.664-3.643 13.88-6.66 18.317-.915 7.684-1.666 14.913-1.666 23.312 4.222 6.486 10.085 12.603 16.65 16.65 6.792 2.352 13.95 5.933 19.983 8.327 7.767 2.033 14.952 4.398 23.313 4.995 7.217 1.804 15.164 1.664 23.312 1.664 7.404-1.61 13.75-3.478 19.98-8.325 3.334-5.197 7.157-11.046 8.327-19.983 2.754-8.263.54-19.06-3.33-26.642-2.04-7.477-5.016-10.373-13.32-14.987-4.596-4.854-10.703-10.322-14.988-13.32-5.28-4.585-12.142-5.154-19.98-8.327-8.493-1.886-11.338-4.33-19.983-4.994l19.983 4.995c-8.488-1.885-11.333-4.33-28.304-6.66z" transform="matrix(.206 0 0 .176 393.337 146.6)" stroke-width="3.75" fill="#030"/>
      <path d="M-51.62 146.53c-7.617 3.463-11.16 7.635-16.65 13.32 0 8.847 1.092 14.2 1.665 21.648 3.466 4.734 7.144 14.25 9.99 18.316 2.173 6.812 6.922 12.137 9.992 16.652 7.69 3.112 15.495 5.44 23.312 8.325 9.197 0 15.097 2.06 23.31 3.332 6.963 2.32 15.345 1.665 23.313 1.665 8.147 0 16.094-.14 23.312 1.665 7.703 1.37 13.396 4.36 21.647 4.995 4.74-.45 13.136-1.138 19.982-1.665 7.667-3.485 10.982-5.707 14.987-11.656 3.753-4.542 3.33-11.267 3.33-19.98 1.603-8.973 2.093-15.008-1.664-23.313-.108-8.96-1.817-13.172-3.33-21.647-3.006-6.01-5.647-8.65-11.657-11.656-5.465-3.808-12.864-5.998-18.317-9.99-7.433-.886-15.76-2.05-21.647-3.33-6.51-4.342-11.933-6.546-21.647-6.662-9.515-1.132-13.997-2.263-21.648 1.665-7.99.57-15.15 2.17-19.982 4.996-6.144.47-15.283.98-19.982 3.33-7.414 0-9.987 1.287-16.65 1.664l16.65-1.665c-7.414 0-9.987 1.287-18.316 9.99z" transform="matrix(.26 0 0 .136 390.547 151.866)" stroke-width="3.75" fill="#030"/>
      <path d="M-123.22 278.07c-4.3-.632-17.278-1.665-26.642-1.665-8.747 0-15.86.58-23.312 3.33-7.975 1.537-13.883 4.047-19.982 6.66-5.053 2.394-10.953 4.83-14.986 8.326-4.955 3.37-9.13 6.336-13.32 13.322-4.477 6.422-4.945 10.56-8.327 18.317v23.31c.588 7.634 1.64 14.23 4.996 21.648 5.022 4.017 11.977 5.282 18.317 6.66 6.03 1.31 13.875 2.775 21.647 3.33 9.24 0 14.16.466 21.646 3.33 9.747-.36 11.606-2.627 19.982-6.66 9.314-3.692 14.86-5.06 23.312-9.99 5.173-4.08 8.578-10.985 11.656-18.317 4.69-7.574 7.91-14.885 11.656-21.646 1.804-7.218 1.665-15.164 1.665-23.312-2.22-6.258-5.752-8.905-9.99-14.986-4.617-5.05-6.673-8.946-9.992-13.32l9.99 13.32c-4.615-5.05-6.67-8.946-18.315-11.656z" transform="matrix(.206 0 0 .176 397.107 143.376)" stroke-width="3.75" fill="#030"/>
      <path d="M0 318.04c6.927 1.02 14.39 1.206 23.312 0 7.405 1.994 12.863 5.647 18.317 8.326 3.056 5.665 7.338 14 11.655 18.316 1.874 6.872 4.64 9.755 8.326 16.652.92 6.795 3.183 13.617 0 19.98-3.795 4.746-9.757 7.516-14.985 9.992-6.902 3.688-10.937 5.046-19.982 6.66-7.444 1.33-15.03 1.666-23.312 1.666-8.012-1.43-14.19-2.757-21.646-3.33-7.414-1.324-15.898-2.007-23.312-3.33-4.813-2.816-11.026-5.127-16.65-6.66-3.908-3.726-9.56-7.954-14.987-11.657-4.19-7.502-8.004-10.206-11.656-16.652-3.318-4.876-4.892-11.17-4.997-19.98 2.625-5.345 6.96-10.53 9.99-14.987 5.198-3.433 11.073-7.99 18.317-11.656 9.784-.346 13.604-3.214 23.312-3.33h24.977c5.65.565 8.44 5.583 13.32 9.99z" transform="matrix(.264 0 0 .147 403.708 141.11)" stroke-width="3.75" fill="#030"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.711" fill="#006a00">
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 345.428 200.725)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 305.086 199.554)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 367.842 200.138)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.166 -.048 .032 .153 339.985 219.52)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 382.785 209.503)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 365.852 211.26)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 288.15 199.554)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.166 -.048 .032 .153 260.29 218.935)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 303.095 208.92)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.17 -.013 .01 .157 286.16 210.674)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(-.346 -.014 .02 -.157 454.53 392.91)"/>
    </g>
    <path d="M356.34 196.48c0 22.512-18.995 6.66-38.298 6.66-19.302 0-36.632 17.517-39.962 0 0-9.19 25.655-23.312 44.957-23.312 14.308 1.666 36.634-4.196 33.303 16.652z" fill-rule="evenodd" transform="matrix(.282 0 0 .31 329.218 218.038)" stroke="#000" stroke-width="2.5" fill="#510800"/>
    <path d="M356.34 196.48c0 22.512-18.995 6.66-38.298 6.66-19.302 0-36.632 17.517-39.962 0 0-9.19 25.655-23.312 44.957-23.312 14.308 1.666 36.634-4.196 33.303 16.652z" fill-rule="evenodd" transform="matrix(-.282 0 0 .31 423.416 216.625)" stroke="#000" stroke-width="2.5" fill="#ffc600"/>
    <path d="M349.68 188.16c0 13.794-7.082 24.977-15.82 24.977-8.736 0-15.818-11.183-15.818-24.977s7.082-24.977 15.82-24.977c8.735 0 15.818 11.183 15.818 24.977z" fill-rule="evenodd" transform="matrix(.348 0 0 .34 328.462 212.16)" stroke="#000" stroke-width="2.5" fill="#520900"/>
    <g fill-rule="evenodd" stroke="#000" stroke-width="1pt" transform="matrix(-.645 -.12 -.383 .645 582.597 96.792)">
      <path d="M273.75 383.58c-2.737 4.56 42.976 12.952 57.694 22.37 14.72 9.42 14.718 10.01 21.194 14.72l1.765-2.945C350.87 414.193 315.91 391.37 292 383.58c-6.805-2.273-10.3-13.248-12.95-8.83l-5.3 8.83z" transform="matrix(-.723 -.545 .52 -.758 322.39 677.92)" fill="#af8000"/>
      <path d="M319.08 369.45l-25.315-5.887s-5.887 2.354-6.475 11.185c-.59 8.83 4.71 14.13 4.71 14.13l27.08-9.42V369.45z" transform="matrix(.844 -.297 .284 .885 -89.238 -64.18)" fill="gray"/>
      <rect ry="3.532" rx="3.532" transform="matrix(.844 -.297 .284 .885 -19.207 -255.61)" height="19.428" width="7.064" y="534.62" x="175.71" fill="gray"/>
    </g>
    <path d="M244.77 87.42c7.322-5.948 11.157-3.72 17.65 3.1 9.322 9.815 41.317 58.855 46.312 63.019s4.24-6.418 10.344-1.388c3.032 3.727-10.437 17.026-13.737 14.57-6.412-5.022 3.914-5.376-.355-9.645-4.925-5.133-51.215-40.497-60.282-50.57-5.92-6.578-6.314-13.97.067-19.088z" fill-rule="evenodd" transform="matrix(.74 .166 .344 .547 178.197 70.438)" stroke="#000" stroke-width="1.25" fill="#923d09"/>
    <g stroke="#000">
      <path d="M193.63 88.25c15.546 4.423 43.275 46.12 53.766 45.79 16.652-.48 83.233-77.56 72.564-88.252-7.4-8.246-67.94 41.192-71.852 44.126-.67-8.6-15.95-51.042-28.31-59.945-14.62-9.31-32.21-4.158-47.82-6.662-7.3-2.843-18.84-14.056-23.78-24.976 8.182-3.544 15.323-2.48 23.31-10.65 5.68-6.643 2.382-46.914.37-51.06-13.29-14.268 5.43-7.327-39.023-9.082-64.268-2.446-75.878 49.582-31.653 70.792-3.462 15.928-14.22 19.594-22.94 21.646-8.776.106-38.825-.763-55.545 12.49-33.403 24.55-8.925 30.903-15.352 35.8C.192 73.91-10.503 88.093-14.523 96.573c-10.472 4.996-99.485-4.995-116.5-1.664-16.42.965-56.467 15.818-73.15 26.64 3.565 14.143 241.51-7.602 249.27-2.496 5.946 3.345-2.705 51.237 2.01 54.95 1.66 9.062 116.54 5.828 125.6 5.828 7.558-18.86 15.39-81.558 20.926-91.583z" fill-rule="evenodd" transform="matrix(-.133 0 0 .21 332.778 192.49)" stroke-width="4.702" fill="#ffc600"/>
      <path d="M174.9-96.568c1.543 25.457-7.118 3.304-38.37 3.304C75.97-98.3 97.246 5.932 97.246-26.24 71.018-39.95 52.41-126.53 126.86-123.17c23.54 0 44.96 7.858 48.045 26.603z" fill-rule="evenodd" transform="matrix(-.16 0 0 .148 336.515 193.377)" stroke-width="5.125"/>
      <path d="M387.98 96.574c0 1.68-.172 7.957-.833 11.656-.445 3.74-1.54 8.077-2.498 10.823-.343 4.452-2.092 5.174-2.498 9.99-1.544 2.27-2.21 5.945-2.498 9.992 0 4.27.686 7.558.833 11.656.48 3.56 1.612 6.367 1.665 10.824 0 4.365-.005 7.55-1.665 9.99v5.83" transform="matrix(-.21 0 0 .21 406.366 185.894)" stroke-width="3.75" fill="none"/>
      <path d="M383.81 76.592c.58 0 1.03 20.13 5.828 24.978 1.04 3.212 4.574 7.343 5.828 9.99 1.672 2.46 3.98 5.368 8.326 6.66h11.656c4.634 0 7.473.056 10.823-1.664 1.486-.8 2.532-1.846 3.33-3.33M442.09 115.72h.832c-2.327 0-1.092-.104 3.33 1.665 1.726 2.537 3.77 3.625 6.66 4.996 2.983 1.468 6.392 1.666 10.824 1.666 4.198-.354 6.74-1.686 9.16-3.33 4.042-1.31 4.814-2.936 5.827-7.494v-11.656c-.478-3.53-1.384-6.372-2.498-9.158-.42-1.258-.833-.705-.833-2.497M510.36 121.55c-.425 0-2.65 2.575-4.163 4.995-2.568.918-3.233 3.716-4.162 5.828-.67 2.675-1.666 2.86-1.666 6.66-1.678 3.71-2.205 7.008-2.498 10.824l2.497-10.823c-1.678 3.708-2.205 7.007-2.498 10.823M461.24 54.946h-.833c2.25 0 1.1-.053-3.33.833-1.817 2.27-4.807 4.304-7.493 5.826-2.977 1.825-5.367 4.01-8.326 4.996l8.326-4.996c-2.977 1.825-5.367 4.01-8.326 4.996M402.13 51.615h.833c-2.248 0-1.105-.054 3.33.833 2.936 1.39 4.752 2.89 7.493 4.995 1.29 1.935 3.257 2.305 4.163 5.828 1.573.848 2.123 1.67 2.497 4.164M427.11 29.968h4.163c4.53 0 6.733.68 10.824.833 3.538-.424 4.382-1.737 7.493-2.497M442.93 133.21c.14.66 1.652 4.256 2.498 5.828 1.517 2.512 3.538 4.914 4.163 8.326.97 2.905 1.527 6.23 1.666 9.99.76 3.503 1.264 6.62-.833 9.16-.898 3.29-2.57 5.69-4.162 9.157-1.11 0-.832-.277-.832.834M416.28 134.87v.833c0-2.248.055-1.105-.832 3.33-1.147 4.013-2.15 7.388-4.163 9.99-1.098 3.295-2.326 4.505-2.498 9.16.662 1.543.833 3.262.833 5.827M412.12 169.01v4.163c.908 1.21.55 1.294 1.665 1.665M343.02 128.21v.832c0-2.327-.258-.936 4.163.833 3.572.638 7.937 1.037 10.823 1.665 2.672 1.312 5.265 3.48 7.493 4.995 1.993 1.728 4.818 3.48 7.493 4.996 3.004 1.503 4.325 2.824 5.828 5.83.908 1.21.55 1.292 1.666 1.664" transform="matrix(-.21 0 0 .21 406.366 185.894)" stroke-width="3.75" fill="none"/>
      <path d="M423.78-16.655c.123-.184 2.467-1.914 4.996-2.498 1.805-1.354 4.812-.74 6.66 0" transform="matrix(-.326 0 0 .21 458.057 186.64)" stroke-width="3" fill="none"/>
      <path d="M445.42-19.153h.833c-2.248 0-1.106.054 3.33-.833 3.48-1.16 7.672-.832 11.656-.832 1.173.39.83.242.83 1.665M444.59-7.497v.833c0-2.327-.103-1.09 1.666 3.33.643 2.962 1.76 7.05.832 9.158-2.184 0-2.86-.274-4.163-.833" transform="matrix(-.254 0 0 .21 427.35 186.64)" stroke-width="3.402" fill="none"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.25 -.036 -.025 .12 403.716 205.73)" stroke-width="4.501"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.18 -.024 -.018 .078 382.9 193.785)" stroke-width="6.528"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.18 -.024 -.018 .078 377.412 193.254)" stroke-width="6.528"/>
      <path d="M412.95 113.64c0 2.07-1.864 3.747-4.163 3.747-2.3 0-4.163-1.678-4.163-3.747s1.864-3.747 4.163-3.747c2.3 0 4.163 1.678 4.163 3.747zM469.57 119.89c0 1.38-1.305 2.498-2.914 2.498-1.61 0-2.914-1.12-2.914-2.498s1.305-2.498 2.914-2.498c1.61 0 2.914 1.12 2.914 2.498z" fill-rule="evenodd" transform="matrix(-.21 0 0 .21 406.366 185.894)" stroke-width="3.75"/>
      <path d="M447.09 31.634v.833c0-2.796.197-1.03-4.163 3.33-3.724 1.785-5.164 3.667-6.66 6.66-1.742 3.85-2.342 6.613-2.498 10.824v6.662M419.61 30.8c.577 0 1.477 2.274 2.498 4.996 1.856 2.19 3.037 5.35 3.33 9.16 0 4.517.78 6.452.833 10.823v3.33M504.54 129.04c0 .48-2.6 1.455-4.163 3.33-3.16.428-5.964.833-9.99.833-.61-.916-1.836-1.217-3.33-1.665" transform="matrix(-.21 0 0 .21 406.366 185.894)" stroke-width="3.75" fill="none"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.75" fill="#fff">
      <path d="M276.41 36.63l88.253 1.665V49.95s8.325 14.987 13.32 48.29c4.996 33.303 9.992 158.19 9.992 158.19l21.646 141.54s-13.32 21.647-23.31 19.982c-9.99-1.666-39.964-13.32-39.964-14.987 0-1.665-1.665-79.926-4.995-133.21-3.33-53.285-9.99-81.592-24.976-104.9-19.982 48.288-21.647 98.243-19.982 98.243s1.665 39.963 3.33 69.936c1.666 29.972-1.665 71.6-1.665 71.6l-58.28-3.33s1.665-36.633 3.33-64.94c1.666-28.307 6.745-41.625 8.327-73.266 1.748-34.963 4.202-53.32 6.66-109.9 3.238-45.74 14.687-75.258 17.9-103.24.87-7.133-.416-12.488.416-13.32z" transform="matrix(-.216 0 0 .137 386.964 218.454)"/>
      <path d="M316.38 166.51s-8.326-13.32-11.656-36.633-3.33-28.307-3.33-28.307M298.06 233.12l-9.99 39.963" transform="matrix(-.216 0 0 .137 386.964 218.454)"/>
      <path d="M276.41 223.12l11.657 54.95" transform="matrix(-.216 0 0 .137 374.02 218.682)"/>
      <path d="M387.98 417.95l-11.66-81.6M276.41 44.955l88.253 1.665M318.04 164.84c0-1.665 4.995-29.972 4.995-29.972M284.74 44.955l-8.326 84.922" transform="matrix(-.216 0 0 .137 386.964 218.454)"/>
    </g>
    <path d="M549.21 538.58c0 136.98-111.05 248.03-248.03 248.03S53.15 675.56 53.15 538.58 164.2 290.55 301.18 290.55 549.21 401.6 549.21 538.58z" stroke="#006a00" stroke-width="6.25" fill="none" transform="matrix(.478 0 0 .48 228.554 -9.847)"/>
    <g stroke-width="1pt">
      <path d="M502.44 222.633c14.924 12.727-2.957 25.453-11.196 25.453-8.24 0-26.12-12.726-11.194-25.453-3.73 12.727 11.194 15.91 11.194 22.273 0-6.364 14.926-9.545 11.195-22.273z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.21 0 0 .18 431.543 145)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M362.044 115.794c14.283-13.446 24.938 5.755 24.028 13.954-.91 8.2-15.52 24.586-26.504 8.325 12.22 5.12 17.03-9.38 23.346-8.677-6.316-.703-7.824-15.908-20.87-13.602z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.023 -.21 .178 .02 277.145 177.76)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M399.996 373.926c-10.344 16.674-25.577.867-26.805-7.29-1.227-8.157 8.677-27.758 23.47-14.876-13.125-1.798-14.043 13.454-20.328 14.402 6.284-.948 11.65 13.355 23.664 7.764z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.03 .21 -.177 .027 466.1 292.163)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M241.94 255.338c-14.103-13.635 4.535-25.22 12.758-24.705 8.223.514 25.277 14.333 9.587 26.103 4.517-12.47-10.18-16.577-9.785-22.93-.396 6.352-15.49 8.596-12.56 21.532z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.21 -.013 .01 -.18 307.862 337.248)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M491.375 190.403c17.44 8.964 2.95 25.454-5.07 27.345-8.02 1.89-28.34-6.396-16.722-22.208-.72 13.244 14.536 12.917 15.993 19.112-1.456-6.194 12.344-12.716 5.8-24.25z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.205 -.048 .04 .175 404.6 131.1)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M326.202 124.41c9.26-17.302 25.47-2.502 27.216 5.56 1.746 8.062-6.888 28.257-22.472 16.347 13.213.955 13.156-14.324 19.367-15.673-6.21 1.35-12.478-12.583-24.11-6.235z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.045 -.206 .175 -.038 265.45 210.23)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M438.042 359.19c-4.076 19.2-23.764 9.51-27.685 2.254-3.92-7.255-1.252-29.058 17.03-21.97-12.954 2.77-8.646 17.428-14.236 20.457 5.59-3.028 15.488 8.602 24.892-.74z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.1 .185 -.158 .085 472.495 259.813)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M249.096 288.697c-17.708-8.424-3.727-25.352 4.23-27.487 7.96-2.136 28.52 5.522 17.394 21.684.314-13.26-14.925-12.465-16.57-18.612 1.645 6.146-11.95 13.09-5.054 24.415z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.203 .055 -.046 -.173 337.646 345.308)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M473.61 165.692c19.413 2.742 11.122 23.08 4.165 27.497-6.958 4.418-28.866 3.26-23.07-15.49 3.658 12.747 17.964 7.43 21.368 12.804-3.404-5.374 7.498-16.066-2.462-24.812z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.178 -.113 .096 .152 372.213 138.156)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M295.512 141.997c4.87-19.014 24.138-8.515 27.755-1.103 3.617 7.412.046 29.086-17.927 21.244 13.06-2.23 9.362-17.054 15.073-19.848-5.71 2.794-15.118-9.237-24.9-.293z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.092 -.19 .16 -.08 256.97 239.857)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M465.258 338.912c1.57 19.566-20.063 15.91-25.89 10.076-5.826-5.832-9.485-27.49 10.058-25.925-11.626 6.36-3.318 19.175-7.81 23.676 4.492-4.5 17.294 3.814 23.642-7.828z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.15 .15 -.127 .127 469.94 233.823)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M262.314 316.518c-19.203-3.95-9.66-23.73-2.442-27.705 7.22-3.975 29.013-1.452 22.06 16.9-2.857-12.95-17.466-8.537-20.53-14.113 3.064 5.576-8.483 15.566.912 24.918z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.185 .102 -.086 -.157 361.8 350.332)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M447.846 142.81c19.452-2.45 16.777 19.35 11.22 25.442-5.556 6.09-27 10.727-26.32-8.89 6.87 11.34 19.282 2.453 23.975 6.745-4.692-4.29 3.03-17.47-8.874-23.297z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.142 -.156 .132 .12 342.783 142.863)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M270.39 164.98c-.282-19.628 21.063-14.556 26.495-8.353 5.43 6.203 7.662 28.055-11.736 25.208 12.017-5.58 4.567-18.916 9.346-23.112-4.78 4.196-17.008-4.944-24.106 6.256z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.14 -.16 .135 -.118 258.827 269.535)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M483.694 311.268c6.134 18.644-15.75 20.2-22.785 15.907-7.038-4.29-15.695-24.475 3.665-27.568-9.8 8.925 1.293 19.417-2.014 24.852 3.308-5.435 17.708-.378 21.134-13.192z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.18 .11 -.093 .153 463.49 208.038)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M283.945 341.222c-19.566 1.23-15.538-20.362-9.613-26.094 5.926-5.732 27.617-9.02 25.714 10.518-6.148-11.75-19.09-3.653-23.507-8.23 4.415 4.576-4.11 17.248 7.405 23.806z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.15 .147 -.125 -.13 388.806 347.73)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M421.07 125.363c18.603-6.194 20.217 15.712 15.95 22.77-4.266 7.056-24.4 15.783-27.544-3.593 8.942 9.787 19.39-1.35 24.827 1.945-5.437-3.295-.426-17.727-13.234-21.122z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.11 -.18 .153 .093 318.026 145.89)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M252.642 196.757c-6.852-18.39 14.954-20.796 22.15-16.78 7.2 4.014 16.634 23.845-2.592 27.69 9.446-9.3-2.046-19.353 1.047-24.913-3.093 5.56-17.678 1.066-20.605 14.003z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.184 -.103 .087 -.157 276.84 299.122)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M496.635 287.41c8.218 17.822-13.345 21.866-20.824 18.406-7.48-3.46-18.38-22.523.502-27.808-8.72 9.987 3.497 19.143.83 24.92 2.667-5.776 17.55-2.396 19.493-15.518z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.19 .088 -.075 .163 464.8 187.162)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M320.435 365.818c-18.015 7.742-21.468-13.95-17.81-21.34 3.657-7.394 22.984-17.788 27.748 1.253-9.734-8.996-19.207 2.983-24.902.158 5.696 2.825 1.918 17.63 14.965 19.93z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.093 .19 -.16 -.08 421.383 336.668)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M504.167 257.892c11.793 15.68-8.43 24.195-16.47 22.395-8.04-1.8-22.72-18.126-5.38-27.286-6.414 11.607 7.46 17.972 6.073 24.183 1.386-6.21 16.645-6.055 15.777-19.29z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.206 .046 -.04 .175 451.895 166.64)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M392.025 118.176c16.868-10.002 23.084 11.064 20.41 18.866-2.676 7.802-20.505 20.603-27.68 2.335 10.81 7.667 18.663-5.434 24.674-3.368-6.012-2.067-4.17-17.235-17.405-17.834z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.068 -.2 .17 .058 295.67 160.104)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M357.465 376.692c-16.067 11.247-23.852-9.29-21.772-17.272 2.08-7.982 18.894-22.093 27.424-4.418-11.357-6.83-18.2 6.828-24.35 5.22 6.15 1.608 5.456 16.87 18.698 16.47z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.053 .204 -.174 -.045 450.388 327.608)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M243.966 224.172c-10.793-16.387 9.92-23.62 17.833-21.323 7.912 2.297 21.544 19.51 3.668 27.567 7.125-11.182-6.324-18.4-4.554-24.513-1.77 6.11-16.99 5.003-16.948 18.268z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.202 -.06 .05 -.172 290.45 318.508)"/>
    </g>
    <g stroke="#000">
      <path d="M193.63 88.25c15.546 4.423 43.275 46.12 53.766 45.79 16.652-.48 83.233-77.56 72.564-88.252-7.4-8.246-67.94 41.192-71.852 44.126-.67-8.6-15.95-51.042-28.31-59.945-14.62-9.31-32.21-4.158-47.82-6.662-7.3-2.843-18.84-14.056-23.78-24.976 8.182-3.544 15.323-2.48 23.31-10.65 5.68-6.643 2.382-46.914.37-51.06-13.29-14.268 5.43-7.327-39.023-9.082-64.268-2.446-75.878 49.582-31.653 70.792-3.462 15.928-14.22 19.594-22.94 21.646-8.776.106-38.825-.763-55.545 12.49-33.403 24.55-8.925 30.903-15.352 35.8C.192 73.91-10.503 88.093-14.523 96.573c-10.472 4.996-99.485-4.995-116.5-1.664-16.42.965-56.467 15.818-73.15 26.64 3.565 14.143 241.51-7.602 249.27-2.496 5.946 3.345-2.705 51.237 2.01 54.95 1.66 9.062 116.54 5.828 125.6 5.828 7.558-18.86 15.39-81.558 20.926-91.583z" fill-rule="evenodd" transform="matrix(.133 0 0 .21 417.097 193.746)" stroke-width="4.702" fill="#520900"/>
      <path d="M174.9-96.568c1.543 25.457-7.118 3.304-38.37 3.304C75.97-98.3 97.246 5.932 97.246-26.24 71.018-39.95 52.41-126.53 126.86-123.17c23.54 0 44.96 7.858 48.045 26.603z" fill-rule="evenodd" transform="matrix(.16 0 0 .148 413.364 194.634)" stroke-width="5.125"/>
      <path d="M387.98 96.574c0 1.68-.172 7.957-.833 11.656-.445 3.74-1.54 8.077-2.498 10.823-.343 4.452-2.092 5.174-2.498 9.99-1.544 2.27-2.21 5.945-2.498 9.992 0 4.27.686 7.558.833 11.656.48 3.56 1.612 6.367 1.665 10.824 0 4.365-.005 7.55-1.665 9.99v5.83" transform="matrix(.21 0 0 .21 343.51 187.15)" stroke-width="3.75" fill="none"/>
      <path d="M383.81 76.592c.58 0 1.03 20.13 5.828 24.978 1.04 3.212 4.574 7.343 5.828 9.99 1.672 2.46 3.98 5.368 8.326 6.66h11.656c4.634 0 7.473.056 10.823-1.664 1.486-.8 2.532-1.846 3.33-3.33M442.09 115.72h.832c-2.327 0-1.092-.104 3.33 1.665 1.726 2.537 3.77 3.625 6.66 4.996 2.983 1.468 6.392 1.666 10.824 1.666 4.198-.354 6.74-1.686 9.16-3.33 4.042-1.31 4.814-2.936 5.827-7.494v-11.656c-.478-3.53-1.384-6.372-2.498-9.158-.42-1.258-.833-.705-.833-2.497M510.36 121.55c-.425 0-2.65 2.575-4.163 4.995-2.568.918-3.233 3.716-4.162 5.828-.67 2.675-1.666 2.86-1.666 6.66-1.678 3.71-2.205 7.008-2.498 10.824l2.497-10.823c-1.678 3.708-2.205 7.007-2.498 10.823M461.24 54.946h-.833c2.25 0 1.1-.053-3.33.833-1.817 2.27-4.807 4.304-7.493 5.826-2.977 1.825-5.367 4.01-8.326 4.996l8.326-4.996c-2.977 1.825-5.367 4.01-8.326 4.996M402.13 51.615h.833c-2.248 0-1.105-.054 3.33.833 2.936 1.39 4.752 2.89 7.493 4.995 1.29 1.935 3.257 2.305 4.163 5.828 1.573.848 2.123 1.67 2.497 4.164M427.11 29.968h4.163c4.53 0 6.733.68 10.824.833 3.538-.424 4.382-1.737 7.493-2.497M442.93 133.21c.14.66 1.652 4.256 2.498 5.828 1.517 2.512 3.538 4.914 4.163 8.326.97 2.905 1.527 6.23 1.666 9.99.76 3.503 1.264 6.62-.833 9.16-.898 3.29-2.57 5.69-4.162 9.157-1.11 0-.832-.277-.832.834M416.28 134.87v.833c0-2.248.055-1.105-.832 3.33-1.147 4.013-2.15 7.388-4.163 9.99-1.098 3.295-2.326 4.505-2.498 9.16.662 1.543.833 3.262.833 5.827M412.12 169.01v4.163c.908 1.21.55 1.294 1.665 1.665M343.02 128.21v.832c0-2.327-.258-.936 4.163.833 3.572.638 7.937 1.037 10.823 1.665 2.672 1.312 5.265 3.48 7.493 4.995 1.993 1.728 4.818 3.48 7.493 4.996 3.004 1.503 4.325 2.824 5.828 5.83.908 1.21.55 1.292 1.666 1.664" transform="matrix(.21 0 0 .21 343.51 187.15)" stroke-width="3.75" fill="none"/>
      <path d="M423.78-16.655c.123-.184 2.467-1.914 4.996-2.498 1.805-1.354 4.812-.74 6.66 0" transform="matrix(.326 0 0 .21 291.82 187.893)" stroke-width="3" fill="none"/>
      <path d="M445.42-19.153h.833c-2.248 0-1.106.054 3.33-.833 3.48-1.16 7.672-.832 11.656-.832 1.173.39.83.242.83 1.665M444.59-7.497v.833c0-2.327-.103-1.09 1.666 3.33.643 2.962 1.76 7.05.832 9.158-2.184 0-2.86-.274-4.163-.833" transform="matrix(.254 0 0 .21 322.53 187.893)" stroke-width="3.402" fill="none"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.25 -.036 .025 .12 346.164 206.986)" stroke-width="4.501"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.18 -.024 .018 .078 366.977 195.038)" stroke-width="6.528"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.18 -.024 .018 .078 372.47 194.51)" stroke-width="6.528"/>
      <path d="M412.95 113.64c0 2.07-1.864 3.747-4.163 3.747-2.3 0-4.163-1.678-4.163-3.747s1.864-3.747 4.163-3.747c2.3 0 4.163 1.678 4.163 3.747zM469.57 119.89c0 1.38-1.305 2.498-2.914 2.498-1.61 0-2.914-1.12-2.914-2.498s1.305-2.498 2.914-2.498c1.61 0 2.914 1.12 2.914 2.498z" fill-rule="evenodd" transform="matrix(.21 0 0 .21 343.51 187.15)" stroke-width="3.75"/>
      <path d="M447.09 31.634v.833c0-2.796.197-1.03-4.163 3.33-3.724 1.785-5.164 3.667-6.66 6.66-1.742 3.85-2.342 6.613-2.498 10.824v6.662M419.61 30.8c.577 0 1.477 2.274 2.498 4.996 1.856 2.19 3.037 5.35 3.33 9.16 0 4.517.78 6.452.833 10.823v3.33M504.54 129.04c0 .48-2.6 1.455-4.163 3.33-3.16.428-5.964.833-9.99.833-.61-.916-1.836-1.217-3.33-1.665" transform="matrix(.21 0 0 .21 343.51 187.15)" stroke-width="3.75" fill="none"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.75" fill="#fff">
      <path d="M276.41 36.63l88.253 1.665V49.95s8.325 14.987 13.32 48.29c4.996 33.303 9.992 158.19 9.992 158.19l21.646 141.54s-13.32 21.647-23.31 19.982c-9.99-1.666-39.964-13.32-39.964-14.987 0-1.665-1.665-79.926-4.995-133.21-3.33-53.285-9.99-81.592-24.976-104.9-19.982 48.288-21.647 98.243-19.982 98.243s1.665 39.963 3.33 69.936c1.666 29.972-1.665 71.6-1.665 71.6l-58.28-3.33s1.665-36.633 3.33-64.94c1.666-28.307 6.745-41.625 8.327-73.266 1.748-34.963 4.202-53.32 6.66-109.9 3.238-45.74 14.687-75.258 17.9-103.24.87-7.133-.416-12.488.416-13.32z" transform="matrix(.216 0 0 .137 362.915 219.704)"/>
      <path d="M316.38 166.51s-8.326-13.32-11.656-36.633-3.33-28.307-3.33-28.307M298.06 233.12l-9.99 39.963" transform="matrix(.216 0 0 .137 362.915 219.704)"/>
      <path d="M276.41 223.12l11.657 54.95" transform="matrix(.216 0 0 .137 375.86 219.932)"/>
      <path d="M387.98 417.95l-11.66-81.6M276.41 44.955l88.253 1.665M318.04 164.84c0-1.665 4.995-29.972 4.995-29.972M284.74 44.955l-8.326 84.922" transform="matrix(.216 0 0 .137 362.915 219.704)"/>
    </g>
    <path d="M349.68 188.16c0 13.794-7.082 24.977-15.82 24.977-8.736 0-15.818-11.183-15.818-24.977s7.082-24.977 15.82-24.977c8.735 0 15.818 11.183 15.818 24.977z" fill-rule="evenodd" transform="matrix(.348 0 0 .34 189.228 212.87)" stroke="#000" stroke-width="2.5" fill="#ffc600"/>
    <g stroke-width="1pt">
      <g fill-rule="evenodd" stroke="#000">
        <path d="M212.6 892.91s53.15-17.716 70.867-17.716c17.716 0 17.716 17.716 19.135 18.212l-1.42 34.937H212.6V892.91z" transform="matrix(.21 0 0 .25 289.382 93.4)" fill="#00cfe6"/>
        <path d="M265.75 892.91s0-17.716 17.717-17.716c17.716 0 35.433-.496 53.15 0v53.15H265.75V892.91z" transform="matrix(.21 0 0 -.25 296.837 560.12)" fill="#00cfe6"/>
        <path d="M212.6 892.91s53.15-17.716 70.867-17.716c17.716 0 17.716 17.716 19.135 18.212l-1.42 34.937H212.6V892.91z" transform="matrix(-.21 0 0 .25 468.3 93.454)" fill="#00cfe6"/>
        <path d="M265.75 892.91s0-17.716 17.717-17.716c17.716 0 35.433-.496 53.15 0v53.15H265.75V892.91z" transform="matrix(-.21 0 0 -.25 460.845 560.17)" fill="#00cfe6"/>
        <path d="M249.45 875.2s0-17.717-17.716-17.717h53.15L249.45 875.2z" transform="matrix(-.21 0 0 -.25 449.96 555.763)" fill="#fff"/>
        <path d="M159.45 892.91l106.3 53.15s-36.04 33.3-70.866 35.433c-17.717 0-35.433-35.433-35.433-35.433v-53.15z" transform="matrix(-.245 0 0 .25 473.89 93.453)" fill="#00cfe6"/>
        <path d="M249.45 892.91c.71-8.858-2.214-35.434-19.93-35.434h55.363c17.717 0 35.434 17.717 35.434 35.434l-1.42 35.432H249.45V892.91z" transform="matrix(-.21 0 0 .25 457.415 97.865)" fill="#fff"/>
        <path d="M249.45 875.2s0-17.717-17.716-17.717h53.15L249.45 875.2z" transform="matrix(.21 0 0 -.25 307.72 555.713)" fill="#fff"/>
        <path d="M159.45 892.91l106.3 53.15s-36.04 33.3-70.866 35.433c-17.717 0-35.433-35.433-35.433-35.433v-53.15z" transform="matrix(.245 0 0 .25 283.79 93.398)" fill="#00cfe6"/>
        <path d="M249.45 892.91c.71-8.858-2.214-35.434-19.93-35.434h55.363c17.717 0 35.434 17.717 35.434 35.434l-1.42 35.432H249.45V892.91z" transform="matrix(.21 0 0 .25 300.266 97.81)" fill="#fff"/>
        <path d="M88.802 891.9c-.11 1.012 17.607-34.42 53.04-34.42s177.16 70.865 247.92 70.865c70.975 0 196.65-72.56 230.42-70.866 33.773 1.693 53.52 37.16 53.15 35.432-.374-1.728-17.717 53.15-17.717 53.15 0-17.716-17.717-35.433-35.433-35.433-17.717 0-106.3 70.865-230.42 70.865-123.91 0-212.49-70.866-230.21-70.866-17.717 0-35.433 17.715-35.433 35.432l-35.32-54.16z" transform="matrix(.21 0 0 .25 296.814 93.454)" fill="#fff"/>
      </g>
      <path d="M415.924 324.256l-3.26-9.946 3.337-1.53.386 1.173-2.726 1.25.998 3.047 2.553-1.17.382 1.164-2.552 1.172 1.11 3.385 2.832-1.3.385 1.176-3.443 1.58zM401.934 330.77l-3.26-9.947.612-.28 2.873 8.77 2.272-1.042.385 1.176-2.883 1.323zM397.7 332.757l-3.26-9.946 3.114-1.427.384 1.174-2.5 1.148 1.007 3.08 2.165-.995.385 1.175-2.165.994 1.48 4.517-.61.28zM360.216 332.887l2.15-10.258 1.255.367-.027 7.7c-.003.716-.01 1.254-.016 1.607.15-.352.377-.868.68-1.544l3.005-6.695 1.124.33-2.15 10.258-.804-.236 1.8-8.586-3.633 8.05-.755-.222.005-9.268-1.83 8.733-.804-.236zM359.85 321.64l.793.39-1.978 5.64c-.343.98-.685 1.724-1.024 2.236-.337.51-.75.858-1.235 1.047-.48.185-1.01.134-1.59-.15-.56-.277-.967-.657-1.214-1.15-.246-.49-.34-1.07-.286-1.74.057-.675.267-1.53.63-2.563l1.978-5.64.794.39-1.975 5.633c-.296.848-.468 1.498-.514 1.948-.042.448.017.838.175 1.172.163.333.414.585.755.752.584.287 1.075.277 1.475-.03.4-.31.813-1.076 1.243-2.302l1.976-5.633zM343.864 314.838l.595.325-2.187 5.594c-.38.972-.733 1.716-1.06 2.235-.325.516-.692.88-1.1 1.095-.406.212-.824.197-1.257-.04-.42-.23-.705-.572-.85-1.033-.145-.458-.16-1.014-.043-1.667.118-.657.377-1.498.778-2.524l2.186-5.593.595.325-2.183 5.587c-.328.84-.535 1.482-.62 1.92-.08.437-.076.81.015 1.123.094.312.268.54.523.68.437.238.832.196 1.185-.13.353-.325.766-1.095 1.24-2.31l2.184-5.587zM333.256 316.575l.602.2c-.163.502-.258.933-.284 1.3-.023.36.03.694.16 1 .13.3.323.52.578.66.227.124.455.162.685.112.23-.05.437-.178.618-.383.186-.21.338-.463.454-.762.118-.305.18-.592.187-.86.007-.273-.052-.54-.18-.796-.08-.167-.287-.47-.617-.905-.328-.442-.538-.8-.63-1.082-.12-.36-.162-.747-.13-1.162.037-.417.143-.85.316-1.293.192-.49.434-.91.728-1.262.296-.355.61-.576.943-.66.333-.086.65-.045.953.12.333.182.58.46.743.83.166.37.23.816.19 1.34-.038.524-.164 1.073-.375 1.646l-.606-.22c.208-.63.285-1.147.23-1.557-.054-.41-.254-.708-.6-.897-.36-.197-.676-.198-.954-.003-.273.193-.488.488-.645.89-.135.347-.19.666-.16.953.026.29.234.682.622 1.186.393.496.646.882.756 1.156.16.403.225.84.194 1.307-.03.466-.147.957-.35 1.477-.202.515-.46.964-.776 1.347-.314.378-.65.625-1.007.74-.352.11-.69.077-1.01-.1-.406-.22-.697-.534-.873-.94-.174-.406-.24-.906-.198-1.503.045-.6.19-1.225.436-1.88zM419.134 316.693c-.54-1.65-.758-3.036-.65-4.155.104-1.126.48-1.837 1.13-2.136.427-.195.883-.152 1.37.13s.945.775 1.382 1.48c.436.7.817 1.546 1.144 2.542.33 1.008.53 1.955.603 2.838.07.88.002 1.61-.21 2.18-.212.564-.52.938-.92 1.12-.435.2-.896.153-1.387-.142-.49-.294-.95-.793-1.383-1.494s-.793-1.49-1.08-2.363zm.637-.268c.393 1.198.852 2.074 1.376 2.63.526.552 1.014.723 1.466.516.46-.21.723-.73.79-1.566.07-.83-.105-1.888-.524-3.168-.266-.81-.56-1.486-.886-2.03-.324-.548-.663-.93-1.017-1.147-.352-.224-.68-.266-.986-.127-.433.2-.7.69-.802 1.476-.102.78.093 1.92.583 3.415zM410.564 326.6l-3.258-9.946 2.045-.94c.412-.187.755-.24 1.028-.158.272.08.545.314.822.708.276.394.503.863.683 1.41.23.707.32 1.35.267 1.933-.054.58-.28 1.037-.68 1.372.216.092.393.2.53.324.294.27.608.635.94 1.092l1.69 2.34-.768.35-1.288-1.788c-.375-.517-.67-.905-.89-1.17-.22-.263-.396-.438-.53-.52-.13-.085-.252-.133-.363-.14-.08 0-.198.036-.355.11l-.708.323 1.448 4.42-.61.28zm-1.21-5.836l1.313-.602c.28-.128.477-.29.594-.484.117-.2.172-.452.165-.762-.01-.313-.067-.632-.172-.952-.153-.47-.36-.82-.62-1.05-.255-.23-.555-.268-.897-.11l-1.46.668 1.078 3.292zM403.59 324.137c-.54-1.65-.76-3.036-.653-4.155.105-1.125.483-1.837 1.133-2.135.426-.195.882-.153 1.367.13.486.28.947.774 1.384 1.48.437.7.818 1.546 1.144 2.542.33 1.007.532 1.953.604 2.836.07.88.002 1.61-.21 2.18-.213.565-.52.938-.92 1.122-.435.2-.897.152-1.387-.144-.49-.293-.95-.792-1.382-1.494-.433-.702-.792-1.49-1.078-2.363zm.635-.268c.393 1.197.852 2.074 1.376 2.63.527.55 1.015.722 1.466.516.46-.21.724-.733.79-1.568.07-.83-.104-1.888-.523-3.167-.266-.81-.56-1.486-.886-2.03-.324-.547-.663-.93-1.017-1.147-.353-.223-.682-.265-.986-.125-.432.198-.7.69-.8 1.476-.103.78.09 1.92.58 3.415zM368.867 335.137l1.226-10.47 2.415.397c.492.08.873.253 1.144.516.274.26.468.622.583 1.092.12.467.15.94.094 1.42-.054.448-.178.856-.373 1.227-.196.37-.46.65-.79.846.37.25.63.614.786 1.095.158.483.202 1.03.13 1.64-.057.488-.175.934-.353 1.338-.175.398-.37.697-.584.894-.214.2-.47.333-.767.404-.296.07-.65.07-1.06.004l-2.453-.403zm1.562-5.93l1.392.228c.378.062.653.064.826.013.23-.07.412-.22.548-.443.14-.224.232-.52.275-.887.042-.347.026-.66-.046-.938-.07-.284-.196-.488-.376-.615-.178-.13-.5-.234-.962-.31l-1.286-.21-.37 3.16zm-.567 4.833l1.603.263c.275.045.47.06.586.046.203-.027.378-.096.525-.206.148-.113.278-.288.392-.526.115-.243.192-.53.23-.864.047-.39.025-.738-.065-1.044-.09-.31-.236-.542-.442-.69-.203-.156-.51-.266-.92-.333l-1.488-.244-.422 3.597zM376.438 336.415v-10.57h2.882c.58 0 1.02.097 1.32.29.302.187.544.52.724 1 .18.483.27 1.012.27 1.594 0 .75-.15 1.38-.448 1.897-.298.513-.76.838-1.383.98.228.177.4.352.52.525.25.375.488.843.712 1.406l1.13 2.877h-1.08l-.86-2.2c-.252-.635-.46-1.12-.62-1.457-.164-.335-.31-.572-.44-.705-.127-.134-.257-.23-.39-.282-.098-.033-.258-.05-.48-.05h-.997v4.695h-.86zm.86-5.904h1.85c.392 0 .7-.066.92-.194.223-.136.39-.346.507-.634.115-.294.172-.612.172-.953 0-.5-.112-.91-.336-1.233-.22-.322-.573-.484-1.054-.484h-2.058v3.498zM383.967 336.134l.9-10.922.913-.19 4.177 9.856-.965.203-1.22-2.996-2.674.56-.23 3.3-.9.19zm1.207-4.66l2.17-.455-1.1-2.742c-.334-.835-.593-1.522-.777-2.065.02.685.003 1.373-.047 2.06l-.246 3.2zM343.395 325.786l3.783-9.682 1.685.922c.343.188.58.437.708.75.133.306.178.696.135 1.17-.04.47-.147.926-.32 1.37-.163.415-.367.775-.614 1.084s-.518.517-.817.628c.204.32.3.726.282 1.22-.014.493-.133 1.024-.352 1.587-.177.452-.387.85-.63 1.194-.24.337-.466.578-.68.717-.213.14-.44.21-.68.21-.238 0-.5-.077-.786-.234l-1.712-.937zm2.788-5.29l.97.532c.264.144.468.21.61.2.19-.015.367-.116.53-.298.165-.18.314-.443.447-.782.125-.32.2-.623.223-.903.025-.286-.012-.51-.11-.67-.097-.164-.307-.335-.63-.512l-.897-.49-1.143 2.924zm-1.748 4.47l1.12.613c.19.104.332.162.42.176.16.02.308-.005.447-.077.14-.075.286-.212.436-.413.152-.205.29-.462.41-.77.14-.36.22-.696.237-1.008.02-.317-.027-.57-.14-.757-.107-.194-.304-.368-.59-.524l-1.04-.57-1.3 3.327z"/>
    </g>
    <g>
      <path d="M212.6 432.28v106.3c0 70.866 88.583 106.3 88.583 106.3s88.583-35.433 88.583-106.3v-106.3h-177.17z" fill-rule="evenodd" transform="matrix(.42 0 0 .396 249.17 46.69)" stroke="#000" stroke-width="1pt" fill="#00daec"/>
      <path d="M212.6 432.28v106.3c0 8.858 1.384 17.163 3.806 24.914l84.777-42.63V432.28H212.6z" fill-rule="evenodd" transform="matrix(.42 0 0 .396 249.17 46.69)" stroke="#000" stroke-width="1pt" fill="#fff"/>
      <path d="M212.6 432.28v106.3c0 8.858 1.384 17.163 3.806 24.914l84.777-42.63V432.28H212.6z" fill-rule="evenodd" transform="matrix(-.42 0 0 .396 502.66 46.69)" stroke="#000" stroke-width="1pt" fill="#fff300"/>
      <g fill-rule="evenodd" transform="matrix(.42 0 0 .396 249.17 46.69)" stroke="#000" stroke-width="1.25">
        <path d="M244.77 87.42c7.322-5.948 11.157-3.72 17.65 3.1 9.322 9.815 41.317 58.855 46.312 63.019s4.24-6.418 10.344-1.388c3.032 3.727-10.437 17.026-13.737 14.57-6.412-5.022 3.914-5.376-.355-9.645-4.925-5.133-51.215-40.497-60.282-50.57-5.92-6.578-6.314-13.97.067-19.088z" transform="matrix(.64 .237 .142 .702 58.9 338.88)" fill="#923d09"/>
        <rect ry="1.293" rx="2.606" transform="matrix(.756 .655 -.586 .81 0 0)" height="66.045" width="5.212" y="181.35" x="495.52" fill="#af8000"/>
        <path transform="matrix(.83 .556 -.64 .767 0 0)" fill="#b0b0d4" d="M497.3 228.63h29.375v18.868H497.3z"/>
      </g>
      <g stroke="#000" fill-rule="evenodd" transform="matrix(.42 0 0 .396 249.17 46.69)">
        <path d="M385.112 528.567c4.885-2.105-27.333-35.603-33.785-51.842-6.452-16.24-6.108-16.717-8.617-24.322l-3.15 1.36c.805 4.93 15.87 43.87 30.735 64.15 4.198 5.82.63 16.77 5.36 14.73l9.457-4.076z" stroke-width="1pt" fill="#af8000"/>
        <path d="M341.548 453.55l-25.872 2.485s-4.83 4.107-2.573 12.665c2.255 8.558 8.965 11.892 8.965 11.892l22.668-17.557-3.188-9.486z" stroke-width="1pt" fill="gray"/>
        <rect ry="3.532" rx="3.532" transform="rotate(-18.58)" height="19.428" width="7.064" y="534.62" x="175.71" stroke-width="1pt" fill="gray"/>
        <path d="M239.61 381.52c0 7.316-1.58 13.246-3.532 13.246-1.95 0-3.533-5.93-3.533-13.246 0-7.316 1.582-13.246 3.533-13.246s3.532 5.93 3.532 13.246z" transform="matrix(.745 -.716 .635 .393 -105.9 523.83)" stroke-width="1pt" fill="#cf0000"/>
        <path d="M239.61 381.52c0 7.316-1.58 13.246-3.532 13.246-1.95 0-3.533-5.93-3.533-13.246 0-7.316 1.582-13.246 3.533-13.246s3.532 5.93 3.532 13.246z" transform="matrix(-.978 .31 .027 .755 595.47 104.78)" stroke-width="1pt" fill="#cf0000"/>
        <path transform="rotate(-30.8) skewX(-.84)" stroke-width="1.153" fill="#b0b0d4" d="M15.073 596.25H93.94v17.796H15.074z"/>
      </g>
      <path d="M231.36 591.4l8.83-7.065s1.767 8.83 7.065 8.83 8.83-7.652 8.83-7.652 0 7.653 5.888 7.653 10.597-7.653 10.597-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.887 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.12 7.653 8.242 7.064s10.01-6.476 10.01-6.476 1.176 7.065 5.886 7.065 6.476-6.476 6.476-6.476 2.353 5.297 6.474 5.297 10.597-5.887 10.597-5.887" transform="matrix(.402 0 0 .396 259.724 53.456)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
      <path d="M231.95 591.4c3.14-1.374 5.69.196 9.418-7.654 0 4.12 2.945 9.42 8.243 9.42s8.832-7.653 8.832-7.653 0 7.653 5.887 7.653 10.596-7.653 10.596-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.888 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.122 7.653 8.243 7.064 2.943-.59 10.01-7.065 10.01-7.065" transform="matrix(.42 0 0 .396 260.566 57.656)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
      <g fill-rule="evenodd">
        <path d="M305.54 350.62v18.84h14.717l-5.298-18.84h-9.42z" transform="matrix(.332 0 0 .296 285.894 167.7)" stroke="#000" stroke-width="1pt" fill="#fff"/>
        <path d="M394.11 274.293c-.197.174-7.83 3.828-17.81 3.828s-13.892-1.043-14.87-1.74c-.98-.695-2.153-2.087-3.327-2.087-1.175 0-2.153.87-2.153.87s1.957.87 3.327 1.914c1.37 1.044 1.76 2.958 1.76 2.958l32.29.348s-.197-1.914-.197-2.958 1.175-2.61.98-3.132z"/>
        <path d="M365.932 261.938h.782v16.183h-.782zM376.498 259.848h.783v18.445h-.782zM386.87 262.98h.782v15.14h-.783z"/>
        <path stroke="#000" stroke-width="1.024" fill="#fff" d="M299.07 342.37h18.84v8.83h-18.84z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.101" fill="#fff" d="M297.89 350.61h21.782v8.83H297.89z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.159" fill="#fff" d="M296.71 358.86h24.137v8.83H296.71z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.372" fill="#fff" d="M266.17 334.72h23.837v12.527H266.17z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.475" fill="#fff" d="M264.68 346.41h27.56v12.527h-27.56z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.553" fill="#fff" d="M263.19 358.1h30.54v12.527h-30.54z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.312" fill="#fff" d="M260.21 368.28h36.5v15.307h-36.5z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.017" fill="#fff" d="M238.25 342.37h15.763v10.414H238.25z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.094" fill="#fff" d="M237.26 352.09h18.226v10.414H237.26z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width="1.151" fill="#fff" d="M236.28 361.81h20.197v10.414H236.28z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path stroke="#000" stroke-width=".973" fill="#fff" d="M234.31 370.27h24.137v12.725H234.31z" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path d="M220 377.51h9.876l10.597-33.374L220 377.51z" stroke="#000" stroke-width="1.017" fill="#fff" transform="matrix(.332 0 0 .296 284.524 162.83)"/>
        <path d="M366.11 262.116l.722.317-9.496 13.072-.723-.318z"/>
      </g>
      <path d="M231.36 591.4l8.83-7.065s1.767 8.83 7.065 8.83 8.83-7.652 8.83-7.652 0 7.653 5.888 7.653 10.597-7.653 10.597-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.887 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.12 7.653 8.242 7.064s10.01-6.476 10.01-6.476 1.176 7.065 5.886 7.065 6.476-6.476 6.476-6.476 2.353 5.297 6.474 5.297 7.065-7.064 7.065-7.064 1.176 6.476 4.12 6.476c2.943 0 6.476-6.477 6.476-6.477s1.177 8.242 4.12 7.653c2.944-.59 4.122-8.242 4.71-7.653.59.59 4.71 7.064 4.12 7.064" transform="matrix(.398 0 0 .396 256.288 48.556)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
    </g>
  </g>
</svg>
