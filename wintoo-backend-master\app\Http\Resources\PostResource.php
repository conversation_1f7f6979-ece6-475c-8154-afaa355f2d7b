<?php

namespace App\Http\Resources;

use App\Models\Customer;
use App\Models\Like;
use App\Models\Post;
use Bepsvpt\Blurhash\Facades\BlurHash;
use Illuminate\Filesystem\Cache;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class PostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


        $post_media = $this->post_media ?? [];


        $like = Like::where("likeable_id",$this->id)
            ->where("likeable_type","App\Models\Post")
            ->where("customer_id",auth('customers')->user()->id)
            ->first();

         $post =  Post::find($this->id);
        $post->views_count++ ;
        $post->save();
        $this->views_count = $post->views_count;

      $type =   $this->checkIfImageOrVideo()  ;

        if($this->media_url  && $type =="image" )
            $post_media[] =[
                "id" => 0 ,
                "post_id"=>$this->id,
                "image"=>$this->media_url,
                "media_url"=>$this->media_url,
                "created_at"=>"2023-02-14T11:38:52.000000Z",
                "updated_at"=>"2023-02-14T11:38:52.000000Z"
            ]       ;

        return [
            'id'=>$this->id,
            "description" => $this->description,
            "status"=>$this->status,
            "media_url"=>$this->media_url,
           //    "media_url_hash_blur"=>$this->media_url!=null?BlurHash::encode($this->media_url):"",
            "post_media"=>$post_media,
            "video_thumbnail_url"=>$this->media_thumbnail_url,
         //  "video_thumbnail_hash_blur"=>$this->media_thumbnail_url!=null?BlurHash::encode($this->media_thumbnail_url):"",
            "store" => $this->store ? new StoreResource($this->store) : null,
            "customer" => $this->customer ? new CustomerResource($this->customer) : null,

            "type"=> $type,
            "created_at"=>    Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
            "times_ago"=>    Carbon::parse($this->created_at)->diffForHumans(),
             "customer_id"=>auth('customers')->user()->id,
            "is_like"=>$like?true:false,
            "share_text"=>$this->getShareText(),
            "likes_count"=>$this->like()->count(),
            "comments_count"=>$this->comments()->count(),
            "views_count"=>$this->views_count,
            "video_duration"=>23,
            "start_date"=>Carbon::parse($this['start_date'])->format('Y-m-d'),
            "end_date"=>Carbon::parse($this['end_date'])->format('Y-m-d'),


        ];
}



    public function getVideoDuration($type){
        if ($type == "video")
       return \Illuminate\Support\Facades\Cache::get('video_duration_'.$this->media, function () use ($type) {
                return $this->formatSeconds(\FFMpeg::fromDisk('public')->open($this->media)->getDurationInSeconds());
        });
        else
            return "00:00";


    }

    public function formatSeconds($seconds){

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remaining_seconds = $seconds % 60;

        // Initialize an empty result string
        $time_format = '';

        // Add hours to the result if it's greater than zero
        if ($hours > 0) {
            $time_format .= sprintf("%02d:", $hours);
        }

        // Add minutes and seconds to the result
        $time_format .= sprintf("%02d:%02d", $minutes, $remaining_seconds);

        return $time_format ;

    }

    public function checkIfImageOrVideo()
    {
        $ext = pathinfo($this->media, PATHINFO_EXTENSION);
        $videoExtension = ['mp4','mov','mpg','mpeg','wmv','mkv','avi','ts','TS'];
        if (in_array( $ext,$videoExtension)){
            return "video";
        }
        return "image";
    }


    private function getShareText()
    {
        $data = [] ;

        $data[] = $this->description;
        $data[] = "";

        if ($this->media_url)
            $data[] = $this->media_url;

        if ($this->post_media && !empty($this->post_media)) {
            $data[] = "";
            foreach ($this->post_media as $media) {
                $data[] = "";

                $data[] = $media->media_url;
            }
        }

        return implode( PHP_EOL,$data);
    }
}
