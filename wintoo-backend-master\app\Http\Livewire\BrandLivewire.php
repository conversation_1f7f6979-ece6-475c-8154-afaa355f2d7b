<?php

namespace App\Http\Livewire;

use App\Models\Brand;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
class BrandLivewire extends Component
{
    use PublicFunction , Alert,WithPagination;
    public $columes;
    public $page_length = 10;
    public $search ;
    public $model_title="";
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $listeners = ['brand-livewire:conformDelete' => 'conformDelete'];
    protected $paginationTheme = 'bootstrap';
    public function mount()
    {
        $this->columes =Brand::getColumnLang();
        $this->model_title=\Lang::get('lang.add_brand');    
        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);
    }
    public function render()
    {

        $data = Brand::query();
        if($this->search){
            $this->resetPage();
            $search = $this->search;
            $data->where('name','LIKE','%'.$search.'%');
        }
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);

//        $this->is_not_admin();
        if(in_array('brands_show',$this->actions_permission()) ) {
            return view('dashboard.brands.index',[ 'data'=>$data])->extends('dashboard_layout.main');
        }else{

            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }
    }

    public function edit($id){
        //$this->is_not_admin();
        return redirect()->route('dashboard.brands.edit',$id);
    }


    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'brand-livewire:conformDelete',['id'=>$id]);
    }
    public function conformDelete($id){

        $brand = Brand::find($id['id']);
        if($brand->products->isNotEmpty()){
            $this->showModal('لا يمكن الحذف','لا يمكن حذف التصنيف  بسبب وجود منتجات مرتبطة بها','error');
            return  true;
        }
        Brand::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }


    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }


    public function setStatus($id){
        $object = Brand::find($id);
        $object->status =!$object->status;
        $object->save();
    }


}
