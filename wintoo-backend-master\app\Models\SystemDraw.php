<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemDraw extends Model
{
    use \App\Traits\PropertyGetter;


    protected $guarded = [];
    protected $appends = ["draw_date_format","is_old","is_finished","winer_id","winer_type"];
    protected  $casts = ["is_end" => "boolean"];

    public  static function  getColumnLang(){
        $columes=[
            'title'=>[\Lang::get('systemDraw.title') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'prize_name'=>[\Lang::get('systemDraw.prize_name') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'date'=>[\Lang::get('systemDraw.date') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'winer_id'=>[\Lang::get('systemDraw.winer_id') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'winer_type'=>[\Lang::get('systemDraw.winer_type') ,1,true,false, ['type'=>'string','actions'=>null] ],
            //	 'status'=>[\Lang::get('systemDraw.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
            'is_finished'=>[\Lang::get('systemDraw.is_finished') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'actions'=>['الخيارات',5,true,false,['type'=>'button','actions'=>['edit','delete']]],
        ];
        return $columes;
    }

    public static function getSearchable(){
        $columes=['title'=>[\Lang::get('SystemDraw.title')],
            'prize_name'=>[\Lang::get('SystemDraw.prize_name')],
            'date'=>[\Lang::get('SystemDraw.date')],
            'winer_id'=>[\Lang::get('systemDraw.winer_id')],

        ]; return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["title"])){
            $query->where("title","LIKE","%".$data["title"]."%");}
        if(isset($data["prize_name"])){
            $query->where("prize_name","LIKE","%".$data["prize_name"]."%");}
        if(isset($data["date"])){
            $query->where("date","LIKE","%".$data["date"]."%");}
        if(isset($data["winer_id"])){
            $query->where("winer_id","LIKE","%".$data["winer_id"]."%");}
        if(isset($data["winer_type"])){
            $query->where("winer_type","LIKE","%".$data["winer_type"]."%");}
        if(isset($data["status"])){
            $query->where("status","LIKE","%".$data["status"]."%");
        }
        if(isset($data["store_id"])){
            $query->where("store_id",$data["store_id"]);
        }

        return $query ;
    }


    public function getDrawDateFormatAttribute(){

        if($this->date){
            return Carbon::parse($this->date)->format('Y/m/d H:i:s');
        }

        return null;
    }


    public function getIsOldAttribute(){
        return Carbon::parse($this->date)->isPast();
    }


    public function getWinerIdAttribute(){



        $store = Store::find($this->winner_store_id);
        $user = Customer::find($this->winner_customer_id) ;

        $data= [] ;

        if ($store){
            $data[] = $store->name ;
        }

        if ($user){

            $data[] = $user->username ;

        }

        if (!$user&& !$store){
            $data[] = "لا يوجد فائز";
        }

        return implode(" - " ,$data );


    }

    public function getWinerTypeAttribute(){


        $store = Store::find($this->winner_store_id);
        $user = Customer::find($this->winner_customer_id) ;

        $data= [] ;

        if ($store){
            $data[] = "متجر" ;
        }

        if ($user){

            $data[] = "مستخدم" ;

        }

        if (!$user&& !$store){
            $data[] = "لا يوجد فائز";
        }

        return implode(" - " ,$data );




    }




    public function getIsFinishedAttribute(){
        return $this->is_end?\Lang::get('systemDraw.is_finished'):\Lang::get('systemDraw.not_finished') ;
    }

    public function store()
    {
        return $this->belongsTo(Store::class,"store_id");
    }

    public function sponsors()
    {
        return $this->belongsToMany(
            Sponsor::class,
            "system_draw_sponsors",
            "sponsor_id",
            "system_draw_id"
        );
    }
    public function scopeIsActive($query)
    {
        $endtDate = Carbon::today();

        return $query->where("is_end",false)/*->where("status",true)*/
        ->whereDate('date', '>=', $endtDate);
    }

    public function scopeCanBeViewed($query)
    {
        $endtDate = Carbon::today();
        return $query->where(function ($query)use ($endtDate){
            $query->where("is_end",false)/*->where("status",true)*/
            ->whereDate('date', '>=', $endtDate);
        })->orWhere(function ($query)use ($endtDate){
            $endtDate->subWeek();
            $query->where("is_end",true)->whereNotNull("winner_customer_id")
                ->whereDate('date', '>=', $endtDate);
        });
    }

    public function scopeHasWinners($query)
    {

        return $query->whereNotNull("winner_store_id")->whereNotNull("winner_customer_id") ;


    }


    public function scopeIsSystemDraw($query)
    {

        return $query->whereNull("store_id");

    }

    public function scopeIsStoreDraw($query)
    {

        return $query->whereNotNull("store_id");

    }


    public function scopeIsEndAndLast($query)
    {
        $endtDate = Carbon::today();

        return $query->where("is_end",true);
    }


}
