<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOffersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('offers', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('title')->nullable()->comment('title');
			$table->integer('store_id')->nullable()->comment('store_id');
			$table->boolean('status')->nullable()->comment('status');
			$table->dateTime('start_date')->nullable()->comment('start_date');
			$table->dateTime('end_date')->nullable()->comment('end_date');
			$table->timestamps();
			$table->string('image', 191)->nullable();
			$table->boolean('is_black')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('offers');
	}

}
