<?php

namespace App\Http\Resources;

use App\Models\Currency;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Favorite;
use App\Models\Reviews;

class ProductHomeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        try {
            $product_reviews_user_count = $this->reviews()->count('customer_id') ?? 0;
            $product_reviews = $this->reviews()->avg('rating') ?? 0;

            $is_favorite = false;
            $rate = false;
            
            if (auth('customers')->check()) {
                $favorite = Favorite::where('product_id', $this->id)
                    ->where('customer_id', auth('customers')->id())
                    ->first();
                $is_favorite = $favorite ? true : false;
                
                $rate = Reviews::where('status', 1)
                    ->where('customer_id', auth('customers')->id())
                    ->where('product_id', $this->id)
                    ->first();
            }

            $image_url = $this->generateImageUrl();

            $this->currency = Currency::find($this->currency_id);

            return [
                'id' => $this->id,
                'title' => $this->title,
                "price" => getRound($this->price),
                "is_available" => $this->stock_quantity > 0 ? 1 : 0,
                "description" => $this->description,
                "is_new" => $this->is_new,
                'is_offer' => $this->offer_id && $this->new_price,
                "new_price" => getRound($this->new_price),
                "currency_id" => $this->currency_id,
                "currency" => new CurrencyResource($this->currency),
                "charged_price" => getRound(getPriceForUser($this->price, $this->currency)),
                "charged_new_price" => getRound(getPriceForUser($this->new_price, $this->currency)),
                'charged_currency' => new CurrencyResource(getCurrentUserCurrency()),
                'offer_discount_rate' => $this->offer_discount_rate,
                "image_url" => $image_url,
                "category_id" => $this->category_id,
                "is_favorite" => $is_favorite,
                "rated" => $rate ? true : false,
            ];
        } catch (\Exception $e) {
            // Handle errors gracefully and log them
            \Log::error('Error transforming product resource: ' . $e->getMessage());
            return [
                'error' => 'Failed to load product data',
            ];
        }
    }

    /**
     * Generate the appropriate image URL.
     *
     * @return string
     */
    private function generateImageUrl()
    {
        $currentURL = \URL::current();
        $urlParts = explode('/', $currentURL);
        $currentUrlName = end($urlParts);

        switch ($currentUrlName) {
            case 'home':
                return 'https://wintoo.me/image/600/400/' . $this->file_image;
            case 'product':
                return 'https://zara.techmix.club/image/700/600/' . $this->file_image;
            default:
                return $this->image_url;
        }
    }
}
