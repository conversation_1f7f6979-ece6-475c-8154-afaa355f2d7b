<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;
class Customer extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable,SoftDeletes;
    protected $connection = "mysql";
    protected $fillable = [
            'phone',
            'email',
            'password',
            'username',
            'status',
            'device_type',
            'fcm_token',
            'lang_code',
            'sms_code',
            'sms_verify',
            'image',
            'bio',

            "country_id",
            "governorate_id",
            "city_id",
            "region_id",
            "currency_id",

            "whatsapp",
            "address",
            "sms_code",
            "token",
            "sms_verify",
            "is_chat_register",
             "chat_auth",
             "code",
             "phone_code",
             "full_mobile",
             'provider', 'provider_id',
             "whatsapp_phone_code"
    ];



    protected $appends = ['image_url','full_mobile'];
    protected $softDelete = true;
    protected $dates = ['deleted_at'];
    protected $hidden = [
        'password',
        'sms_code'
    ];
    public static $searchable=[
        'fullname'=>[],
        'id_card_number'=>[],
    ];



    public  static function  getColumnLang(){
        $columes=[
            '#'=>['#',1,true,false,[]],
            'image'=>[\Lang::get('customer.image') ,1,true,false,[]],
            'email'=>[\Lang::get('customer.email') ,1,true,false,[]],
            'phone'=>[\Lang::get('customer.phone') ,1,true,false,[]],
            'username'=>[\Lang::get('customer.username') ,1,true,false,[]],
            'password'=>[\Lang::get('customer.password') ,1,false,false,[]],
            'status'=>[\Lang::get('customer.status') ,1,true,false,[]],
            'actions'=>['الخيارات',5,true,false,['edit','delete']],
        ];
        return $columes;
    }

    public static function getSearchable(){
        $columes=['email'=>[\Lang::get('customer.email')],
            'phone'=>[\Lang::get('customer.phone')],
            'username'=>[\Lang::get('customer.username')],
            //'status'=>[\Lang::get('customer.status')],

        ]; return $columes;
    }
    protected static function boot()
    {

  parent::boot();


        Customer::deleting(function ($model) {
            Order::where('customer_id',$model->id)->delete();
            Favorite::where('customer_id',$model->id)->delete();
            Reviews::where('customer_id',$model->id)->delete();
        });


        static::creating(function ($model)
        {
            $model->attributes['full_mobile'] = getFullMobile($model->phone_code,$model->phone);
        });


        static::updating(function ($model)
        {
            $model->attributes['full_mobile'] = getFullMobile($model->phone_code,$model->phone);
        });



    }

    public function getImageUrlAttribute(){

       // return $this->image?asset('storage/customer/'.$this->image):asset('images/avatar_new.png');

        //return $this->image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->image]):null;

        if (strpos($this->image, 'https://') === 0) {
            return $this->image;
        }
        return $this->image?route("resize.storage",[config("app.def_thumb_width"),config("app.def_thumb_height"),"customer/$this->image"]):asset('images/avatar_new.png');

    }

    public function getImageLargeUrlAttribute(){

       // return $this->image?asset('storage/customer/'.$this->image):asset('images/avatar_new.png');

        //return $this->image?route("resize.storage",[config("app.def_img_width"),config("app.def_img_height"),$this->image]):null;

        if (strpos($this->image, 'https://') === 0) {
            return $this->image;
        }
        return $this->image?route("resize.storage",[config("app.def_large_width"),config("app.def_large_height"),"customer/$this->image"]):asset('images/avatar_new.png');

    }

    public function getFullMobileAttribute(){

      return $this->getFullMobile();

    }

    public function getFullMobile(){

        return getFullMobile($this->phone_code,$this->phone);
    }

    public function scopeWhereMobile($query, $data)
    {

        $mobileNumberWithoutLeadingZero = preg_replace('/^0/', '', $data["phone"], 1);
        $query->where("phone",$mobileNumberWithoutLeadingZero);
        $query->where("phone_code",$data["phone_code"] );

    }

    public function scopeSearch($query, $data) {
        if(isset($data["email"])){
            $query->where("email","LIKE","%".$data["email"]."%");}
        if(isset($data["phone"])){
            $query->where("phone","LIKE","%".$data["phone"]."%");}
        if(isset($data["username"])){
            $query->where("username","LIKE","%".$data["username"]."%");}
        if(isset($data["password"])){
            $query->where("password","LIKE","%".$data["password"]."%");}
        if(isset($data["status"])){
            $query->where("status","LIKE","%".$data["status"]."%");}
        if(isset($data["image"])){
            $query->where("image","LIKE","%".$data["image"]."%");}
        return $query ;
    }

    public function country(){
        return $this->belongsTo(Country::class,"country_id");
    }

    public function governorate(){
        return $this->belongsTo(Governorate::class,"governorate_id");
    }

    public function city(){
        return $this->belongsTo(City::class,"city_id");
    }

    public function region(){
        return $this->belongsTo(Region::class,"region_id");
    }


    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');
    }


    public function follower(){
        return $this->belongsToMany(Customer::class,"users_followers","followed_customer_id","customer_id")->distinct();
    }

}
