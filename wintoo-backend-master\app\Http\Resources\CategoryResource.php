<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id"=>$this->id,
            "parent_id"=> $this->parent_id,
            "name"=> $this->name,
            "image_url"=> $this->image_url,
            "banner_url"=> $this->banner_url,
            "has_children"=> count($this->children)
        ];
      //  return parent::toArray($request);
    }
}
