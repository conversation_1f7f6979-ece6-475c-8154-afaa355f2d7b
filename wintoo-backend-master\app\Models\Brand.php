<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
class Brand extends Model
{
use \App\Traits\PropertyGetter;
    use Loggable;
    use HasFactory;
    protected $guarded=[];
    protected $appends=['image_url'];
    protected $connection = "mysql";

    public function brandUrl(){
        return $this->image?asset('storage/brand/'.$this->image):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image)));
    }

    public function getImageUrlAttribute(){
        return $this->image?asset('storage/brand/'.$this->image):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image)));
    }

    public function scopeIsActive($query){
        return $query->where('status',1);
    }
    public  static function  getColumnLang(){

        $columes=[
            'image'=>['الصورة',1,true,false,[]],
            'name'=>['الاسم',2,true,true,[]],
            'order'=>['الترتيب',3,true,false,[]],
            'status'=>['الحالة',3,true,false,[]],
            'actions'=>['الخيارات',4,true,false,['edit','show','delete']],
        ];
        return $columes;
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function setStatus()
    {
        $this->status = !$this->status;
    }
}
