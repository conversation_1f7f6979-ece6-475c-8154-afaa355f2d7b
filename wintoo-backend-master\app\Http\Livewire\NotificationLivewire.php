<?php

namespace App\Http\Livewire;

use App\Models\Customer;
use App\Models\Store;
use App\Models\User;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;



use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;


class NotificationLivewire extends Component

{
 use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $title = "";
        public $columes;
        public $page_length = 10;
        public $search ;
        public $model_title="";
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $listeners = ['governorate-livewire:conformDelete' => 'conformDelete'];
        public $notification;

       public function render()
       {

           return view('dashboard.notification.index')->extends('dashboard_layout.main');
       }




// public function send(){




// $factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));


// $messaging = $factory->createMessaging();


//     $fcmTokens = Customer::whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
// $notification = Notification::create('Title', 'Body');

// // Create CloudMessage with notification
// $message = CloudMessage::new()->withNotification($notification);

// // Send notification to all FCM tokens in one statement
// try {
//     $messaging->sendMulticast($message, $fcmTokens);
//     echo "Notification sent successfully to all FCM tokens\n";
// } catch (\Throwable $e) {
//     // Handle errors
//     echo "Error sending notification to FCM tokens. Error: ".$e->getMessage()."\n";
// }

// }





//      public function send(){
//
//$factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));
//
//
//$messaging = $factory->createMessaging();
//
//
//    $fcmTokens = Customer::whereNotNull('fcm_token')->orderBy('created_at', 'desc')->pluck('fcm_token')->toArray();
//     $dataMsg=[
//                  'title' => $this->notification['title'],
//                  'body' => $this->notification['description'],
//                  'id'=>null,
//                  'type'=>'general_notification',
//                  'link'=>$this->notification['link']
//              ];
//
////$notification = Notification::create($dataMsg['title'], $dataMsg['body']);
//          $notification = Notification::create($dataMsg['title'], $dataMsg['body']);
//
//
//          $customers = Customer::whereNotNull('fcm_token')->get();
//          foreach ( $customers as $customer){
//
//              $data=[
//                  'title' => $this->notification['title'],
//                  'body' => $this->notification['description'],
//                  'id'=>null,
//                  'type'=>'general_notification',
//                  'link'=>$this->notification['link']
//              ];
//
//            //   fcmNotification($customer,$data);
//                  \Notification::send($customer,
//                  new \App\Notifications\GeneralNotification(
//                      $data['title'],
//                      'general_notification',
//                      $data
//                  ));
//          }
//
//
//
//           $message = CloudMessage::new()->withNotification($notification);
//
//// Send notification to all FCM tokens in one statement
//$batchSize = 150; // Adjust as needed
//
//// Split FCM tokens into batches and send notifications
//$chunks = array_chunk($fcmTokens, $batchSize);
//foreach ($chunks as $chunk) {
//    try {
//        $messaging->sendMulticast($message, $chunk);
//        // echo "Notification sent successfully to batch of FCM tokens\n";
//    } catch (\Throwable $e) {
//        // Handle errors
//        // echo "Error sending notification to batch of FCM tokens. Error: ".$e->getMessage()."\n";
//    }
//}
//
//         $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
//
//}


//    public function send()
//    {
//        try {
//            // Initialize Firebase with service account
//            $firebase = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
//            $messaging = $firebase->createMessaging();
//
//            // Retrieve FCM tokens of customers
//            $fcmTokens = Customer::whereNotNull('fcm_token')->orderBy('created_at', 'desc')->pluck('fcm_token')->toArray();
//
//            $dataMsg = [
//                'title' => $this->notification['title'],
//                'body' => $this->notification['description'],
//                'id' => null,
//                'type' => 'general_notification',
//                'link' => $this->notification['link'],
//            ];
//
//            // Create Firebase Notification object
//            $notification = FirebaseNotification::create($dataMsg['title'], $dataMsg['body']);
//
//            foreach ($fcmTokens as $fcmToken) {
//                try {
//                    // Create Firebase Cloud Message with the FCM token
//                    $firebaseMessage = CloudMessage::withTarget('token', $fcmToken)
//                        ->withNotification($notification)
//                        ->withData($dataMsg);
//
//                    // Send the notification
//                    $messaging->send($firebaseMessage);
//
//                    // Log success
//                    logger()->info("Notification sent successfully to FCM token: $fcmToken");
//
//                } catch (\Throwable $e) {
//                    // Log the error for specific token failure
//                    logger()->error("Error sending notification to FCM token: $fcmToken - " . $e->getMessage());
//                }
//            }
//
//            // Optional: Show modal confirming the notification was sent successfully
//            $this->showModal(\Lang::get('lang.saved_done'), \Lang::get('lang.saved_changed'), 'success');
//
//        } catch (\Throwable $e) {
//            // Handle and log the error
//            logger()->error('Error sending Firebase notification: ' . $e->getMessage());
//            throw new \Exception('Failed to send Firebase notification: ' . $e->getMessage());
//        }
//    }


    public function send()
    {
        try {
            // Initialize Firebase with service account
            $firebase = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
            $messaging = $firebase->createMessaging();

            // Retrieve FCM tokens of customers
            $fcmTokens = Customer::whereNotNull('fcm_token')->orderBy('created_at', 'desc')->pluck('fcm_token')->toArray();

            // Prepare notification data
            $dataMsg = [
                'title' => $this->notification['title'],
                'body' => $this->notification['description'],
                'link' => $this->notification['link'],
                'type' => 'general_notification',
            ];

            // Create Firebase Notification object
            $notification = FirebaseNotification::create($dataMsg['title'], $dataMsg['body']);

            // Send in-app notifications
            $this->sendInAppNotifications($dataMsg);
            // Send out-of-app notifications
            $this->sendOutOfAppNotifications($messaging, $fcmTokens, $notification, $dataMsg);

            // Optional: Show modal confirming the notification was sent successfully
            $this->showModal(\Lang::get('lang.saved_done'), \Lang::get('lang.saved_changed'), 'success');

        } catch (\Throwable $e) {
            // Handle and log the error
            logger()->error('Error sending notifications: ' . $e->getMessage());
            throw new \Exception('Failed to send notifications: ' . $e->getMessage());
        }
    }

    /**
     * Send out-of-app notifications using FCM tokens.
     *
     * @param $messaging
     * @param array $fcmTokens
     * @param $notification
     * @param array $dataMsg
     */
    private function sendOutOfAppNotifications($messaging, array $fcmTokens, $notification, array $dataMsg)
    {
        foreach ($fcmTokens as $fcmToken) {
            try {
                // Create Firebase Cloud Message with the FCM token
                $firebaseMessage = CloudMessage::withTarget('token', $fcmToken)
                    ->withNotification($notification)
                    ->withData($dataMsg);

                // Send the notification
                $messaging->send($firebaseMessage);

                // Log success
                logger()->info("Notification sent successfully to FCM token: $fcmToken");

            } catch (\Throwable $e) {
                // Log the error for specific token failure
                logger()->error("Error sending notification to FCM token: $fcmToken - " . $e->getMessage());
            }
        }
    }

    /**
     * Send in-app notifications to all customers.
     *
     * @param array $dataMsg
     */
    private function sendInAppNotifications(array $dataMsg)
    {
        // Retrieve all customers with FCM tokens
        $customers = Customer::whereNotNull('fcm_token')->get();

        // Check if there are customers available
        if ($customers->isEmpty()) {
            logger()->warning("No customers available for in-app notification.");
            return;
        }

        foreach ($customers as $customer) {
            try {
                // Send the in-app notification
                \Notification::send($customer, new \App\Notifications\GeneralNotification(
                    $dataMsg['title'],
                    'general_notification',
                    $dataMsg
                ));
                logger()->info("In-app notification sent to customer: ID {$customer->id}");
            } catch (\Throwable $e) {
                logger()->error("Error sending in-app notification to customer: ID {$customer->id} - " . $e->getMessage());
            }
        }
    }





    public function sendToStore(){


            $factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));


            $messaging = $factory->createMessaging();


            $fcmTokens = Store::whereNotNull('fcm_token')->orderBy('created_at', 'desc')->pluck('fcm_token')->toArray();
             $dataMsg=[
                  'title' => $this->notification['title'],
                  'body' => $this->notification['description'],
                  'id'=>null,
                  'type'=>'general_notification',
                  'link'=>$this->notification['link']
              ];

            $notification = Notification::create($dataMsg['title'], $dataMsg['body']);

          $customers = Store::whereNotNull('fcm_token')->get();
          foreach ( $customers as $customer){

              $data=[
                  'title' => $this->notification['title'],
                  'body' => $this->notification['description'],
                  'id'=>null,
                  'type'=>'general_notification',
                  'link'=>$this->notification['link']
              ];

            //   fcmNotification($customer,$data);
                  \Notification::send($customer,
                  new \App\Notifications\GeneralNotification(
                      $data['title'],
                      'general_notification',
                      $data
                  ));
          }



          $message = CloudMessage::new()->withNotification($notification);

                // Send notification to all FCM tokens in one statement
                $batchSize = 150; // Adjust as needed

                // Split FCM tokens into batches and send notifications
                $chunks = array_chunk($fcmTokens, $batchSize);
                foreach ($chunks as $chunk) {
                    try {
                        $messaging->sendMulticast($message, $chunk);
                        // echo "Notification sent successfully to batch of FCM tokens\n";
                    } catch (\Throwable $e) {
                        // Handle errors
                        // echo "Error sending notification to batch of FCM tokens. Error: ".$e->getMessage()."\n";
                    }
                }

         $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

}





//Orignal Code
    //   public function send(){



    //       $customers = Customer::whereNotNull('fcm_token')->get();
    //       $fcmTokens = Customer::whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
    //       foreach ( $customers as $customer){

    //           $data=[
    //               'title' => $this->notification['title'],
    //               'body' => $this->notification['description'],
    //               'id'=>null,
    //               'type'=>'general_notification',
    //               'link'=>$this->notification['link']
    //           ];

    //         //   fcmNotification($customer,$data);
    //               \Notification::send($customer,
    //               new \App\Notifications\GeneralNotification(
    //                   $data['title'],
    //                   'general_notification',
    //                   $data
    //               ));
    //       }

    //       $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    //   }



   }



