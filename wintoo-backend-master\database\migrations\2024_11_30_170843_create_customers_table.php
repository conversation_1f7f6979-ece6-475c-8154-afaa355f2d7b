<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('customers', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('email', 191)->nullable();
			$table->string('phone', 191)->nullable();
			$table->string('full_mobile', 191)->nullable();
			$table->string('username', 191)->nullable();
			$table->string('password', 191);
			$table->boolean('status')->default(1);
			$table->string('device_type', 191)->nullable();
			$table->string('fcm_token', 191)->nullable();
			$table->string('sms_code', 191)->nullable();
			$table->boolean('sms_verify')->default(0);
			$table->timestamps();
			$table->string('image', 191)->nullable();
			$table->softDeletes();
			$table->integer('governorate_id')->nullable();
			$table->integer('city_id')->nullable();
			$table->integer('region_id')->nullable();
			$table->string('whatsapp', 191)->nullable()->comment('whatsapp');
			$table->string('address', 191)->nullable()->comment('address');
			$table->string('token', 191)->nullable()->comment('token');
			$table->boolean('is_chat_register')->default(0);
			$table->string('chat_auth', 250)->nullable();
			$table->integer('country_id')->nullable();
			$table->string('code', 191)->nullable();
			$table->string('phone_code', 191)->nullable();
			$table->integer('currency_id')->nullable()->default(1);
			$table->string('whatsapp_phone_code', 191)->nullable();
			$table->string('lang_code', 191)->nullable();
			$table->string('bio', 191)->nullable();
			$table->enum('phone_visibilty', array('public','followers','private'))->nullable()->default('private');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('customers');
	}

}
