<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Like extends Model
{
use \App\Traits\PropertyGetter;

     use \App\Traits\PropertyGetter;
     protected $fillable = ['likeable_id','likeable_type','customer_id'];

     public function likeable()
    {
        return $this->morphTo();
    }

    public function customer(){
        return $this->belongsTo(Customer::class);
    }

}
