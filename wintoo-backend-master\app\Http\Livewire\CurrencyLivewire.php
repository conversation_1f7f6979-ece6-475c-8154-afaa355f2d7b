<?php

namespace App\Http\Livewire;

use App\Models\Product;
use App\Models\Store;
use App\Models\SubCategory;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Currency;
class CurrencyLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Currency-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'currency';
        public function mount()
            {
                $searchable = Currency::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Currency::getColumnLang();
                $this->searchable =Currency::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =Currency::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);

               return view('dashboard/currency/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }

        public function search(){
                    $this->resetPage();
        }

        public function resetSearch(){
            $this->search_array=[];
         }

        public function edit($id){
                 return redirect()->route('dashboard.currency.edit',$id);
             }

    public function delete($id){

     $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Currency-livewire:conformDelete',['id'=>$id]);

    }


    public function conformDelete($id){

        $object = Currency::where($id['id'])->first();
        $getStoreConnectedToCategory = Product::where("currency_id",$object->id)->get();
            if($getStoreConnectedToCategory->isNotEmpty() || ($id == 1 || $id == 2) ){
                $this->showModal("خطأ ","هذه العملة  مرتبطة بمنتجات. او لا يمكن حذفها",'error');
                return true;
            }
                 Currency::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
    public function setStatus($id){
        $object = Currency::find($id);
        $object->status =!$object->status;
        $object->save();
    }
}

