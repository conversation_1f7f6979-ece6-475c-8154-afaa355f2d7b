<?php

namespace App\Http\Livewire;

use App\Services\VideoEdit;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Post;
class PostFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'post';
      public $post;
      public $image;
      protected $listeners = ['Post-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "post.description"=>'required',
                "post.media"=>'nullable',
                "post.media_type"=>'nullable',
                "post.store_id"=>'nullable',
                "post.status"=>'nullable',
       ];

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Post::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->post  = $id?Post::find($id):new Post();
          }
      public function render()
          {
              return view('dashboard/post/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               if (auth()->user()->type=="STORE"){
                   $this->post->store_id = auth()->user()->store->id;
               }

               $filename = $this->image?$this->image->store('/','public'):$this->post->media;
               $ext = pathinfo($filename, PATHINFO_EXTENSION);
               $videoExtension = ['mp4','mov','mpg','mpeg','wmv','mkv','avi','ts','TS'];
               $this->post->media = $filename;
               if (in_array( $ext, $videoExtension)){
                   $this->post->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
               }

                $this->post->save();
                \DB::commit();
                $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

               return redirect()->route('dashboard.post');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


