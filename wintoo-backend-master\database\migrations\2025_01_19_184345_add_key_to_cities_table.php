<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddKeyToCitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->string('key')->nullable();
            $table->index(['key', 'governorate_id', 'country_id']);
            $table->index(['key', 'country_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->dropColumn('key');
            $table->dropIndex(['key', 'governorate_id', 'country_id']);
            $table->dropIndex(['key', 'country_id']);
        });
    }
}
