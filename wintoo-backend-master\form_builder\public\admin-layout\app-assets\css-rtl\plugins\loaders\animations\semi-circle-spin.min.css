@-webkit-keyframes spin-rotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-webkit-transform:rotate(-360deg);transform:rotate(-360deg)}}@keyframes spin-rotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-webkit-transform:rotate(-360deg);transform:rotate(-360deg)}}.semi-circle-spin{position:relative;width:35px;height:35px;overflow:hidden}.semi-circle-spin>div{position:absolute;border-width:0;border-radius:100%;-webkit-animation:spin-rotate .6s 0s infinite linear;animation:spin-rotate .6s 0s infinite linear;background-image:-webkit-linear-gradient(transparent 0,transparent 70%,#B8C2CC 30%,#B8C2CC 100%);background-image:linear-gradient(transparent 0,transparent 70%,#B8C2CC 30%,#B8C2CC 100%);width:100%;height:100%}