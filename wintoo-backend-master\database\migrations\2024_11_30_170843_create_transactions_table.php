<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('transactions', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('store_id')->nullable()->comment('store_id');
			$table->integer('order_id')->nullable()->comment('order_id');
			$table->integer('customer_id')->nullable()->comment('customer_id');
			$table->string('type', 191)->nullable()->comment('type');
			$table->string('status', 191)->nullable()->comment('status');
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('transactions');
	}

}
