!function(a){"use strict";a(".pickadate").pickadate(),a(".format-picker").pickadate({format:"mmmm, d, yyyy"}),a(".pickadate-limits").pickadate({min:[2019,3,20],max:[2019,5,28]}),a(".pickadate-disable").pickadate({disable:[1,[2019,3,6],[2019,3,20]]}),a(".pickadate-translations").pickadate({formatSubmit:"dd/mm/yyyy",monthsFull:["<PERSON><PERSON>","Février","Mars","Avril","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","Sept<PERSON><PERSON>","Octobre","<PERSON><PERSON><PERSON>","Décembre"],monthsShort:["Jan","Fev","Mar","Avr","Mai","Juin","Juil","Aou","Sep","Oct","Nov","Dec"],weekdaysShort:["Di<PERSON>","<PERSON>n","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>"],today:"aujourd'hui",clear:"clair",close:"<PERSON>rm<PERSON>"}),a(".pickadate-months").pickadate({selectYears:!1,selectMonths:!0}),a(".pickadate-months-year").pickadate({selectYears:!0,selectMonths:!0}),a(".pickadate-short-string").pickadate({weekdaysShort:["S","M","Tu","W","Th","F","S"],showMonthsShort:!0}),a(".pickadate-firstday").pickadate({firstDay:1}),a(".pickatime").pickatime(),a(".pickatime-format").pickatime({format:"T!ime selected: h:i a",formatLabel:"HH:i a",formatSubmit:"HH:i",hiddenPrefix:"prefix__",hiddenSuffix:"__suffix"}),a(".pickatime-formatlabel").pickatime({formatLabel:function(a){var e=(a.pick-this.get("now").pick)/60,i=e<0?" !hours to now":0<e?" !hours from now":"now";return"h:i a <sm!all>"+(e?Math.abs(e):"")+i+"</sm!all>"}}),a(".pickatime-min-max").pickatime({min:new Date(2015,3,20,7),max:new Date(2015,7,14,18,30)}),a(".pickatime-intervals").pickatime({interval:150}),a(".pickatime-disable").pickatime({disable:[3,5,7,13,17,21]}),a(".pickatime-close-action").pickatime({closeOnSelect:!1,closeOnClear:!1})}((window,document,jQuery));