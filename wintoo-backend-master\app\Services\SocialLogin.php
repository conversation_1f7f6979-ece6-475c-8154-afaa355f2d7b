<?php
namespace App\Services;

use App\Models\Customer;
use App\Utils\AppleUtils;
use Laravel\Socialite\Facades\Socialite;
use App\Models\Store;

class SocialLogin
{
    public static function storeSocialLogin($accessToken, $authCode, $provider)
    {
        try {
            $id = null;
            if ($provider == 'apple') {
                $email = self::getAppleUserEmail($authCode);
                $id = self::getAppleUserId($authCode);
            } else {
                // Use the access token to fetch user details
                $socialUser = Socialite::driver($provider)->userFromToken($accessToken);
                $email = $socialUser->getEmail();
                $id = $socialUser->getId();
            }

            $user = Store::where("email", $email)->first();
            if (!$user && $id) {
                $user = Customer::where("provider_id", $id)->first();
                $user = Store::where("email", $user->email)->first();
            }
            return $user;
        } catch (\Exception $e) {
            return response()->json(['error' => 'Authentication failed'], 500);
        }
    }
    public static function getAppleUserEmail($idToken)
    {
        $socialUser = AppleUtils::extractData($idToken);
        $email = $socialUser['email'];
        return $email;
    }
    public static function getAppleUserId($idToken)
    {
        $socialUser = AppleUtils::extractData($idToken);
        $id = $socialUser['sub'];
        return $id;
    }
}