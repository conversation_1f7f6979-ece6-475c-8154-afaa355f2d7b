<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QRRequest extends Model
{
    use \App\Traits\PropertyGetter;


    protected $guarded = [];
    protected $appends = ['status_name'];
    public  static function  getColumnLang(){
        $columes=[
            'store_id'=>[\Lang::get('qRRequest.store_id') ,1,true,false, ['type'=>'integer','actions'=>null] ],
            'customer_id'=>[\Lang::get('qRRequest.customer_id') ,1,true,false, ['type'=>'integer','actions'=>null] ],
            'status'=>[\Lang::get('qRRequest.status') ,1,true,false, ['type'=>'string','actions'=>null] ],
            'actions'=>['الخيارات',2,true,false,['type'=>'button','actions'=>['edit','delete']]],
        ];
        return $columes;
    }
    public static function getSearchable(){
        $columes=['store_id'=>
            [\Lang::get('QRRequest.store_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'Store']],
            'customer_id'=>[\Lang::get('QRRequest.customer_id'),
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'Customer']
            ],
            'status'=>[\Lang::get('QRRequest.status')],

        ];
        return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["store_id"])){
            $query->where("store_id","LIKE","%".$data["store_id"]."%");}
        if(isset($data["customer_id"])){
            $query->where("customer_id","LIKE","%".$data["customer_id"]."%");}
        if(isset($data["status"])){
            $query->where("status","LIKE","%".$data["status"]."%");}
        return $query ;
    }
    public function getStatusNameAttribute(){
        if ($this->status == "New"){

            return \Lang::get('qr_requests.New');
        }
        if ($this->status == "Rejected"){

            return \Lang::get('qr_requests.Rejected');
        }
        if ($this->status == "Approved"){

            return \Lang::get('qr_requests.Approved');
        }

    }
    public function store(){
        return $this->belongsTo(Store::class,"store_id");
    }
    public function customer(){
        return $this->belongsTo(Customer::class,"customer_id");
    }
}
