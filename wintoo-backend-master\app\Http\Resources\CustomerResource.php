<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Follower;
use App\Models\Maincolor;
use App\Models\Size;
use App\Models\UsersFollowers;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */


    public function toArray($request)
    {


        $is_follow = false;

        if(auth('customers')->check()){

            $follow  = UsersFollowers::where('followed_customer_id',$this->id)
                ->where('customer_id',auth('customers')->id())
                ->first();

            $is_follow =$follow?true:false;


        }



        $followers  = UsersFollowers::where('followed_customer_id',$this->id)->whereHas("customer")
            ->count();


        return [
            'id'=>$this->id,
            'bio'=>$this->bio??"",
            'email'=>$this->email,
            'phone'=>$this->phone,
            'mobile'=>$this->phone,
            'full_mobile'=>$this->full_mobile,
            'username'=>$this->username,
            'status'=>$this->status,
            'sms_verify'=>$this->sms_verify,
            'device_type'=>$this->device_type,
            'fcm_token'=>$this->fcm_token,
            'image'=>$this->image,
            'image_url'=>$this->image_url,
            'image_large_url'=>$this->image_large_url,
            'whatsapp'=>$this->whatsapp,

            'whatsapp_phone_code'=>$this->whatsapp_phone_code,

            'country_id'=>$this->country_id,
            'country'=>new GeneralResource($this->country),

            'governorate_id'=>$this->governorate_id,
            'governorate'=> GeneralResource::make($this->governorate),

            'city_id'=>$this->city_id,
            'city'=>new GeneralResource($this->city),

             'currency_id'=>$this->currency_id,
            'currency'=>new CurrencyResource($this->currency),

            'region_id'=>$this->region_id,
            'region'=>new GeneralResource($this->region),
            "is_chat_register"=>$this->is_chat_register,
            "auth_token"=>$this->chat_auth,
            "uid"=>"user_".$this->id,

            'address'=>$this->address,
            'created_at'=>$this->created_at,
            'code'=>$this->code,
            'phone_code'=>$this->phone_code,

            'followers_count'=>$followers,
            'is_follow'=>$is_follow,
            'phone_visibilty'=>$this->phone_visibilty,

        ];

    }
}
