<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVariationsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('variations', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('product_id', 191)->nullable()->comment('product_id');
			$table->string('variation_id', 191)->nullable()->comment('variation_id');
			$table->string('name', 191)->nullable()->comment('name');
			$table->string('is_in_stock', 191)->nullable()->comment('is_in_stock');
			$table->string('product_size', 191)->nullable()->comment('product_size');
			$table->string('product_color', 191)->nullable()->comment('product_color');
			$table->string('stock_code', 191)->nullable()->comment('stock_code');
			$table->string('currency', 191)->nullable()->comment('currency');
			$table->string('selling_price', 191)->nullable()->comment('selling_price');
			$table->string('vat_included', 191)->nullable()->comment('vat_included');
			$table->string('vat_percent', 191)->nullable()->comment('vat_percent');
			$table->string('stock_quantity', 191)->nullable()->comment('stock_quantity');
			$table->string('specific_sale_price', 191)->nullable()->comment('specific_sale_price');
			$table->string('display_selling_price', 191)->nullable()->comment('display_selling_price');
			$table->string('store_selling_price', 191)->nullable()->comment('store_selling_price');
			$table->string('display_symbol', 191)->nullable()->comment('display_symbol');
			$table->string('display_currency', 191)->nullable()->comment('display_currency');
			$table->string('color_en', 191)->nullable()->comment('color_en');
			$table->string('size_en', 191)->nullable()->comment('size_en');
			$table->string('offer_price', 191)->nullable()->comment('offer_price');
			$table->string('offer_type', 191)->nullable()->comment('offer_type');
			$table->float('price_after_offer', 10, 0)->nullable()->comment('price_after_offer');
			$table->string('store_jan_product_id', 191)->nullable();
			$table->timestamps();
			$table->string('product_size_id', 191)->nullable();
			$table->string('product_color_id', 191)->nullable();
			$table->string('category_id', 191)->nullable();
			$table->string('profit_type', 191)->nullable();
			$table->float('profit_amount', 10, 0)->nullable();
			$table->float('profit_price', 10, 0)->nullable()->default(0);
			$table->boolean('status')->default(1);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('variations');
	}

}
