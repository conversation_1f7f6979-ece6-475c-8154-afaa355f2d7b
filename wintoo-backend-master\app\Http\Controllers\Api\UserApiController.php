<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\OrderResource;
use App\Models\Address;
use App\Models\ContactUS;
use App\Models\Customer;
use App\Models\Store;
use App\Models\CustomersBio;
use App\Models\Favourite;
use App\Models\Follower;
use App\Models\Order;
use App\Models\ProductRequest;
use App\Models\UsersFollowers;
use App\Services\CometChat;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Lang;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class UserApiController extends BaseControllerApi
{

 public function saveProfile(Request $request)
{
    $this->validate($request, [
        'email' => 'required|email|unique:customers,email,' . $this->CURRENT_USER->id,
        'username' => 'required',
        'country_id' => 'required',
        'governorate_id' => 'required',
        'city_id' => 'required',
        'phone' => 'required',
        'address' => 'required',
        'bio' => 'nullable|string',
        'currency_id' => 'nullable|int',
    ], [], [
        'email' => Lang::get("lang.email"),
        'phone.required' => 'Please enter your phone number.',
        'phone.phone'    => 'Your phone number must be a valid mobile number in E.164 format (e.g., +1234567890).',
    ]);

    $customer = Customer::find($this->CURRENT_USER->id);

    $filename = $request->file('image') ? $request->image->store('/', 'customer') : $customer->image;

    $customer->update([
        'image' => $filename,
        'email' => $request['email'] ?? $customer->email,
        'username' => $request['username'],
        'bio' => $request['bio'],
        'country_id' => $request['country_id'],
        'phone' => $request['phone'],
        'phone_code' => $request['phone_code'],
        'code' => $request['code'],
        'governorate_id' => $request['governorate_id'],
        'city_id' => $request['city_id'],
        'region_id' => $request['region_id'],
        'currency_id' => $request['currency_id'],
        'address' => $request['address'],
    ]);

    $chat = new CometChat();
    $chat->create_user($customer, true, "user_" . $customer->id);

    return $this->sendResponse(new CustomerResource($customer), '');
}



    public function change_mobile_visibility(Request $request)
    {

        $customer = Customer::find($this->CURRENT_USER->id);

        if(in_array($request->status ,["public","followers","private"])){

            $customer->phone_visibilty = $request->status ;
            $customer->save();

             return $this->sendResponse( new CustomerResource($customer),'');
        }

        return $this->sendError( "invalid_status",422);

    }

    public function add_bio_item(Request $request)
    {

        $this->validate($request,[
            //   'email'=>'required|email',
            'place'=>'required',
            //  'username'=>'required',
            'type'=>'in:living,study,work',
            'current'=>'required',

        ],[],[

        ]);



        $create =    CustomersBio::create([

            "place" => $request->place ,
            "type" => $request->type ,
            "customer_id" => $this->CURRENT_USER->id ,
            "current" => $request->current ,

        ]);

        $customerBios =  CustomersBio::where('customer_id',$this->CURRENT_USER->id)->where('id','!=',$create->id)->where('type','=',$request->type)->get();

        if($create->current){

            foreach ($customerBios as $customerBio){

                $customerBio->current = false;
                $customerBio->save();
            }

        }

        if ($customerBios->isEmpty()){
            $create->current = true ;
            $create->save();
        }

        return $this->sendResponse( $create,'');

    }
    public function edit_bio_item(Request $request , $id)
    {

        $this->validate($request,[
            //   'email'=>'required|email',
            'place'=>'required',
            //  'username'=>'required',
            'type'=>'in:living,study,work',

            'current'=>'required',

        ],[],[

        ]);



        $update =    CustomersBio::where("id",$id)
            ->where("customer_id",$this->CURRENT_USER->id)->first() ;

        $update->update([

                "place" => $request->place ,
                "type" => $request->type ,
                "current" => $request->current ,

        ]);



        $customerBios =  CustomersBio::where('customer_id',$this->CURRENT_USER->id)->where('id','!=',$update->id)->where('type','=',$request->type)->get();

        if($update->current){

            foreach ($customerBios as $customerBio){

                $customerBio->current = false;
                $customerBio->save();
            }

        }

        if ($customerBios->isEmpty()){
            $update->current = true ;
            $update->save();
        }


        return $this->sendResponse( $update,'');

    }

    public function get_bio_item(Request $request )
    {

        $bios =    CustomersBio::where("customer_id",$this->CURRENT_USER->id) ;

        if ($request->type){
            $bios->where("type",$request->type) ;
        }

        $bios  = $bios->get();

        $out = [
            "living"=>[] ,
            "study"=>[] ,
            "work"=>[] ,
            ] ;



        $bios->each(function ($item) use (&$out){
            $out[$item->type][] = $item ;
        });

        return $this->sendResponse( $out,'');

    }

    public function delete_bio_item(Request $request , $id)
    {


         CustomersBio::where("id",$id)
            ->where("customer_id",$this->CURRENT_USER->id)->delete() ;


        return $this->sendResponse(null, __("lang.deleted_successfully"));

    }

        public  function contact_us(Request $request){
        $this->validate($request,[
            'email'=>'required|email',
            'description'=>'required',
            'phone' => 'required|numeric'
        ]);

        $contact_us = ContactUS::create($request->all());

        return $this->sendResponse($contact_us,'');
    }


    public function addresses(){
        $data = Address::where('customer_id',$this->CURRENT_USER->id)->paginate(12);
        $result= [
            'address'=>AddressResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]

        ];
        return $this->sendResponse($result,'');
    }

    public function save_address(Request $request){

        $this->validate($request,[
            'city'=>'required',
            'country_id'=>'required',
            'state'=>'required',
            'fullname'=>'required',
            'addressline1'=>'required',
            'zipcode'=>'required',
            'phone'=>'required',
            'name'=>'required',
            'is_default'=>'required'
        ]);


        \DB::beginTransaction();

        try {
            $create = Address::create([
                'city'=>$request->city,
                'state'=>$request->state,
                'country_id'=>$request->country_id,
                'email'=>$request->email,
                'fullname'=>$request->fullname,
                'addressline1'=>$request->addressline1,
                'addressline2'=>$request->addressline2,
                'zipcode'=>$request->zipcode,
                'phone'=>$request->phone,
                'is_default'=>$request->is_default,
                'customer_id'=>$this->CURRENT_USER->id,
                'name'=>$request->name,
            ]);

            $addresses =  Address::where('customer_id',$this->CURRENT_USER->id)->where('id','!=',$create->id)->get();

            if($create->is_default){
                foreach ($addresses as $address){

                    $address->is_default = false;
                    $address->save();
                }
            }

            \DB::commit();
            $data = AddressResource::collection([$create]);
            return $this->sendResponse($data,'');
        } catch (\Exception $e) {
            //$e->getMessage()
            \DB::rollback();

            return $this->sendError($e->getMessage(),500);
        }
    }


    public function update_address(Request $request){
        $this->validate($request,[
            'address_id'=>'required',
            'city'=>'required',
            'country_id'=>'required',
            'state'=>'required',
            'fullname'=>'required',
            'addressline1'=>'required',
            'zipcode'=>'required',
            'phone'=>'required',
            'name'=>'required',
            'is_default'=>'required'
        ]);

        \DB::beginTransaction();

        try {
            $create=Address::findOrFail($request->address_id);
            $create->update($request->all());

            $addresses =  Address::where('customer_id',$this->CURRENT_USER->id)->where('id','!=',$create->id)->get();

            if($create->is_default){
                foreach ($addresses as $address){
                    $address->is_default = false;
                    $address->save();
                }
            }
            \DB::commit();
            $data = AddressResource::collection([$create]);
            return $this->sendResponse($data,'');
        } catch (\Exception $e) {
            //$e->getMessage()
            \DB::rollback();

            return $this->sendError($e->getMessage(),500);
        }
    }
    public function delete_address(Request $request){
        $this->validate($request,[
            'address_id'=>'required',
        ]);
        \DB::beginTransaction();

        try {
            $orders = Order::where('address_id',$request->address_id)->first();
            if($orders){
                return $this->sendError(\Lang::get("lang.address_linked_to_data"),400);
            }
            Address::where('id',$request->address_id)->where('customer_id',$this->CURRENT_USER->id)->delete();
            \DB::commit();
            return $this->sendResponse(null,\Lang::get("lang.done"));
        } catch (\Exception $e) {
            //$e->getMessage()
            \DB::rollback();

            return $this->sendError($e->getMessage(),500);
        }
    }


    public function  orders(Request $request){

        $data = Order::where('customer_id',$this->CURRENT_USER->id)->orderBy('created_at','desc');
       // dd($request->order_status);

        $data->has("order_items");

        if($request->has('order_status')){

            $data->where('status',$request->order_status);
        }
        if($request->has('name')){
            $search = $request->name;
            $data->whereHas('store', function($q) use($search){
                $q->where('name', 'LIKE', '%'.$search.'%');
            });
         //  $data->where('store',$request->order_status);
        }

        $data = $data->paginate(12);

    //  dd($data);
        $result= [
            'order'=>OrderResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]

        ];
        return $this->sendResponse($result,'');

    }

    public function orderDetails (Request $request){
        $data = Order::find($request->order_id);

        $result= [
            'order'=>new OrderResource($data),
        ];
        return $this->sendResponse($result,'');
    }

    public function orderCancel (Request $request){
        $this->validate($request,[
            'order_id'=>'required'
        ]);

        $data = Order::where('id',$request->order_id)
            ->where('customer_id',$this->CURRENT_USER->id)->first();
        if(is_null($data)){
            return $this->sendError(\Lang::get("lang.can_not_cancel_order"),422);
        }
        $data->status = -1;
        $data->save();
        $data = Order::where('id',$request->order_id)->where('customer_id',$this->CURRENT_USER->id)->first();
        $result= [
            'order'=>new OrderResource($data),
        ];
        return $this->sendResponse($result,'');
    }

    public function followStoreAddOrRemove(Request $request)
    {
        $this->validate(
            $request,
            [
                "store_id" =>"required|exists:stores,id",
                //   'customer_id' => 'required|exists:customers,id',
            ]
        );

        $follower = Follower::where("store_id", $request->store_id)
            ->where("customer_id", $this->CURRENT_USER->id)
            ->first();

        if($follower){
            $follower->delete();
            $data=[
                'remove'=>true,
                'isFollow'=>false,
            ];
            return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
        }
        $create = Follower::create([
            "store_id"=>$request->store_id,
            "customer_id"=>$this->CURRENT_USER->id
        ]);
        $create = (array)$create->toArray();
        $create["isFollow"]  =  true;

        // Notification Process :
        $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));

        $messaging = $factory->createMessaging();

        $StoreDetails = Store::find($request->store_id);
        $customersDetails = Customer::find($this->CURRENT_USER->id);

//            $fcmTokens = Store::whereIn('id', [$request->store_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
        $fcmTokens = $StoreDetails->fcm_token ?? '';

        // Check if FCM tokens are found
        if (empty($fcmTokens)) {
            return $this->sendError('No valid FCM tokens found for the store', 400);  // Send response if no tokens found
        }

//                $fcmTokens = Store::whereIn('id', [$request->store_id])
//                    ->whereNotNull('fcm_token')
//                    ->pluck('fcm_token')
//                    ->toArray();
//
//                dd($fcmTokens); // Check if you get valid tokens here

        $notification = Notification::create("New Follower", $customersDetails->username . ' follow you');

        // Add user ID to the data array
        $data = [
            'id' => $customersDetails->id, // Include the user ID
            'username' => $customersDetails->username . ' follow you',
            'type' => 'follow_request'
        ];

        \Notification::send($StoreDetails,
            new \App\Notifications\GeneralNotification(
                $customersDetails->username . ' follow you',
                'follow_request',
                $data
            )
        );

        $message = CloudMessage::withTarget('token', $StoreDetails->fcm_token)->withNotification($notification);
        $messaging->sendMulticast($message, [$StoreDetails->fcm_token]);

        return $this->sendResponse($create, \Lang::get("lang.following_added"));
    }

            public function followAddOrRemove(Request $request)
             {
            // إضافة logging للتشخيص
            \Log::info('Follow API called', [
                'customer_id' => $this->CURRENT_USER->id,
                'store_id' => $request->store_id,
                'timestamp' => now()
            ]);

            $this->validate(
                $request,
                [
                    "store_id" =>"required|exists:stores,id",
                 //   'customer_id' => 'required|exists:customers,id',
                ]
            );

            $follower = Follower::where("store_id", $request->store_id)
                ->where("customer_id", $this->CURRENT_USER->id)
                ->first();

            if($follower){
                $follower->delete();
                \Log::info('Follow removed successfully', [
                    'customer_id' => $this->CURRENT_USER->id,
                    'store_id' => $request->store_id
                ]);
                $data=[
                    'remove'=>true,
                    'isFollow'=>false,
                ];
                return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
            }
                 $create = Follower::create([
                     "store_id" => (int) $request->store_id, // Cast to integer
                     "customer_id" => $this->CURRENT_USER->id
                 ]);
            $create = (array)$create->toArray();
                 $create["store_id"] = (int) $create["store_id"];
            $create["isFollow"]  =  true;

            \Log::info('Follow created successfully', [
                'follower_id' => $create['id'],
                'customer_id' => $this->CURRENT_USER->id,
                'store_id' => $request->store_id
            ]);

            // Notification Process - جعل الإشعارات اختيارية لتجنب فشل العملية الأساسية
            try {
                $StoreDetails = Store::find($request->store_id);
                $customersDetails = Customer::find($this->CURRENT_USER->id);

                if (!$StoreDetails || !$customersDetails) {
                    \Log::warning('Store or Customer not found for notification', [
                        'store_id' => $request->store_id,
                        'customer_id' => $this->CURRENT_USER->id
                    ]);
                    return $this->sendResponse($create, \Lang::get("lang.following_added"));
                }

//            $fcmTokens = Store::whereIn('id', [$request->store_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
                $fcmTokens = $StoreDetails->fcm_token ?? '';

                // ❌ مشكلة رقم 1: هذا الكود يوقف العملية إذا لم يكن هناك FCM token
                // المشكلة الأصلية:
                // if (empty($fcmTokens)) {
                //     return $this->sendError('No valid FCM tokens found for the store', 400);
                // }

                // ✅ الحل: جعل الإشعارات اختيارية
                if (empty($fcmTokens)) {
                    \Log::warning('No FCM token found for store', ['store_id' => $request->store_id]);
                    // لا توقف العملية، فقط سجل تحذير وأكمل بدون إشعار push
                } else {

                    // إرسال Push Notification
                    $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
                    $messaging = $factory->createMessaging();
                    $notification = Notification::create("New Follower", $customersDetails->username . ' follow you');
                    $message = CloudMessage::withTarget('token', $StoreDetails->fcm_token)->withNotification($notification);
                    $messaging->send($message); // استخدم send بدلاً من sendMulticast
                }

                // إرسال إشعار داخل التطبيق
                $data = [
                    'id' => $customersDetails->id,
                    'username' => $customersDetails->username . ' follow you',
                    'type' => 'follow_request'
                ];

                // ❌ مشكلة رقم 2: خطأ في استخدام first() على كائن Customer
                // المشكلة الأصلية:
                // \Notification::send($StoreDetails,
                //     new \App\Notifications\GeneralNotification(
                //         $customersDetails->first()->username . ' follow you', // ❌ خطأ هنا
                //         'follow_request',
                //         $data
                //     )
                // );

                // ✅ الحل الصحيح:
                \Notification::send($StoreDetails,
                    new \App\Notifications\GeneralNotification(
                        $customersDetails->username . ' follow you', // ✅ صحيح
                        'follow_request',
                        $data
                    )
                );

            } catch (\Exception $e) {
                \Log::error('Failed to send follow notification', [
                    'error' => $e->getMessage(),
                    'customer_id' => $this->CURRENT_USER->id,
                    'store_id' => $request->store_id
                ]);
                // لا توقف العملية الأساسية، فقط سجل الخطأ
            }

            return $this->sendResponse($create, \Lang::get("lang.following_added"));
        }



//    public function followAddOrRemove(Request $request)
//    {
//        $this->validate(
//            $request,
//            [
//                "store_id" => "required|exists:stores,id",
//            ]
//        );
//
//        $follower = Follower::where("store_id", $request->store_id)
//            ->where("customer_id", $this->CURRENT_USER->id)
//            ->first();
//
//        if ($follower) {
//            $follower->delete();
//            $data = [
//                'remove' => true,
//                'isFollow' => false,
//            ];
//            return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
//        }
//
//        $create = Follower::create([
//            "store_id" => $request->store_id,
//            "customer_id" => $this->CURRENT_USER->id
//        ]);
//        $create = (array) $create->toArray();
//        $create["isFollow"] = true;
//
//        // Notification Process:
//        try {
//            $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
//            $messaging = $factory->createMessaging();
//
//            $StoreDetails = Store::whereIn('id', [$request->store_id])->get();
//            $customersDetails = Customer::whereIn('id', [$this->CURRENT_USER->id])->get();
//
//            $fcmTokens = Store::whereIn('id', [$request->store_id])
//                ->whereNotNull('fcm_token')
//                ->pluck('fcm_token')
//                ->toArray();
//
//            // Check if FCM tokens are found
//            if (empty($fcmTokens)) {
//                return $this->sendError('No valid FCM tokens found for the store', 400);  // Send response if no tokens found
//            }
//
//            $notification = \Kreait\Firebase\Messaging\Notification::create("New Follower", $customersDetails->first()->username . ' followed you');
//
//            // Prepare data for notification
//            $data = [
//                'id' => $customersDetails->first()->id,
//                'username' => $customersDetails->first()->username . ' followed you',
//                'type' => 'follow_request'
//            ];
//
//            \Notification::send($StoreDetails, new \App\Notifications\GeneralNotification(
//                $customersDetails->first()->username . ' followed you',
//                'follow_request',
//                $data
//            ));
//
//            $message = \Kreait\Firebase\Messaging\CloudMessage::new()->withNotification($notification);
//            $batchSize = 150; // Adjust as needed
//            $chunks = array_chunk($fcmTokens, $batchSize);
//
//            $errors = []; // Collect specific errors here
//
//            foreach ($chunks as $chunk) {
//                try {
//                    $report = $messaging->sendMulticast($message, $chunk);
//
//                    // Check if there are failures and return them as errors
//                    if ($report->hasFailures()) {
//                        foreach ($report->failures()->getItems() as $failure) {
//                            // Collect error details for each failed token
//                            $errors[] = [
//                                'token' => $failure->target()->value(),  // Token that caused the failure
//                                'error' => $failure->error()->getMessage()  // Error message from Firebase
//                            ];
//                        }
//                    }
//                } catch (\Throwable $e) {
//                    // Log the messaging error
//                    return $this->sendError('Error sending notifications: ' . $e->getMessage(), 500);
//                }
//            }
//
//            // If there were any errors during the notification process, return them
//            if (!empty($errors)) {
//                return response()->json([
//                    'status' => false,
//                    'msg' => 'Some notifications failed to send',
//                    'result' => $errors // Return the list of failed tokens and their errors
//                ], 500);
//            }
//
//        } catch (\Throwable $e) {
//            // Return general error if Firebase config or token fetching fails
//            return $this->sendError('Error with Firebase service: ' . $e->getMessage(), 500);
//        }
//
//        return $this->sendResponse($create, \Lang::get("lang.following_added"));
//    }




    public function usersFollowAddOrRemove(Request $request)
        {
            $this->validate(
                $request,
                [
                    "customer_id" =>"required|exists:customers,id",
                 //   'customer_id' => 'required|exists:customers,id',
                ]
            );

            $follower = UsersFollowers::where("followed_customer_id",$request->customer_id)
                ->where("customer_id",$this->CURRENT_USER->id)
                ->first();

            if($follower){
                $follower->delete();
                $data=[
                    'remove'=>true,
                    'isFollow'=>false,
                ];
                return $this->sendResponse($data,\Lang::get("lang.following_deleted"));
            }
            $create = UsersFollowers::create([
                "followed_customer_id"=>$request->customer_id,
                "customer_id"=>$this->CURRENT_USER->id
            ]);
            $create = (array)$create->toArray();
            $create["isFollow"]  =  true;

            // Notification Process :
            $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));

            $messaging = $factory->createMessaging();

            $customersDetails = Customer::whereIn('id', [$request->customer_id])->first();
            
            $MyDetails = Customer::whereIn('id', [$this->CURRENT_USER->id])->first();

            $notification = Notification::create("New Follower", $MyDetails->username . ' follow you');
            $message = CloudMessage::withTarget('token', $customersDetails->fcm_token)->withNotification($notification);
            // Add user ID to the data array
            $data = [
                'id' => $MyDetails->first()->id, // Include the user ID
                'username' => $MyDetails->first()->username . ' follow you',
                'type' => 'follow_request'
            ];

            \Notification::send($customersDetails,
                new \App\Notifications\GeneralNotification(
                    $MyDetails->first()->username . ' follow you',
                    'follow_request',
                    $data
                )
            );
            $messaging->sendMulticast($message, [$customersDetails->fcm_token]);
            return $this->sendResponse($create, \Lang::get("lang.following_added"));
        }



    public function addProductProblem(Request $request){
        $this->validate(
            $request,
            [
                "product_name" =>"required",
                "description" =>"required",
                "store_category_id" =>"required|exists:store_categories,id",
            ]
        );

        $addProduct = ProductRequest::create([
            "product_name" =>$request->product_name,
            "description" =>$request->description,
            "store_category_id" =>$request->store_category_id,
            "customer_id" =>$this->CURRENT_USER->id,
        ]);
        $data=[
            'title' => "طلب منتج جديد ",
            'body' => '',
            'id'=>$addProduct->id,
            'type'=>'product_request',
        ];
        $admin = \App\Models\User::first();
      //  fcmNotification($store,$data);
        \Notification::send($admin,
            new \App\Notifications\GeneralNotification(
                "طلب منتج جديد ",
                'qr',
                $data
            ));
        return $this->sendResponse($addProduct,\Lang::get("lang.order_sent_successfully"));
    }

    public function follows(Request $request){

        $customers = optional($this->CURRENT_USER)->follower();

        if ($request->name){
            $customers->where('username',"LIKE",'%'.$request->name."%");
        }

        $customers =  $customers->paginate(10);


        $result = [
            'customers'=>CustomerResource::collection($customers),
            "pagination"=>[
                "i_total_objects"=>$customers->count(),
                "i_items_on_page"=> $customers->count(),
                "i_per_pages"=>$customers->perPage(),
                "i_current_page"=>$customers->currentPage() ,
                "i_total_pages"=> $customers->total()
            ]
        ];

        return $this->sendResponse($result,'');

    }

    public function likes(Request $request , $post_id){

        $customers = optional($this->CURRENT_USER)->follower();

        if ($request->name){
            $customers->where('username',"LIKE",'%'.$request->name."%");
        }

        $customers =  $customers->paginate(10);


        $result = [
            'customers'=>CustomerResource::collection($customers),
            "pagination"=>[
                "i_total_objects"=>$customers->count(),
                "i_items_on_page"=> $customers->count(),
                "i_per_pages"=>$customers->perPage(),
                "i_current_page"=>$customers->currentPage() ,
                "i_total_pages"=> $customers->total()
            ]
        ];

        return $this->sendResponse($result,'');

    }


}
