/*! UIkit 3.0.0-beta.39 | http://www.getuikit.com | (c) 2014 - 2017 YOOtheme | MIT License */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("uikit",e):t.UIkit=e()}(this,function(){"use strict";var t=2,e="setImmediate"in window?setImmediate:setTimeout;function i(e){this.state=t,this.value=void 0,this.deferred=[];var i=this;try{e(function(t){i.resolve(t)},function(t){i.reject(t)})}catch(t){i.reject(t)}}i.reject=function(t){return new i(function(e,i){i(t)})},i.resolve=function(t){return new i(function(e,i){e(t)})},i.all=function(t){return new i(function(e,n){var o=0,s=[];function r(i){return function(n){s[i]=n,(o+=1)===t.length&&e(s)}}0===t.length&&e(s);for(var a=0;a<t.length;a+=1)i.resolve(t[a]).then(r(a),n)})},i.race=function(t){return new i(function(e,n){for(var o=0;o<t.length;o+=1)i.resolve(t[o]).then(e,n)})};var n=i.prototype;n.resolve=function(e){var i=this;if(i.state===t){if(e===i)throw new TypeError("Promise settled with itself.");var n=!1;try{var o=e&&e.then;if(null!==e&&ze(e)&&He(o))return void o.call(e,function(t){n||i.resolve(t),n=!0},function(t){n||i.reject(t),n=!0})}catch(t){return void(n||i.reject(t))}i.state=0,i.value=e,i.notify()}},n.reject=function(e){var i=this;if(i.state===t){if(e===i)throw new TypeError("Promise settled with itself.");i.state=1,i.value=e,i.notify()}},n.notify=function(){var i=this;e(function(){if(i.state!==t)for(;i.deferred.length;){var e=i.deferred.shift(),n=e[0],o=e[1],s=e[2],r=e[3];try{0===i.state?He(n)?s(n.call(void 0,i.value)):s(i.value):1===i.state&&(He(o)?s(o.call(void 0,i.value)):r(i.value))}catch(t){r(t)}}})},n.then=function(t,e){var n=this;return new i(function(i,o){n.deferred.push([t,e,i,o]),n.notify()})},n.catch=function(t){return this.then(void 0,t)};var o=window,s=document,r=s.documentElement,a=o.MutationObserver,l=o.requestAnimationFrame,h="ontouchstart"in o,u=o.PointerEvent,c=h||o.DocumentTouch&&s instanceof DocumentTouch||navigator.maxTouchPoints,d=c?"mousedown "+(h?"touchstart":"pointerdown"):"mousedown",f=c?"mousemove "+(h?"touchmove":"pointermove"):"mousemove",p=c?"mouseup "+(h?"touchend":"pointerup"):"mouseup",m=c&&u?"pointerenter":"mouseenter",g=c&&u?"pointerleave":"mouseleave";var v,w={};function b(t,e,i){if(ze(e))for(var n in e)b(t,n,e[n]);else{if(qe(i))return(t=ne(t))&&t.getAttribute(e);oe(t).forEach(function(t){He(i)&&(i=i.call(t,b(t,e))),null===i?x(t,e):t.setAttribute(e,i)})}}function y(t,e){return oe(t).some(function(t){return t.hasAttribute(e)})}function x(t,e){t=oe(t),e.split(" ").forEach(function(e){return t.forEach(function(t){return t.removeAttribute(e)})})}function k(t,e,i,n){b(t,e,function(t){return t?t.replace(i,n):t})}function $(t,e){for(var i=0,n=[e,"data-"+e];i<n.length;i++)if(y(t,n[i]))return b(t,n[i])}function I(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];A(t,e,"add")}function T(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];A(t,e,"remove")}function C(t,e){k(t,"class",new RegExp("(^|\\s)"+e+"(?!\\S)","g"),"")}function E(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];e[0]&&T(t,e[0]),e[1]&&I(t,e[1])}function _(t,e){return w.ClassList&&oe(t).some(function(t){return t.classList.contains(e)})}function S(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];if(w.ClassList&&e.length){var n=Ve((e=N(e))[e.length-1])?[]:e.pop();e=e.filter(Boolean),oe(t).forEach(function(t){for(var i=t.classList,o=0;o<e.length;o++)w.Force?i.toggle.apply(i,[e[o]].concat(n)):i[(qe(n)?!i.contains(e[o]):n)?"add":"remove"](e[o])})}}function A(t,e,i){e=N(e).filter(Boolean),w.ClassList&&e.length&&oe(t).forEach(function(t){var n=t.classList;w.Multiple?n[i].apply(n,e):e.forEach(function(t){return n[i](t)})})}function N(t){return t.reduce(function(t,e){return t.concat.call(t,Ve(e)&&Oe(e," ")?e.trim().split(" "):e)},[])}(v=s.createElement("_").classList)&&(v.add("a","b"),v.toggle("c",!1),w.Multiple=v.contains("b"),w.Force=!v.contains("c"),w.ClassList=!0),v=null;var D={"animation-iteration-count":!0,"column-count":!0,"fill-opacity":!0,"flex-grow":!0,"flex-shrink":!0,"font-weight":!0,"line-height":!0,opacity:!0,order:!0,orphans:!0,widows:!0,"z-index":!0,zoom:!0};function M(t,e,i){return oe(t).map(function(t){if(Ve(e)){if(e=W(e),qe(i))return O(t,e);i||0===i?t.style[e]=Ye(i)&&!D[e]?i+"px":i:t.style.removeProperty(e)}else{if(Pe(e)){var n=B(t);return e.reduce(function(t,e){return t[e]=n[W(e)],t},{})}ze(e)&&ni(e,function(e,i){return M(t,i,e)})}return t})[0]}function B(t,e){return(t=ne(t)).ownerDocument.defaultView.getComputedStyle(t,e)}function O(t,e,i){return B(t,i)[e]}var P={};function H(t){if(!(t in P)){var e=It(r,s.createElement("div"));I(e,"var-"+t);try{P[t]=O(e,"content",":before").replace(/^["'](.*)["']$/,"$1"),P[t]=JSON.parse(P[t])}catch(t){}r.removeChild(e)}return P[t]}var z={};function W(t){var e=z[t];return e||(e=z[t]=function(t){if((t=ke(t))in j)return t;var e,i=L.length;for(;i--;)if((e="-"+L[i]+"-"+t)in j)return e}(t)||t),e}var L=["webkit","moz","ms"],j=s.createElement("_").style;var F={width:["x","left","right"],height:["y","top","bottom"]};function V(t,e,i,n,o,s,r,a){i=K(i),n=K(n);var l={element:i,target:n};if(!t||!e)return l;var h=Y(t),u=Y(e),c=u;return Q(c,i,h,-1),Q(c,n,u,1),o=tt(o,h.width,h.height),s=tt(s,u.width,u.height),o.x+=s.x,o.y+=s.y,c.left+=o.x,c.top+=o.y,a=Y(a||Z(t)),r&&ni(F,function(t,e){var s=t[0],d=t[1],f=t[2];if(!0===r||Oe(r,s)){var p=i[s]===d?-h[e]:i[s]===f?h[e]:0,m=n[s]===d?u[e]:n[s]===f?-u[e]:0;if(c[d]<a[d]||c[d]+h[e]>a[f]){var g=h[e]/2,v="center"===n[s]?-u[e]/2:0;"center"===i[s]&&(w(g,v)||w(-g,-v))||w(p,m)}}function w(t,i){var n=c[d]+t+i-2*o[s];if(n>=a[d]&&n+h[e]<=a[f])return c[d]=n,["element","target"].forEach(function(i){l[i][s]=t?l[i][s]===F[e][1]?F[e][2]:F[e][1]:l[i][s]}),!0}}),R(t,c),l}function R(t,e){if(t=ne(t),!e)return Y(t);var i=R(t),n=M(t,"position");["left","top"].forEach(function(o){if(o in e){var s=M(t,o);t.style[o]=e[o]-i[o]+Je("absolute"===n&&"auto"===s?q(t)[o]:s)+"px"}})}function Y(t){var e=Z(t=ne(t)),i=e.pageYOffset,n=e.pageXOffset;if(Le(t)){var o=t.innerHeight,s=t.innerWidth;return{top:i,left:n,height:o,width:s,bottom:i+o,right:n+s}}var r=!1;bt(t)||(r=t.style.display,t.style.display="block");var a=t.getBoundingClientRect();return!1!==r&&(t.style.display=r),{height:a.height,width:a.width,top:a.top+i,left:a.left+n,bottom:a.bottom+i,right:a.right+n}}function q(t){var e=function(t){var e=ne(t).offsetParent;for(;e&&"static"===M(e,"position");)e=e.offsetParent;return e||nt(t)}(t=ne(t)),i=e===nt(t)?{top:0,left:0}:R(e),n=["top","left"].reduce(function(n,o){var s=Ce(o);return n[o]-=i[o]+(Je(M(t,"margin"+s))||0)+(Je(M(e,"border"+s+"Width"))||0),n},R(t));return{top:n.top,left:n.left}}var U=J("height"),X=J("width");function J(t){var e=Ce(t);return function(i,n){if(i=ne(i),qe(n)){if(Le(i))return i["inner"+e];if(je(i)){var o=i.documentElement;return Math.max(o.offsetHeight,o.scrollHeight)}return n="auto"===(n=M(i,t))?i["offset"+e]:Je(n)||0,G(t,i,n)}M(i,t,n||0===n?G(t,i,n)+"px":"")}}function G(t,e,i){return"border-box"===M(e,"boxSizing")?F[t].slice(1).map(Ce).reduce(function(t,i){return t-Je(M(e,"padding"+i))-Je(M(e,"border"+i+"Width"))},i):i}function Z(t){return Le(t)?t:it(t).defaultView}function Q(t,e,i,n){ni(F,function(o,s){var r=o[0],a=o[1],l=o[2];e[r]===l?t[a]+=i[s]*n:"center"===e[r]&&(t[a]+=i[s]*n/2)})}function K(t){var e=/left|center|right/,i=/top|center|bottom/;return 1===(t=(t||"").split(" ")).length&&(t=e.test(t[0])?t.concat(["center"]):i.test(t[0])?["center"].concat(t):["center","center"]),{x:e.test(t[0])?t[0]:"center",y:i.test(t[1])?t[1]:"center"}}function tt(t,e,i){var n=(t||"").split(" "),o=n[0],s=n[1];return{x:o?Je(o)*(Ne(o,"%")?e/100:1):0,y:s?Je(s)*(Ne(s,"%")?i/100:1):0}}function et(t){switch(t){case"left":return"right";case"right":return"left";case"top":return"bottom";case"bottom":return"top";default:return t}}function it(t){return ne(t).ownerDocument}function nt(t){return it(t).documentElement}var ot="rtl"===b(r,"dir");function st(){return"complete"===s.readyState||"loading"!==s.readyState&&!r.doScroll}function rt(t){if(st())t();else var e=function(){i(),n(),t()},i=ae(s,"DOMContentLoaded",e),n=ae(o,"load",e)}function at(t,e,i,n){return void 0===i&&(i=400),void 0===n&&(n="linear"),be.all(oe(t).map(function(t){return new be(function(o,s){for(var r in e){var a=M(t,r);""===a&&M(t,r,a)}var l=setTimeout(function(){return ue(t,"transitionend")},i);he(t,"transitionend transitioncanceled",function(e){var i=e.type;clearTimeout(l),T(t,"uk-transition"),M(t,{"transition-property":"","transition-duration":"","transition-timing-function":""}),"transitioncanceled"===i?s():o()},!1,function(e){var i=e.target;return t===i}),I(t,"uk-transition"),M(t,ii({"transition-property":Object.keys(e).map(W).join(","),"transition-duration":i+"ms","transition-timing-function":n},e))})}))}var lt={start:at,stop:function(t){return ue(t,"transitionend"),be.resolve()},cancel:function(t){ue(t,"transitioncanceled")},inProgress:function(t){return _(t,"uk-transition")}},ht="uk-animation-",ut="uk-cancel-animation";function ct(t,e,i,n,o){var s=arguments;return void 0===i&&(i=200),be.all(oe(t).map(function(t){return new be(function(r,a){if(_(t,ut))requestAnimationFrame(function(){return be.resolve().then(function(){return ct.apply(void 0,s).then(r,a)})});else{var l=e+" "+ht+(o?"leave":"enter");Se(e,ht)&&(n&&(l+=" uk-transform-origin-"+n),o&&(l+=" "+ht+"reverse")),h(),he(t,"animationend animationcancel",function(e){var i=!1;"animationcancel"===e.type?(a(),h()):(r(),be.resolve().then(function(){i=!0,h()})),requestAnimationFrame(function(){i||(I(t,ut),requestAnimationFrame(function(){return T(t,ut)}))})},!1,function(e){var i=e.target;return t===i}),M(t,"animationDuration",i+"ms"),I(t,l)}function h(){M(t,"animationDuration",""),C(t,ht+"\\S*")}})}))}var dt=new RegExp(ht+"(enter|leave)"),ft={in:function(t,e,i,n){return ct(t,e,i,n,!1)},out:function(t,e,i,n){return ct(t,e,i,n,!0)},inProgress:function(t){return dt.test(b(t,"class"))},cancel:function(t){ue(t,"animationcancel")}};function pt(t,e,i){return void 0===e&&(e=0),void 0===i&&(i=0),ri(ne(t).getBoundingClientRect(),{top:e,left:i,bottom:e+U(o),right:i+X(o)})}function mt(t,e,i,n){void 0===i&&(i=0),void 0===n&&(n=!1);var o=(e=oe(e)).length;return t=Ye(t)?Xe(t):"next"===t?i+1:"previous"===t?i-1:Ot(e,t),n?oi(t,0,o-1):(t%=o)<0?t+o:t}var gt={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};function vt(t){return gt[ne(t).tagName.toLowerCase()]}var wt={ratio:function(t,e,i){var n,o="width"===e?"height":"width";return(n={})[o]=Math.round(i*t[o]/t[e]),n[e]=i,n},contain:function(t,e){var i=this;return ni(t=ii({},t),function(n,o){return t=t[o]>e[o]?i.ratio(t,o,e[o]):t}),t},cover:function(t,e){var i=this;return ni(t=this.contain(t,e),function(n,o){return t=t[o]<e[o]?i.ratio(t,o,e[o]):t}),t}};function bt(t){return oe(t).some(function(t){return t.offsetHeight||t.getBoundingClientRect().height})}var yt="input,select,textarea,button";function xt(t){return oe(t).some(function(t){return Gt(t,yt)})}function kt(t){return(t=ne(t)).innerHTML="",t}function $t(t,e){return t=ne(t),qe(e)?t.innerHTML:It(t.hasChildNodes()?kt(t):t,e)}function It(t,e){return t=ne(t),Et(e,function(e){return t.appendChild(e)})}function Tt(t,e){return t=ne(t),Et(e,function(e){return t.parentNode.insertBefore(e,t)})}function Ct(t,e){return t=ne(t),Et(e,function(e){return t.nextSibling?Tt(t.nextSibling,e):It(t.parentNode,e)})}function Et(t,e){return(t=Ve(t)?Bt(t):t)?"length"in t?oe(t).map(e):e(t):null}function _t(t){oe(t).map(function(t){return t.parentNode&&t.parentNode.removeChild(t)})}function St(t,e){for(e=ne(Tt(t,e));e.firstChild;)e=e.firstChild;return It(e,t),e}function At(t,e){return oe(oe(t).map(function(t){return t.hasChildNodes?St(oe(t.childNodes),e):It(t,e)}))}function Nt(t){oe(t).map(function(t){return t.parentNode}).filter(function(t,e,i){return i.indexOf(t)===e}).forEach(function(t){Tt(t,t.childNodes),_t(t)})}var Dt=/^\s*<(\w+|!)[^>]*>/,Mt=/^<(\w+)\s*\/?>(?:<\/\1>)?$/;function Bt(t){var e;if(e=Mt.exec(t))return s.createElement(e[1]);var i=s.createElement("div");return Dt.test(t)?i.insertAdjacentHTML("beforeend",t.trim()):i.textContent=t,i.childNodes.length>1?oe(i.childNodes):i.firstChild}function Ot(t,e){return e?oe(t).indexOf(ne(e)):oe((t=ne(t))&&t.parentNode.children).indexOf(t)}var Pt=Array.prototype;function Ht(t,e){return Ve(t)?Wt(t)?ne(Bt(t)):ne(Ft(t,e,"querySelector")):ne(t)}function zt(t,e){return Ve(t)?Wt(t)?oe(Bt(t)):oe(Ft(t,e,"querySelectorAll")):oe(t)}function Wt(t){return"<"===t[0]||t.match(/^\s*</)}function Lt(t,e){return Ht(t,Ut(t)?e:s)}function jt(t,e){return zt(t,Ut(t)?e:s)}function Ft(t,e,i){if(void 0===e&&(e=s),!t||!Ve(t))return null;var n;Ut(t=t.replace(qt,"$1 *"))&&(n=[],t=t.split(",").map(function(t,i){var o=e;if("!"===(t=t.trim())[0]){var s=t.substr(1).trim().split(" ");o=Qt(e.parentNode,s[0]),t=s.slice(1).join(" ")}return o?(o.id||(o.id="uk-"+Date.now()+i,n.push(function(){return x(o,"id")})),"#"+re(o.id)+" "+t):null}).filter(Boolean).join(","),e=s);try{return e[i](t)}catch(t){return null}finally{n&&n.forEach(function(t){return t()})}}function Vt(t,e){return zt(t).filter(function(t){return Gt(t,e)})}function Rt(t,e){return Ve(e)?Gt(t,e)||Qt(t,e):t===e||ne(e).contains(ne(t))}var Yt=/(^|,)\s*[!>+~]/,qt=/([!>+~])(?=\s+[!>+~]|\s*$)/g;function Ut(t){return Ve(t)&&t.match(Yt)}var Xt=Element.prototype,Jt=Xt.matches||Xt.webkitMatchesSelector||Xt.msMatchesSelector;function Gt(t,e){return oe(t).some(function(t){return Jt.call(t,e)})}var Zt=Xt.closest||function(t){var e=this;do{if(Gt(e,t))return e;e=e.parentNode}while(e&&1===e.nodeType)};function Qt(t,e){return Se(e,">")&&(e=e.slice(1)),ee(t)?t.parentNode&&Zt.call(t,e):oe(t).map(function(t){return t.parentNode&&Zt.call(t,e)}).filter(Boolean)}function Kt(t,e){for(var i=[],n=ne(t).parentNode;n&&1===n.nodeType;)Gt(n,e)&&i.push(n),n=n.parentNode;return i}function te(t){return ze(t)&&!!t.jquery}function ee(t){return t instanceof Node||ze(t)&&1===t.nodeType}function ie(t){return t instanceof NodeList||t instanceof HTMLCollection}function ne(t){return ee(t)||Le(t)||je(t)?t:ie(t)||te(t)?t[0]:Pe(t)?ne(t[0]):null}function oe(t){return ee(t)?[t]:ie(t)?Pt.slice.call(t):Pe(t)?t.map(ne).filter(Boolean):te(t)?t.toArray():[]}var se=o.CSS&&CSS.escape||function(t){return t.replace(/([^\x7f-\uFFFF\w-])/g,function(t){return"\\"+t})};function re(t){return Ve(t)?se.call(null,t):""}function ae(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i,n=de(t),o=n[0],s=n[1],r=n[2],a=n[3],l=n[4];return o=pe(o),r&&(a=function(t,e,i){var n=this;return function(o){var s=o.target,r=">"===e[0]?zt(e,t).reverse().filter(function(t){return Rt(s,t)})[0]:Qt(s,e);r&&(o.delegate=t,o.current=r,i.call(n,o))}}(o,r,a)),a.length>1&&(i=a,a=function(t){return Pe(t.detail)?i.apply(i,[t].concat(t.detail)):i(t)}),s.split(" ").forEach(function(t){return o&&o.addEventListener(t,a,l)}),function(){return le(o,s,a,l)}}function le(t,e,i,n){void 0===n&&(n=!1),(t=pe(t))&&e.split(" ").forEach(function(e){return t.removeEventListener(e,i,n)})}function he(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i=de(t),n=i[0],o=i[1],s=i[2],r=i[3],a=i[4],l=i[5],h=ae(n,o,s,function(t){var e=!l||l(t);e&&(h(),r(t,e))},a);return h}function ue(t,e,i){return me(t).reduce(function(t,n){return t&&n.dispatchEvent(ce(e,!0,!0,i))},!0)}function ce(t,e,i,n){if(void 0===e&&(e=!0),void 0===i&&(i=!1),Ve(t)){var o=s.createEvent("CustomEvent");o.initCustomEvent(t,e,i,n),t=o}return t}function de(t){return Ve(t[0])&&(t[0]=Ht(t[0])),He(t[2])&&t.splice(2,0,!1),t}function fe(t){return"EventTarget"in o?t instanceof EventTarget:t&&"addEventListener"in t}function pe(t){return fe(t)?t:ne(t)}function me(t){return fe(t)?[t]:Pe(t)?t.map(pe).filter(Boolean):oe(t)}function ge(t,e){return function(i){var n=arguments.length;return n?n>1?t.apply(e,arguments):t.call(e,i):t.call(e)}}var ve=Object.prototype.hasOwnProperty;function we(t,e){return ve.call(t,e)}var be="Promise"in window?window.Promise:i,ye=/(?:^|[-_\/])(\w)/g;var xe=/([a-z\d])([A-Z])/g;function ke(t){return t.replace(xe,"$1-$2").toLowerCase()}var $e=/-(\w)/g;function Ie(t){return t.replace($e,Te)}function Te(t,e){return e?e.toUpperCase():""}function Ce(t){return t.length?Te(0,t.charAt(0))+t.slice(1):""}var Ee=String.prototype,_e=Ee.startsWith||function(t){return 0===this.lastIndexOf(t,0)};function Se(t,e){return _e.call(t,e)}var Ae=Ee.endsWith||function(t){return this.substr(-t.length)===t};function Ne(t,e){return Ae.call(t,e)}var De=function(t){return~this.indexOf(t)},Me=Ee.includes||De,Be=Array.prototype.includes||De;function Oe(t,e){return t&&(Ve(t)?Me:Be).call(t,e)}var Pe=Array.isArray;function He(t){return"function"==typeof t}function ze(t){return null!==t&&"object"==typeof t}function We(t){return ze(t)&&Object.getPrototypeOf(t)===Object.prototype}function Le(t){return ze(t)&&t===t.window}function je(t){return ze(t)&&9===t.nodeType}function Fe(t){return"boolean"==typeof t}function Ve(t){return"string"==typeof t}function Re(t){return"number"==typeof t}function Ye(t){return Re(t)||Ve(t)&&!isNaN(t-parseFloat(t))}function qe(t){return void 0===t}function Ue(t){return Fe(t)?t:"true"===t||"1"===t||""===t||"false"!==t&&"0"!==t&&t}function Xe(t){var e=Number(t);return!isNaN(e)&&e}function Je(t){return parseFloat(t)||0}function Ge(t){return Pe(t)?t:Ve(t)?t.split(/,(?![^(]*\))/).map(function(t){return Ye(t)?Xe(t):Ue(t.trim())}):[t]}var Ze={};function Qe(t){if(Ve(t))if("@"===t[0]){var e="media-"+t.substr(1);t=Ze[e]||(Ze[e]=Je(H(e)))}else if(isNaN(t))return t;return!(!t||isNaN(t))&&"(min-width: "+t+"px)"}function Ke(t,e,i){return t===Boolean?Ue(e):t===Number?Xe(e):"query"===t?Lt(e,i):"list"===t?Ge(e):"media"===t?Qe(e):t?t(e):e}function ti(t){return t?Ne(t,"ms")?Je(t):1e3*Je(t):0}function ei(t,e,i){return t.replace(new RegExp(e+"|"+i,"mg"),function(t){return t===e?i:e})}var ii=Object.assign||function(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];t=Object(t);for(var n=0;n<e.length;n++){var o=e[n];if(null!==o)for(var s in o)we(o,s)&&(t[s]=o[s])}return t};function ni(t,e){for(var i in t)if(!1===e.call(t[i],t[i],i))break}function oi(t,e,i){return void 0===e&&(e=0),void 0===i&&(i=1),Math.min(Math.max(t,e),i)}function si(){}function ri(t,e){return t.left<=e.right&&e.left<=t.right&&t.top<=e.bottom&&e.top<=t.bottom}function ai(t,e){return ri({top:t.y,bottom:t.y,left:t.x,right:t.x},e)}function li(t,e){return new be(function(i,n){var o=ii({data:null,method:"GET",headers:{},xhr:new XMLHttpRequest,beforeSend:si,responseType:""},e),s=o.xhr;for(var r in o.beforeSend(o),o)if(r in s)try{s[r]=o[r]}catch(t){}for(var a in s.open(o.method.toUpperCase(),t),o.headers)s.setRequestHeader(a,o.headers[a]);ae(s,"load",function(){0===s.status||s.status>=200&&s.status<300||304===s.status?i(s):n(ii(Error(s.statusText),{xhr:s,status:s.status}))}),ae(s,"error",function(){return n(ii(Error("Network Error"),{xhr:s}))}),ae(s,"timeout",function(){return n(ii(Error("Network Timeout"),{xhr:s}))}),s.send(o.data)})}var hi={reads:[],writes:[],read:function(t){return this.reads.push(t),ui(),t},write:function(t){return this.writes.push(t),ui(),t},clear:function(t){return di(this.reads,t)||di(this.writes,t)},flush:function(){ci(this.reads),ci(this.writes.splice(0,this.writes.length)),this.scheduled=!1,(this.reads.length||this.writes.length)&&ui()}};function ui(){hi.scheduled||(hi.scheduled=!0,l(hi.flush.bind(hi)))}function ci(t){for(var e;e=t.shift();)e()}function di(t,e){var i=t.indexOf(e);return!!~i&&!!t.splice(i,1)}function fi(){}function pi(t,e){return(e.y-t.y)/(e.x-t.x)}fi.prototype={positions:[],position:null,init:function(){var t=this;this.positions=[],this.position=null;var e=!1;this.unbind=ae(s,"mousemove",function(i){e||(setTimeout(function(){var n=Date.now(),o=t.positions.length;o&&n-t.positions[o-1].time>100&&t.positions.splice(0,o),t.positions.push({time:n,x:i.pageX,y:i.pageY}),t.positions.length>5&&t.positions.shift(),e=!1},5),e=!0)})},cancel:function(){this.unbind&&this.unbind()},movesTo:function(t){if(this.positions.length<2)return!1;var e=R(t),i=this.positions[this.positions.length-1],n=this.positions[0];if(e.left<=i.x&&i.x<=e.right&&e.top<=i.y&&i.y<=e.bottom)return!1;var o=[[{x:e.left,y:e.top},{x:e.right,y:e.bottom}],[{x:e.right,y:e.top},{x:e.left,y:e.bottom}]];return e.right<=i.x||(e.left>=i.x?(o[0].reverse(),o[1].reverse()):e.bottom<=i.y?o[0].reverse():e.top>=i.y&&o[1].reverse()),!!o.reduce(function(t,e){return t+(pi(n,e[0])<pi(i,e[0])&&pi(n,e[1])>pi(i,e[1]))},0)}};var mi={};mi.args=mi.events=mi.init=mi.created=mi.beforeConnect=mi.connected=mi.ready=mi.beforeDisconnect=mi.disconnected=mi.destroy=function(t,e){return t=t&&!Pe(t)?[t]:t,e?t?t.concat(e):Pe(e)?e:[e]:t},mi.update=function(t,e){return mi.args(t,He(e)?{read:e}:e)},mi.props=function(t,e){return Pe(e)&&(e=e.reduce(function(t,e){return t[e]=String,t},{})),mi.methods(t,e)},mi.computed=mi.defaults=mi.methods=function(t,e){return e?t?ii({},t,e):e:t};var gi=function(t,e){return qe(e)?t:e};function vi(t,e){var i,n={};if(e.mixins)for(var o=0,s=e.mixins.length;o<s;o++)t=vi(t,e.mixins[o]);for(i in t)r(i);for(i in e)we(t,i)||r(i);function r(i){n[i]=(mi[i]||gi)(t[i],e[i])}return n}var wi=0,bi=function(t){this.id=++wi,this.el=ne(t)};function yi(t,e){try{t.contentWindow.postMessage(JSON.stringify(ii({event:"command"},e)),"*")}catch(t){}}bi.prototype.isVideo=function(){return this.isYoutube()||this.isVimeo()||this.isHTML5()},bi.prototype.isHTML5=function(){return"VIDEO"===this.el.tagName},bi.prototype.isIFrame=function(){return"IFRAME"===this.el.tagName},bi.prototype.isYoutube=function(){return this.isIFrame()&&!!this.el.src.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)},bi.prototype.isVimeo=function(){return this.isIFrame()&&!!this.el.src.match(/vimeo\.com\/video\/.*/)},bi.prototype.enableApi=function(){var t=this;if(this.ready)return this.ready;var e,n=this.isYoutube(),s=this.isVimeo();return n||s?this.ready=new i(function(r){var a;he(t.el,"load",function(){if(n){var i=function(){return yi(t.el,{event:"listening",id:t.id})};e=setInterval(i,100),i()}}),(a=function(e){return n&&e.id===t.id&&"onReady"===e.event||s&&Number(e.player_id)===t.id},new i(function(t){he(o,"message",function(e,i){return t(i)},!1,function(t){var e=t.data;if(e&&Ve(e)){try{e=JSON.parse(e)}catch(t){return}return e&&a(e)}})})).then(function(){r(),e&&clearInterval(e)}),b(t.el,"src",t.el.src+(Oe(t.el.src,"?")?"&":"?")+(n?"enablejsapi=1":"api=1&player_id="+wi))}):i.resolve()},bi.prototype.play=function(){var t=this;if(this.isVideo())if(this.isIFrame())this.enableApi().then(function(){return yi(t.el,{func:"playVideo",method:"play"})});else if(this.isHTML5())try{this.el.play()}catch(t){}},bi.prototype.pause=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return yi(t.el,{func:"pauseVideo",method:"pause"})}):this.isHTML5()&&this.el.pause())},bi.prototype.mute=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return yi(t.el,{func:"mute",method:"setVolume",value:0})}):this.isHTML5()&&(this.el.muted=!0,b(this.el,"muted","")))};var xi,ki,$i,Ii,Ti={};function Ci(){xi&&clearTimeout(xi),ki&&clearTimeout(ki),$i&&clearTimeout($i),xi=ki=$i=null,Ti={}}rt(function(){ae(s,"click",function(){return Ii=!0},!0),ae(s,d,function(t){var e=t.target,i=Si(t),n=i.x,o=i.y,s=Date.now(),r=Ai(t.type);Ti.type&&Ti.type!==r||(Ti.el="tagName"in e?e:e.parentNode,xi&&clearTimeout(xi),Ti.x1=n,Ti.y1=o,Ti.last&&s-Ti.last<=250&&(Ti={}),Ti.type=r,Ti.last=s,Ii=t.button>0)}),ae(s,f,function(t){var e=Si(t),i=e.x,n=e.y;Ti.x2=i,Ti.y2=n}),ae(s,p,function(t){var e=t.type,i=t.target;Ti.type===Ai(e)&&(Ti.x2&&Math.abs(Ti.x1-Ti.x2)>30||Ti.y2&&Math.abs(Ti.y1-Ti.y2)>30?ki=setTimeout(function(){var t,e,i,n,o;Ti.el&&(ue(Ti.el,"swipe"),ue(Ti.el,"swipe"+(e=(t=Ti).x1,i=t.x2,n=t.y1,o=t.y2,Math.abs(e-i)>=Math.abs(n-o)?e-i>0?"Left":"Right":n-o>0?"Up":"Down"))),Ti={}}):"last"in Ti?($i=setTimeout(function(){return ue(Ti.el,"tap")}),Ti.el&&"mouseup"!==e&&Rt(i,Ti.el)&&(xi=setTimeout(function(){xi=null,Ti.el&&!Ii&&ue(Ti.el,"click"),Ti={}},350))):Ti={})}),ae(s,"touchcancel",Ci),ae(o,"scroll",Ci)});var Ei=!1;function _i(t){return Ei||"touch"===t.pointerType}function Si(t){var e=t.touches,i=t.changedTouches,n=e&&e[0]||i&&i[0]||t;return{x:n.pageX,y:n.pageY}}function Ai(t){return t.slice(0,5)}ae(s,"touchstart",function(){return Ei=!0},!0),ae(s,"click",function(){Ei=!1}),ae(s,"touchcancel",function(){return Ei=!1},!0);var Ni=Object.freeze({bind:ge,hasOwn:we,Promise:be,Deferred:function(){var t=this;this.promise=new be(function(e,i){t.reject=i,t.resolve=e})},classify:function(t){return t.replace(ye,function(t,e){return e?e.toUpperCase():""})},hyphenate:ke,camelize:Ie,ucfirst:Ce,startsWith:Se,endsWith:Ne,includes:Oe,isArray:Pe,isFunction:He,isObject:ze,isPlainObject:We,isWindow:Le,isDocument:je,isBoolean:Fe,isString:Ve,isNumber:Re,isNumeric:Ye,isUndefined:qe,toBoolean:Ue,toNumber:Xe,toFloat:Je,toList:Ge,toMedia:Qe,coerce:Ke,toMs:ti,swap:ei,assign:ii,each:ni,sortBy:function(t,e){return t.sort(function(t,i){return t[e]>i[e]?1:i[e]>t[e]?-1:0})},clamp:oi,noop:si,intersectRect:ri,pointInRect:ai,ajax:li,$:Ht,$$:zt,query:Lt,queryAll:jt,filter:Vt,within:Rt,matches:Gt,closest:Qt,parents:Kt,isJQuery:te,toNode:ne,toNodes:oe,escape:re,attr:b,hasAttr:y,removeAttr:x,filterAttr:k,data:$,isRtl:ot,isReady:st,ready:rt,transition:at,Transition:lt,animate:ct,Animation:ft,isInView:pt,scrolledOver:function(t){var e=(t=ne(t)).offsetHeight,i=function(t){var e=0;do{e+=t.offsetTop}while(t=t.offsetParent);return e}(t),n=U(o),r=n+Math.min(0,i-n),a=Math.max(0,n-(U(s)-(i+e)));return oi((r+o.pageYOffset-i)/((r+(e-(a<n?a:0)))/100)/100)},getIndex:mt,isVoidElement:vt,Dimensions:wt,preventClick:function(){var t=setTimeout(he(s,"click",function(e){e.preventDefault(),e.stopImmediatePropagation(),clearTimeout(t)},!0))},isVisible:bt,selInput:yt,isInput:xt,empty:kt,html:$t,prepend:function(t,e){return(t=ne(t)).hasChildNodes()?Et(e,function(e){return t.insertBefore(e,t.firstChild)}):It(t,e)},append:It,before:Tt,after:Ct,remove:_t,wrapAll:St,wrapInner:At,unwrap:Nt,fragment:Bt,index:Ot,css:M,getStyles:B,getStyle:O,getCssVar:H,propName:W,addClass:I,removeClass:T,removeClasses:C,replaceClass:E,hasClass:_,toggleClass:S,win:o,doc:s,docEl:r,Observer:a,requestAnimationFrame:l,hasTouch:c,pointerDown:d,pointerMove:f,pointerUp:p,pointerEnter:m,pointerLeave:g,getImage:function(t){return new i(function(e,i){var n=new Image;n.onerror=i,n.onload=function(){return e(n)},n.src=t})},supports:w,on:ae,off:le,once:he,trigger:ue,createEvent:ce,toEventTargets:me,fastdom:hi,MouseTracker:fi,mergeOptions:vi,Player:bi,positionAt:V,offset:R,position:q,height:U,width:X,flipPosition:et,isTouch:_i,getPos:Si});function Di(t){return!(!Se(t,"uk-")&&!Se(t,"data-uk-"))&&Ie(t.replace("data-uk-","").replace("uk-",""))}var Mi,Bi,Oi,Pi,Hi,zi=function(t){this._init(t)};zi.util=Ni,zi.data="__uikit__",zi.prefix="uk-",zi.options={},zi.instances={},zi.elements=[],function(t){var e,i=t.data;function n(t,e){if(t)for(var i in t)t[i]._isReady&&t[i]._callUpdate(e)}t.use=function(t){if(!t.installed)return t.call(null,this),t.installed=!0,this},t.mixin=function(e,i){i=(Ve(i)?t.components[i]:i)||this,(e=vi({},e)).mixins=i.options.mixins,delete i.options.mixins,i.options=vi(e,i.options)},t.extend=function(t){t=t||{};var e=function(t){this._init(t)};return(e.prototype=Object.create(this.prototype)).constructor=e,e.options=vi(this.options,t),e.super=this,e.extend=this.extend,e},t.update=function(e,o,s){if(void 0===s&&(s=!1),e=ce(e||"update"),o)if(o=ne(o),s)do{n(o[i],e),o=o.parentNode}while(o);else!function t(e,i){if(1===e.nodeType)for(i(e),e=e.firstElementChild;e;)t(e,i),e=e.nextElementSibling}(o,function(t){return n(t[i],e)});else n(t.instances,e)},Object.defineProperty(t,"container",{get:function(){return e||s.body},set:function(t){e=Ht(t)}})}(zi),(Mi=zi).prototype._callHook=function(t){var e=this,i=this.$options[t];i&&i.forEach(function(t){return t.call(e)})},Mi.prototype._callConnected=function(){var t=this;this._connected||(Oe(Mi.elements,this.$options.el)||Mi.elements.push(this.$options.el),Mi.instances[this._uid]=this,this._data={},this._callHook("beforeConnect"),this._connected=!0,this._initEvents(),this._initObserver(),this._callHook("connected"),this._isReady||rt(function(){return t._callReady()}),this._callUpdate())},Mi.prototype._callDisconnected=function(){if(this._connected){this._callHook("beforeDisconnect"),this._observer&&(this._observer.disconnect(),this._observer=null);var t=Mi.elements.indexOf(this.$options.el);~t&&Mi.elements.splice(t,1),delete Mi.instances[this._uid],this._unbindEvents(),this._callHook("disconnected"),this._connected=!1}},Mi.prototype._callReady=function(){this._isReady||(this._isReady=!0,this._callHook("ready"),this._resetComputeds(),this._callUpdate())},Mi.prototype._callUpdate=function(t){var e=this,i=(t=ce(t||"update")).type,n=t.detail;"update"===i&&n&&n.mutation&&this._resetComputeds();var o=this.$options.update,s=this._frames,r=s.reads,a=s.writes;o&&o.forEach(function(n,o){var s=n.read,l=n.write,h=n.events;("update"===i||Oe(h,i))&&(s&&!Oe(hi.reads,r[o])&&(r[o]=hi.read(function(){var i=s.call(e,e._data,t);!1===i&&l?(hi.clear(a[o]),delete a[o]):We(i)&&ii(e._data,i),delete r[o]})),l&&!Oe(hi.writes,a[o])&&(a[o]=hi.write(function(){l.call(e,e._data,t),delete a[o]})))})},function(t){var e=0;function i(t,e){var i={},n=t.args;void 0===n&&(n=[]);var o=t.props;void 0===o&&(o={});var s,r,a=t.el;if(!o)return i;for(s in o)if(y(a,r=ke(s))){var l=Ke(o[s],b(a,r),a);if("target"===r&&(!l||Se(l,"_")))continue;i[s]=l}var h=function(t,e){var i;void 0===e&&(e=[]);try{return t?Se(t,"{")?JSON.parse(t):e.length&&!Oe(t,":")?((i={})[e[0]]=t,i):t.split(";").reduce(function(t,e){var i=e.split(/:(.+)/),n=i[0],o=i[1];return n&&o&&(t[n.trim()]=o.trim()),t},{}):{}}catch(t){return{}}}($(a,e),n);for(s in h)void 0!==o[r=Ie(s)]&&(i[r]=Ke(o[r],h[s],a));return i}function n(t,e,i){Object.defineProperty(t,e,{enumerable:!0,get:function(){var n=t._computeds,o=t.$props,s=t.$el;return we(n,e)||(n[e]=i.call(t,o,s)),n[e]},set:function(i){t._computeds[e]=i}})}function o(t,e,i){We(e)||(e={name:i,handler:e});var n,s,r=e.name,a=e.el,l=e.handler,h=e.capture,u=e.delegate,c=e.filter,d=e.self;a=He(a)?a.call(t):a||t.$el,Pe(a)?a.forEach(function(n){return o(t,ii({},e,{el:n}),i)}):!a||c&&!c.call(t)||(n=Ve(l)?t[l]:ge(l,t),l=function(t){return Pe(t.detail)?n.apply(void 0,[t].concat(t.detail)):n(t)},d&&(s=l,l=function(t){if(t.target===t.currentTarget||t.target===t.current)return s.call(null,t)}),t._events.push(ae(a,r,u?Ve(u)?u:u.call(t):null,l,h)))}function s(t,e){return t.every(function(t){return!t||!we(t,e)})}t.prototype.props={},t.prototype._init=function(i){i=i||{},i=this.$options=vi(this.constructor.options,i),this.$el=null,this.$name=t.prefix+ke(this.$options.name),this.$props={},this._frames={reads:{},writes:{}},this._events=[],this._uid=e++,this._initData(),this._initMethods(),this._initComputeds(),this._callHook("created"),i.el&&this.$mount(i.el)},t.prototype._initData=function(){var t=this.$options,e=t.defaults,i=t.data;void 0===i&&(i={});var n=t.args;void 0===n&&(n=[]);var o=t.props;void 0===o&&(o={});var s=t.el;for(var r in n.length&&Pe(i)&&(i=i.slice(0,n.length).reduce(function(t,e,i){return We(e)?ii(t,e):t[n[i]]=e,t},{})),ii({},e,o))this.$props[r]=this[r]=we(i,r)&&!qe(i[r])?Ke(o[r],i[r],s):e?e[r]&&Pe(e[r])?e[r].concat():e[r]:null},t.prototype._initMethods=function(){var t=this.$options.methods;if(t)for(var e in t)this[e]=ge(t[e],this)},t.prototype._initComputeds=function(){var t=this.$options.computed;if(this._resetComputeds(),t)for(var e in t)n(this,e,t[e])},t.prototype._resetComputeds=function(){this._computeds={}},t.prototype._initProps=function(t){var e;for(e in this._resetComputeds(),t=t||i(this.$options,this.$name))qe(t[e])||(this.$props[e]=t[e]);var n=[this.$options.computed,this.$options.methods];for(e in this.$props)e in t&&s(n,e)&&(this[e]=this.$props[e])},t.prototype._initEvents=function(){var t=this,e=this.$options.events;e&&e.forEach(function(e){if(we(e,"handler"))o(t,e);else for(var i in e)o(t,e[i],i)})},t.prototype._unbindEvents=function(){this._events.forEach(function(t){return t()}),this._events=[]},t.prototype._initObserver=function(){var t=this,e=this.$options,n=e.attrs,o=e.props,s=e.el;!this._observer&&o&&n&&a&&(n=Pe(n)?n:Object.keys(o).map(function(t){return ke(t)}),this._observer=new a(function(){var e=i(t.$options,t.$name);n.some(function(i){return!qe(e[i])&&e[i]!==t.$props[i]})&&t.$reset(e)}),this._observer.observe(s,{attributes:!0,attributeFilter:n.concat([this.$name,"data-"+this.$name])}))}}(zi),Oi=(Bi=zi).data,Bi.prototype.$mount=function(t){var e=this.$options.name;t[Oi]||(t[Oi]={}),t[Oi][e]||(t[Oi][e]=this,this.$el=this.$options.el=this.$options.el||t,this._initProps(),this._callHook("init"),Rt(t,r)&&this._callConnected())},Bi.prototype.$emit=function(t){this._callUpdate(t)},Bi.prototype.$update=function(t,e){Bi.update(t,this.$options.el,e)},Bi.prototype.$reset=function(t){this._callDisconnected(),this._initProps(t),this._callConnected()},Bi.prototype.$destroy=function(t){void 0===t&&(t=!1);var e=this.$options,i=e.el,n=e.name;i&&this._callDisconnected(),this._callHook("destroy"),i&&i[Oi]&&(delete i[Oi][n],Object.keys(i[Oi]).length||delete i[Oi],t&&_t(this.$el))},Hi=(Pi=zi).data,Pi.components={},Pi.component=function(t,e){var i=Ie(t);if(We(e))e.name=i,e=Pi.extend(e);else{if(qe(e))return Pi.components[i];e.options.name=i}return Pi.components[i]=e,Pi[i]=function(t,e){for(var n=arguments.length,o=Array(n);n--;)o[n]=arguments[n];return We(t)?new Pi.components[i]({data:t}):Pi.components[i].options.functional?new Pi.components[i]({data:[].concat(o)}):t&&t.nodeType?s(t):zt(t).map(s)[0];function s(t){var n=Pi.getComponent(t,i);return n&&e&&n.$reset(e),n||new Pi.components[i]({el:t,data:e||{}})}},Pi._initialized&&!e.options.functional&&hi.read(function(){return Pi[i]("[uk-"+t+"],[data-uk-"+t+"]")}),Pi.components[i]},Pi.getComponents=function(t){return t&&(t=te(t)?t[0]:t)&&t[Hi]||{}},Pi.getComponent=function(t,e){return Pi.getComponents(t)[e]},Pi.connect=function(t){var e;if(t[Hi])for(e in t[Hi])t[Hi][e]._callConnected();for(var i=0;i<t.attributes.length;i++)(e=Di(t.attributes[i].name))&&e in Pi.components&&Pi[e](t)},Pi.disconnect=function(t){for(var e in t[Hi])t[Hi][e]._callDisconnected()};var Wi,Li,ji={init:function(){I(this.$el,this.$name)}},Fi={props:{container:Boolean},defaults:{container:!0},computed:{container:function(t){var e=t.container;return!0===e&&zi.container||e&&Ht(e)}}},Vi={props:{cls:Boolean,animation:"list",duration:Number,origin:String,transition:String,queued:Boolean},defaults:{cls:!1,animation:[!1],duration:200,origin:!1,transition:"linear",queued:!1,initProps:{overflow:"",height:"",paddingTop:"",paddingBottom:"",marginTop:"",marginBottom:""},hideProps:{overflow:"hidden",height:0,paddingTop:0,paddingBottom:0,marginTop:0,marginBottom:0}},computed:{hasAnimation:function(t){return!!t.animation[0]},hasTransition:function(t){var e=t.animation;return this.hasAnimation&&!0===e[0]}},methods:{toggleElement:function(t,e,i){var n=this;return new be(function(o){var r,a=function(t){return be.all(t.map(function(t){return n._toggleElement(t,e,i)}))},l=(t=oe(t)).filter(function(t){return n.isToggled(t)}),h=t.filter(function(t){return!Oe(l,t)});if(n.queued&&qe(i)&&qe(e)&&n.hasAnimation&&!(t.length<2)){var u=s.body,c=u.scrollTop,d=l[0],f=ft.inProgress(d)&&_(d,"uk-animation-leave")||lt.inProgress(d)&&"0px"===d.style.height;r=a(l),f||(r=r.then(function(){var t=a(h);return u.scrollTop=c,t}))}else r=a(h.concat(l));r.then(o,si)})},toggleNow:function(t,e){var i=this;return new be(function(n){return be.all(oe(t).map(function(t){return i._toggleElement(t,e,!1)})).then(n,si)})},isToggled:function(t){var e=oe(t||this.$el);return this.cls?_(e,this.cls.split(" ")[0]):!y(e,"hidden")},updateAria:function(t){!1===this.cls&&b(t,"aria-hidden",!this.isToggled(t))},_toggleElement:function(t,e,i){var n=this;if(e=Fe(e)?e:ft.inProgress(t)?_(t,"uk-animation-leave"):lt.inProgress(t)?"0px"===t.style.height:!this.isToggled(t),!ue(t,"before"+(e?"show":"hide"),[this]))return be.reject();var o=(!1!==i&&this.hasAnimation?this.hasTransition?this._toggleHeight:this._toggleAnimation:this._toggleImmediate)(t,e);return ue(t,e?"show":"hide",[this]),o.then(function(){ue(t,e?"shown":"hidden",[n]),zi.update(null,t)})},_toggle:function(t,e){t&&(this.cls?S(t,this.cls,Oe(this.cls," ")?void 0:e):b(t,"hidden",e?null:""),zt("[autofocus]",t).some(function(t){return bt(t)&&(t.focus()||!0)}),this.updateAria(t),zi.update(null,t))},_toggleImmediate:function(t,e){return this._toggle(t,e),be.resolve()},_toggleHeight:function(t,e){var i,n=this,o=lt.inProgress(t),s=t.hasChildNodes?Je(M(t.firstElementChild,"marginTop"))+Je(M(t.lastElementChild,"marginBottom")):0,r=bt(t)?U(t)+(o?0:s):0;return lt.cancel(t),this.isToggled(t)||this._toggle(t,!0),U(t,""),hi.flush(),i=U(t)+(o?0:s),U(t,r),(e?lt.start(t,ii({},this.initProps,{overflow:"hidden",height:i}),Math.round(this.duration*(1-r/i)),this.transition):lt.start(t,this.hideProps,Math.round(this.duration*(r/i)),this.transition).then(function(){return n._toggle(t,!1)})).then(function(){return M(t,n.initProps)})},_toggleAnimation:function(t,e){var i=this;return ft.cancel(t),e?(this._toggle(t,!0),ft.in(t,this.animation[0],this.duration,this.origin)):ft.out(t,this.animation[1]||this.animation[0],this.duration,this.origin).then(function(){return i._toggle(t,!1)})}}},Ri={mixins:[ji,Fi,Vi],props:{clsPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean},defaults:{cls:"uk-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1},computed:{panel:function(t,e){return Ht("."+t.clsPanel,e)},transitionElement:function(){return this.panel},transitionDuration:function(){return ti(M(this.transitionElement,"transitionDuration"))}},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.hide()}},{name:"toggle",self:!0,handler:function(t){t.defaultPrevented||(t.preventDefault(),this.toggle())}},{name:"beforeshow",self:!0,handler:function(t){var e=Wi&&Wi!==this&&Wi;if(Wi=this,e){if(!this.stack)return e.hide().then(this.show),void t.preventDefault();this.prev=e}!function(){if(Li)return;Li=[ae(r,"click",function(t){var e=t.target,i=t.defaultPrevented;Wi&&Wi.bgClose&&!i&&!Rt(e,Wi.panel||Wi.$el)&&Wi.hide()}),ae(s,"keydown",function(t){27===t.keyCode&&Wi&&Wi.escClose&&(t.preventDefault(),Wi.hide())})]}()}},{name:"beforehide",self:!0,handler:function(){(Wi=Wi&&Wi!==this&&Wi||this.prev)||(Li&&Li.forEach(function(t){return t()}),Li=null)}},{name:"show",self:!0,handler:function(){_(r,this.clsPage)||(this.scrollbarWidth=X(o)-r.offsetWidth,M(s.body,"overflowY",this.scrollbarWidth&&this.overlay?"scroll":"")),I(r,this.clsPage)}},{name:"hidden",self:!0,handler:function(){for(var t,e=this.prev;e;){if(e.clsPage===this.clsPage){t=!0;break}e=e.prev}t||T(r,this.clsPage),!this.prev&&M(s.body,"overflowY","")}}],methods:{toggle:function(){return this.isToggled()?this.hide():this.show()},show:function(){if(!this.isToggled())return this.container&&this.$el.parentNode!==this.container&&(It(this.container,this.$el),this._callConnected()),this.toggleNow(this.$el,!0)},hide:function(){if(this.isToggled())return this.toggleNow(this.$el,!1)},getActive:function(){return Wi},_toggleImmediate:function(t,e){var i=this;return new be(function(n){return l(function(){i._toggle(t,e),i.transitionDuration?he(i.transitionElement,"transitionend",n,!1,function(t){return t.target===i.transitionElement}):n()})})}}};var Yi={props:{pos:String,offset:null,flip:Boolean,clsPos:String},defaults:{pos:"bottom-"+(ot?"right":"left"),flip:!0,offset:!1,clsPos:""},computed:{pos:function(t){var e=t.pos;return(e+(Oe(e,"-")?"":"-center")).split("-")},dir:function(){return this.pos[0]},align:function(){return this.pos[1]}},methods:{positionAt:function(t,e,i){this._resetComputeds(),C(t,this.clsPos+"-(top|bottom|left|right)(-[a-z]+)?"),M(t,{top:"",left:""});var n,o=this.offset,s=this.getAxis();o=Ye(o)?o:(n=Ht(o))?R(n)["x"===s?"left":"top"]-R(e)["x"===s?"right":"bottom"]:0;var r=V(t,e,"x"===s?et(this.dir)+" "+this.align:this.align+" "+et(this.dir),"x"===s?this.dir+" "+this.align:this.align+" "+this.dir,"x"===s?""+("left"===this.dir?-o:o):" "+("top"===this.dir?-o:o),null,this.flip,i).target,a=r.x,l=r.y;this.dir="x"===s?a:l,this.align="x"===s?l:a,S(t,this.clsPos+"-"+this.dir+"-"+this.align,!1===this.offset)},getAxis:function(){return"top"===this.dir||"bottom"===this.dir?"y":"x"}}};function qi(t){t.component("accordion",{mixins:[ji,Vi],props:{targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,transition:String},defaults:{targets:"> *",active:!1,animation:[!0],collapsible:!0,multiple:!1,clsOpen:"uk-open",toggle:"> .uk-accordion-title",content:"> .uk-accordion-content",transition:"ease"},computed:{items:function(t,e){return zt(t.targets,e)}},events:[{name:"click",delegate:function(){return this.targets+" "+this.$props.toggle},handler:function(t){t.preventDefault(),this.toggle(Ot(zt(this.targets+" "+this.$props.toggle,this.$el),t.current))}}],connected:function(){if(!1!==this.active){var t=this.items[Number(this.active)];t&&!_(t,this.clsOpen)&&this.toggle(t,!1)}},update:function(){var t=this;this.items.forEach(function(e){return t._toggleImmediate(Ht(t.content,e),_(e,t.clsOpen))});var e=!this.collapsible&&!_(this.items,this.clsOpen)&&this.items[0];e&&this.toggle(e,!1)},methods:{toggle:function(t,e){var i=this,n=mt(t,this.items),o=Vt(this.items,"."+this.clsOpen);(t=this.items[n])&&[t].concat(!this.multiple&&!Oe(o,t)&&o||[]).forEach(function(n){var s=n===t,r=s&&!_(n,i.clsOpen);if(r||!s||i.collapsible||!(o.length<2)){S(n,i.clsOpen,r);var a=n._wrapper?n._wrapper.firstElementChild:Ht(i.content,n);n._wrapper||(n._wrapper=St(a,"<div>"),b(n._wrapper,"hidden",r?"":null)),i._toggleImmediate(a,!0),i.toggleElement(n._wrapper,r,e).then(function(){_(n,i.clsOpen)===r&&(r||i._toggleImmediate(a,!1),n._wrapper=null,Nt(a))})}})}}})}function Ui(t){t.component("alert",{attrs:!0,mixins:[ji,Vi],args:"animation",props:{close:String},defaults:{animation:[!0],selClose:".uk-alert-close",duration:150,hideProps:ii({opacity:0},Vi.defaults.hideProps)},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.close()}}],methods:{close:function(){var t=this;this.toggleElement(this.$el).then(function(){return t.$destroy(!0)})}}})}function Xi(t){rt(function(){var e=0,i=0;if(ae(o,"load resize",t.update),ae(o,"scroll",function(i){i.dir=e<=o.pageYOffset?"down":"up",i.scrollY=e=o.pageYOffset,t.update(i)}),ae(s,"animationstart",function(t){var e=t.target;(M(e,"animationName")||"").match(/^uk-.*(left|right)/)&&(i++,s.body.style.overflowX="hidden",setTimeout(function(){--i||(s.body.style.overflowX="")},ti(M(e,"animationDuration"))+100))},!0),c){var n="uk-hover";ae(s,"tap",function(t){var e=t.target;return zt("."+n).forEach(function(t){return!Rt(e,t)&&T(t,n)})}),Object.defineProperty(t,"hoverSelector",{set:function(t){ae(s,"tap",t,function(t){return I(t.current,n)})}}),t.hoverSelector=".uk-animation-toggle, .uk-transition-toggle, [uk-hover]"}})}function Ji(t){t.component("cover",{mixins:[ji,t.components.video.options],props:{width:Number,height:Number},defaults:{automute:!0},update:{write:function(){var t=this.$el;if(bt(t)){var e=t.parentNode,i=e.offsetHeight,n=e.offsetWidth;M(M(t,{width:"",height:""}),wt.cover({width:this.width||t.clientWidth,height:this.height||t.clientHeight},{width:n+(n%2?1:0),height:i+(i%2?1:0)}))}},events:["load","resize"]},events:{loadedmetadata:function(){this.$emit()}}})}function Gi(t){var e,i;t.component("drop",{mixins:[Yi,Vi],args:"pos",props:{mode:"list",toggle:Boolean,boundary:"query",boundaryAlign:Boolean,delayShow:Number,delayHide:Number,clsDrop:String},defaults:{mode:["click","hover"],toggle:!0,boundary:o,boundaryAlign:!1,delayShow:0,delayHide:800,clsDrop:!1,hoverIdle:200,animation:["uk-animation-fade"],cls:"uk-open"},computed:{clsDrop:function(t){var e=t.clsDrop;return e||"uk-"+this.$options.name},clsPos:function(){return this.clsDrop}},init:function(){this.tracker=new fi,I(this.$el,this.clsDrop)},connected:function(){var e=this.$props.toggle;this.toggle=e&&t.toggle(Ve(e)?Lt(e,this.$el):this.$el.previousElementSibling,{target:this.$el,mode:this.mode}),this.updateAria(this.$el)},events:[{name:"click",delegate:function(){return"."+this.clsDrop+"-close"},handler:function(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){if(!t.defaultPrevented){var e=t.target.hash;e||t.preventDefault(),e&&Rt(e,this.$el)||this.hide(!1)}}},{name:"beforescroll",handler:function(){this.hide(!1)}},{name:"toggle",self:!0,handler:function(t,e){t.preventDefault(),this.isToggled()?this.hide(!1):this.show(e,!1)}},{name:m,filter:function(){return Oe(this.mode,"hover")},handler:function(t){_i(t)||(e&&e!==this&&e.toggle&&Oe(e.toggle.mode,"hover")&&!Rt(t.target,e.toggle.$el)&&!ai({x:t.pageX,y:t.pageY},R(e.$el))&&e.hide(!1),t.preventDefault(),this.show(this.toggle))}},{name:"toggleshow",handler:function(t,e){e&&!Oe(e.target,this.$el)||(t.preventDefault(),this.show(e||this.toggle))}},{name:"togglehide "+g,handler:function(t,e){_i(t)||e&&!Oe(e.target,this.$el)||(t.preventDefault(),this.toggle&&Oe(this.toggle.mode,"hover")&&this.hide())}},{name:"beforeshow",self:!0,handler:function(){this.clearTimers(),this.position()}},{name:"show",self:!0,handler:function(){this.tracker.init(),I(this.toggle.$el,this.cls),b(this.toggle.$el,"aria-expanded","true"),function(){if(i)return;i=!0,ae(r,"click",function(t){var i,n=t.target,o=t.defaultPrevented;if(!o)for(;e&&e!==i&&!Rt(n,e.$el)&&(!e.toggle||!Rt(n,e.toggle.$el));)i=e,e.hide(!1)})}()}},{name:"beforehide",self:!0,handler:function(){this.clearTimers()}},{name:"hide",handler:function(t){var i=t.target;this.$el===i?(e=this.isActive()?null:e,T(this.toggle.$el,this.cls),b(this.toggle.$el,"aria-expanded","false"),this.toggle.$el.blur(),zt("a, button",this.toggle.$el).forEach(function(t){return t.blur()}),this.tracker.cancel()):e=null===e&&Rt(i,this.$el)&&this.isToggled()?this:e}}],update:{write:function(){this.isToggled()&&!ft.inProgress(this.$el)&&this.position()},events:["resize"]},methods:{show:function(t,i){var n=this;void 0===i&&(i=!0);var o=function(){return!n.isToggled()&&n.toggleElement(n.$el,!0)},s=function(){if(n.toggle=t||n.toggle,n.clearTimers(),!n.isActive())if(i&&e&&e!==n&&e.isDelaying)n.showTimer=setTimeout(n.show,10);else{if(n.isParentOf(e)){if(!e.hideTimer)return;e.hide(!1)}else if(e&&!n.isChildOf(e)&&!n.isParentOf(e))for(var s;e&&e!==s&&!n.isChildOf(e);)s=e,e.hide(!1);i&&n.delayShow?n.showTimer=setTimeout(o,n.delayShow):o(),e=n}};t&&this.toggle&&t.$el!==this.toggle.$el?(he(this.$el,"hide",s),this.hide(!1)):s()},hide:function(t){var e=this;void 0===t&&(t=!0);var i=function(){return e.toggleNow(e.$el,!1)};this.clearTimers(),this.isDelaying=this.tracker.movesTo(this.$el),t&&this.isDelaying?this.hideTimer=setTimeout(this.hide,this.hoverIdle):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers:function(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null,this.isDelaying=!1},isActive:function(){return e===this},isChildOf:function(t){return t&&t!==this&&Rt(this.$el,t.$el)},isParentOf:function(t){return t&&t!==this&&Rt(t.$el,this.$el)},position:function(){C(this.$el,this.clsDrop+"-(stack|boundary)"),M(this.$el,{top:"",left:"",display:"block"}),S(this.$el,this.clsDrop+"-boundary",this.boundaryAlign);var t=R(this.boundary),e=this.boundaryAlign?t:R(this.toggle.$el);if("justify"===this.align){var i="y"===this.getAxis()?"width":"height";M(this.$el,i,e[i])}else this.$el.offsetWidth>Math.max(t.right-e.left,e.right-t.left)&&I(this.$el,this.clsDrop+"-stack");this.positionAt(this.$el,this.boundaryAlign?this.boundary:this.toggle.$el,this.boundary),M(this.$el,"display","")}}}),t.drop.getActive=function(){return e}}function Zi(t){t.component("dropdown",t.components.drop.extend({name:"dropdown"}))}function Qi(t){t.component("form-custom",{mixins:[ji],args:"target",props:{target:Boolean},defaults:{target:!1},computed:{input:function(t,e){return Ht(yt,e)},state:function(){return this.input.nextElementSibling},target:function(t,e){var i=t.target;return i&&(!0===i&&this.input.parentNode===e&&this.input.nextElementSibling||Lt(i,e))}},connected:function(){ue(this.input,"change")},events:[{name:"focusin focusout mouseenter mouseleave",delegate:yt,handler:function(t){var e=t.type;t.current===this.input&&S(this.state,"uk-"+(Oe(e,"focus")?"focus":"hover"),Oe(["focusin","mouseenter"],e))}},{name:"change",handler:function(){var t,e=this.target,i=this.input;e&&(e[xt(e)?"value":"textContent"]=i.files&&i.files[0]?i.files[0].name:Gt(i,"select")&&(t=zt("option",i).filter(function(t){return t.selected})[0])?t.textContent:i.value)}}]})}function Ki(t){t.component("gif",{update:{read:function(t){var e=pt(this.$el);if(!e||t.isInView===e)return!1;t.isInView=e},write:function(){this.$el.src=this.$el.src},events:["scroll","load","resize"]}})}function tn(t){t.component("grid",t.components.margin.extend({mixins:[ji],name:"grid",defaults:{margin:"uk-grid-margin",clsStack:"uk-grid-stack"},update:{write:function(t){var e=t.stacks;S(this.$el,this.clsStack,e)},events:["load","resize"]}}))}function en(t){t.component("height-match",{args:"target",props:{target:String,row:Boolean},defaults:{target:"> *",row:!0},computed:{elements:function(t,e){return zt(t.target,e)}},update:{read:function(){var t=this,e=!1;return M(this.elements,"minHeight",""),{rows:this.row?this.elements.reduce(function(t,i){return e!==i.offsetTop?t.push([i]):t[t.length-1].push(i),e=i.offsetTop,t},[]).map(function(e){return t.match(e)}):[this.match(this.elements)]}},write:function(t){t.rows.forEach(function(t){var e=t.height;return M(t.elements,"minHeight",e)})},events:["load","resize"]},methods:{match:function(t){if(t.length<2)return{};var e=0,i=[];return t.forEach(function(t){var n,o;bt(t)||(n=b(t,"style"),o=b(t,"hidden"),b(t,{style:(n||"")+";display:block !important;",hidden:null})),e=Math.max(e,t.offsetHeight),i.push(t.offsetHeight),qe(n)||b(t,{style:n,hidden:o})}),t=t.filter(function(t,n){return i[n]<e}),{height:e,elements:t}}}})}function nn(t){function e(t){return t&&t.offsetHeight||0}t.component("height-viewport",{props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean,minHeight:Number},defaults:{expand:!1,offsetTop:!1,offsetBottom:!1,minHeight:0},update:{write:function(){M(this.$el,"boxSizing","border-box");var t,i=U(o),n=0;if(this.expand){M(this.$el,{height:"",minHeight:""});var s=i-e(r);s>0&&(t=e(this.$el)+s)}else{var a=R(this.$el).top;a<i/2&&this.offsetTop&&(n+=a),!0===this.offsetBottom?n+=e(this.$el.nextElementSibling):Ye(this.offsetBottom)?n+=i/100*this.offsetBottom:this.offsetBottom&&Ne(this.offsetBottom,"px")?n+=Je(this.offsetBottom):Ve(this.offsetBottom)&&(n+=e(Lt(this.offsetBottom,this.$el))),t=n?"calc(100vh - "+n+"px)":"100vh"}if(t){M(this.$el,{height:"",minHeight:t});var l=this.$el.offsetHeight;this.minHeight&&this.minHeight>l&&M(this.$el,"minHeight",this.minHeight),i-n>=l&&M(this.$el,"height",t)}},events:["load","resize"]}})}var on,sn='<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"/><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"/></svg>',rn='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"/><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"/></svg>',an='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect x="9" y="4" width="1" height="11"/><rect x="4" y="9" width="11" height="1"/></svg>',ln='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect y="9" width="20" height="2"/><rect y="3" width="20" height="2"/><rect y="15" width="20" height="2"/></svg>',hn='<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><rect x="19" y="0" width="1" height="40"/><rect x="0" y="19" width="40" height="1"/></svg>',un='<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"/></svg>',cn='<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"/></svg>',dn='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"/><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"/></svg>',fn='<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"/><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"/></svg>',pn='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',mn='<svg width="14px" height="24px" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1.225,23 12.775,12 1.225,1 "/></svg>',gn='<svg width="25px" height="40px" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="4.002,38.547 22.527,20.024 4,1.5 "/></svg>',vn='<svg width="14px" height="24px" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="12.775,1 1.225,12 12.775,23 "/></svg>',wn='<svg width="25px" height="40px" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="20.527,1.5 2,20.024 20.525,38.547 "/></svg>',bn='<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"/></svg>',yn='<svg width="18" height="10" viewBox="0 0 18 10" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9 "/></svg>';function xn(t){var e={},i={spinner:bn,totop:yn,marker:an,"close-icon":sn,"close-large":rn,"navbar-toggle-icon":ln,"overlay-icon":hn,"pagination-next":un,"pagination-previous":cn,"search-icon":dn,"search-large":fn,"search-navbar":pn,"slidenav-next":mn,"slidenav-next-large":gn,"slidenav-previous":vn,"slidenav-previous-large":wn};function n(e,i){t.component(e,t.components.icon.extend({name:e,mixins:i?[i]:[],defaults:{icon:e}}))}t.component("icon",t.components.svg.extend({attrs:["icon","ratio"],mixins:[ji],name:"icon",args:"icon",props:["icon"],defaults:{exclude:["id","style","class","src","icon"]},init:function(){I(this.$el,"uk-icon"),ot&&(this.icon=ei(ei(this.icon,"left","right"),"previous","next"))},methods:{getSvg:function(){var t=function(t){if(!i[t])return null;e[t]||(e[t]=Ht(i[t].trim()));return e[t]}(this.icon);return t?be.resolve(t):be.reject("Icon not found.")}}})),["marker","navbar-toggle-icon","overlay-icon","pagination-previous","pagination-next","totop"].forEach(function(t){return n(t)}),["slidenav-previous","slidenav-next"].forEach(function(t){return n(t,{init:function(){I(this.$el,"uk-slidenav"),_(this.$el,"uk-slidenav-large")&&(this.icon+="-large")}})}),n("search-icon",{init:function(){_(this.$el,"uk-search-icon")&&Kt(this.$el,".uk-search-large").length?this.icon="search-large":Kt(this.$el,".uk-search-navbar").length&&(this.icon="search-navbar")}}),n("close",{init:function(){this.icon="close-"+(_(this.$el,"uk-close-large")?"large":"icon")}}),n("spinner",{connected:function(){var t=this;this.svg.then(function(e){return 1!==t.ratio&&M(Ht("circle",e),"stroke-width",1/t.ratio)},si)}}),t.icon.add=function(n){Object.keys(n).forEach(function(t){i[t]=n[t],delete e[t]}),t._initialized&&ni(t.instances,function(t){"icon"===t.$options.name&&t.$reset()})}}function kn(t){t.component("leader",{mixins:[ji],props:{fill:String,media:"media"},defaults:{fill:"",media:!1,clsWrapper:"uk-leader-fill",clsHide:"uk-leader-hide",attrFill:"data-fill"},computed:{fill:function(t){var e=t.fill;return e||H("leader-fill")}},connected:function(){this.wrapper=At(this.$el,'<span class="'+this.clsWrapper+'">')[0]},disconnected:function(){Nt(this.wrapper.childNodes)},update:[{read:function(t){var e=t.changed,i=t.width,n=i;return{width:i=Math.floor(this.$el.offsetWidth/2),changed:e||n!==i,hide:this.media&&!o.matchMedia(this.media).matches}},write:function(t){S(this.wrapper,this.clsHide,t.hide),t.changed&&(t.changed=!1,b(this.wrapper,this.attrFill,new Array(t.width).join(this.fill)))},events:["load","resize"]}]})}function $n(t){t.component("margin",{props:{margin:String,firstColumn:Boolean},defaults:{margin:"uk-margin-small-top",firstColumn:"uk-first-column"},update:{read:function(t){var e=this.$el.children;if(!e.length||!bt(this.$el))return t.rows=!1;t.stacks=!0;for(var i=[[]],n=0;n<e.length;n++){var o=e[n],s=o.getBoundingClientRect();if(s.height)for(var r=i.length-1;r>=0;r--){var a=i[r];if(!a[0]){a.push(o);break}var l=a[0].getBoundingClientRect();if(s.top>=Math.floor(l.bottom)){i.push([o]);break}if(Math.floor(s.bottom)>l.top){if(t.stacks=!1,s.left<l.left&&!ot){a.unshift(o);break}a.push(o);break}if(0===r){i.unshift([o]);break}}}t.rows=i},write:function(t){var e=this;t.rows.forEach(function(t,i){return t.forEach(function(t,n){S(t,e.margin,0!==i),S(t,e.firstColumn,0===n)})})},events:["load","resize"]}})}function In(t){t.component("modal",{mixins:[Ri],defaults:{clsPage:"uk-modal-page",clsPanel:"uk-modal-dialog",selClose:".uk-modal-close, .uk-modal-close-default, .uk-modal-close-outside, .uk-modal-close-full"},events:[{name:"show",self:!0,handler:function(){_(this.panel,"uk-margin-auto-vertical")?I(this.$el,"uk-flex"):M(this.$el,"display","block"),U(this.$el)}},{name:"hidden",self:!0,handler:function(){M(this.$el,"display",""),T(this.$el,"uk-flex")}}]}),t.component("overflow-auto",{mixins:[ji],computed:{modal:function(t,e){return Qt(e,".uk-modal")},panel:function(t,e){return Qt(e,".uk-modal-dialog")}},connected:function(){M(this.$el,"minHeight",150)},update:{write:function(){if(this.panel&&this.modal){var t=M(this.$el,"maxHeight");M(M(this.$el,"maxHeight",150),"maxHeight",Math.max(150,150+U(this.modal)-this.panel.offsetHeight)),t!==M(this.$el,"maxHeight")&&ue(this.$el,"resize")}},events:["load","resize"]}}),t.modal.dialog=function(e,i){var n=t.modal(' <div class="uk-modal"> <div class="uk-modal-dialog">'+e+"</div> </div> ",i);return n.show(),ae(n.$el,"hidden",function(t){t.target===t.currentTarget&&n.$destroy(!0)}),n},t.modal.alert=function(e,i){return i=ii({bgClose:!1,escClose:!1,labels:t.modal.labels},i),new be(function(n){return ae(t.modal.dialog(' <div class="uk-modal-body">'+(Ve(e)?e:$t(e))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-primary uk-modal-close" autofocus>'+i.labels.ok+"</button> </div> ",i).$el,"hide",n)})},t.modal.confirm=function(e,i){return i=ii({bgClose:!1,escClose:!0,labels:t.modal.labels},i),new be(function(n,o){var s=!1,r=t.modal.dialog(' <form> <div class="uk-modal-body">'+(Ve(e)?e:$t(e))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+i.labels.cancel+'</button> <button class="uk-button uk-button-primary" autofocus>'+i.labels.ok+"</button> </div> </form> ",i);ae(r.$el,"submit","form",function(t){t.preventDefault(),n(),s=!0,r.hide()}),ae(r.$el,"hide",function(){s||o()})})},t.modal.prompt=function(e,i,n){return n=ii({bgClose:!1,escClose:!0,labels:t.modal.labels},n),new be(function(o){var s=!1,r=t.modal.dialog(' <form class="uk-form-stacked"> <div class="uk-modal-body"> <label>'+(Ve(e)?e:$t(e))+'</label> <input class="uk-input" autofocus> </div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+n.labels.cancel+'</button> <button class="uk-button uk-button-primary">'+n.labels.ok+"</button> </div> </form> ",n),a=Ht("input",r.$el);a.value=i,ae(r.$el,"submit","form",function(t){t.preventDefault(),o(a.value),s=!0,r.hide()}),ae(r.$el,"hide",function(){s||o(null)})})},t.modal.labels={ok:"Ok",cancel:"Cancel"}}function Tn(t){t.component("nav",t.components.accordion.extend({name:"nav",defaults:{targets:"> .uk-parent",toggle:"> a",content:"> ul"}}))}function Cn(t){t.component("navbar",{mixins:[ji],props:{dropdown:String,mode:"list",align:String,offset:Number,boundary:Boolean,boundaryAlign:Boolean,clsDrop:String,delayShow:Number,delayHide:Number,dropbar:Boolean,dropbarMode:String,dropbarAnchor:"query",duration:Number},defaults:{dropdown:".uk-navbar-nav > li",align:ot?"right":"left",clsDrop:"uk-navbar-dropdown",mode:void 0,offset:void 0,delayShow:void 0,delayHide:void 0,boundaryAlign:void 0,flip:"x",boundary:!0,dropbar:!1,dropbarMode:"slide",dropbarAnchor:!1,duration:200},computed:{boundary:function(t,e){var i=t.boundary,n=t.boundaryAlign;return!0===i||n?e:i},pos:function(t){return"bottom-"+t.align}},beforeConnect:function(){var t=this.$props.dropbar;this.dropbar=t&&(Ve(t)&&Lt(t,this.$el)||Ht("<div></div>")),this.dropbar&&(I(this.dropbar,"uk-navbar-dropbar"),"slide"===this.dropbarMode&&I(this.dropbar,"uk-navbar-dropbar-slide"))},disconnected:function(){this.dropbar&&_t(this.dropbar)},update:function(){t.drop(zt(this.dropdown+" ."+this.clsDrop,this.$el).filter(function(e){return!t.getComponent(e,"drop")&&!t.getComponent(e,"dropdown")}),ii({},this.$props,{boundary:this.boundary,pos:this.pos,offset:this.dropbar||this.offset}))},events:[{name:"mouseover",delegate:function(){return this.dropdown},handler:function(t){var e=t.current,i=this.getActive();i&&i.toggle&&!Rt(i.toggle.$el,e)&&!i.tracker.movesTo(i.$el)&&i.hide(!1)}},{name:"mouseleave",el:function(){return this.dropbar},handler:function(){var t=this.getActive();t&&!Gt(this.dropbar,":hover")&&t.hide()}},{name:"beforeshow",capture:!0,filter:function(){return this.dropbar},handler:function(){this.dropbar.parentNode||Ct(this.dropbarAnchor||this.$el,this.dropbar)}},{name:"show",capture:!0,filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el;this.clsDrop&&I(i,this.clsDrop+"-dropbar"),this.transitionTo(i.offsetHeight+Je(M(i,"margin-top"))+Je(M(i,"margin-bottom")),i)}},{name:"beforehide",filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=this.getActive();Gt(this.dropbar,":hover")&&n&&n.$el===i&&t.preventDefault()}},{name:"hide",filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=this.getActive();(!n||n&&n.$el===i)&&this.transitionTo(0)}}],methods:{getActive:function(){var e=t.drop.getActive();return e&&Oe(e.mode,"hover")&&Rt(e.toggle.$el,this.$el)&&e},transitionTo:function(t,e){var i=this.dropbar,n=bt(i)?U(i):0;return M(e=n<t&&e,{height:n,overflow:"hidden"}),U(i,n),lt.cancel([e,i]),lt.start([e,i],{height:t},this.duration).catch(si).finally(function(){return M(e,{height:"",overflow:""})})}}})}function En(t){t.component("offcanvas",{mixins:[Ri],args:"mode",props:{content:String,mode:String,flip:Boolean,overlay:Boolean},defaults:{content:".uk-offcanvas-content",mode:"slide",flip:!1,overlay:!1,clsPage:"uk-offcanvas-page",clsContainer:"uk-offcanvas-container",clsPanel:"uk-offcanvas-bar",clsFlip:"uk-offcanvas-flip",clsContent:"uk-offcanvas-content",clsContentAnimation:"uk-offcanvas-content-animation",clsSidebarAnimation:"uk-offcanvas-bar-animation",clsMode:"uk-offcanvas",clsOverlay:"uk-offcanvas-overlay",selClose:".uk-offcanvas-close"},computed:{content:function(t){var e=t.content;return Ht(e)||s.body},clsFlip:function(t){var e=t.flip,i=t.clsFlip;return e?i:""},clsOverlay:function(t){var e=t.overlay,i=t.clsOverlay;return e?i:""},clsMode:function(t){var e=t.mode,i=t.clsMode;return i+"-"+e},clsSidebarAnimation:function(t){var e=t.mode,i=t.clsSidebarAnimation;return"none"===e||"reveal"===e?"":i},clsContentAnimation:function(t){var e=t.mode,i=t.clsContentAnimation;return"push"!==e&&"reveal"!==e?"":i},transitionElement:function(t){return"reveal"===t.mode?this.panel.parentNode:this.panel}},update:{write:function(){this.getActive()===this&&((this.overlay||this.clsContentAnimation)&&X(this.content,X(o)-this.scrollbarWidth),this.overlay&&(U(this.content,U(o)),on&&(this.content.scrollTop=on.y)))},events:["resize"]},events:[{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){var e=t.current;e.hash&&Ht(e.hash,this.content)&&(on=null,this.hide())}},{name:"beforescroll",filter:function(){return this.overlay},handler:function(t,e,i){e&&i&&this.isToggled()&&Ht(i,this.content)&&(he(this.$el,"hidden",function(){return e.scrollTo(i)}),t.preventDefault())}},{name:"show",self:!0,handler:function(){on=on||{x:o.pageXOffset,y:o.pageYOffset},"reveal"!==this.mode||_(this.panel,this.clsMode)||(St(this.panel,"<div>"),I(this.panel.parentNode,this.clsMode)),M(r,"overflowY",(!this.clsContentAnimation||this.flip)&&this.scrollbarWidth&&this.overlay?"scroll":""),I(s.body,this.clsContainer,this.clsFlip,this.clsOverlay),U(s.body),I(this.content,this.clsContentAnimation),I(this.panel,this.clsSidebarAnimation+" "+("reveal"!==this.mode?this.clsMode:"")),I(this.$el,this.clsOverlay),M(this.$el,"display","block"),U(this.$el)}},{name:"hide",self:!0,handler:function(){T(this.content,this.clsContentAnimation);var t=this.getActive();("none"===this.mode||t&&t!==this&&t!==this.prev)&&ue(this.panel,"transitionend")}},{name:"hidden",self:!0,handler:function(){if("reveal"===this.mode&&Nt(this.panel),this.overlay){if(!on){var t=this.content,e=t.scrollLeft,i=t.scrollTop;on={x:e,y:i}}}else on={x:o.pageXOffset,y:o.pageYOffset};T(this.panel,this.clsSidebarAnimation,this.clsMode),T(this.$el,this.clsOverlay),M(this.$el,"display",""),T(s.body,this.clsContainer,this.clsFlip,this.clsOverlay),s.body.scrollTop=on.y,M(r,"overflow-y",""),X(this.content,""),U(this.content,""),o.scrollTo(on.x,on.y),on=null}},{name:"swipeLeft swipeRight",handler:function(t){this.isToggled()&&_i(t)&&("swipeLeft"===t.type&&!this.flip||"swipeRight"===t.type&&this.flip)&&this.hide()}}]})}function _n(t){t.component("responsive",{props:["width","height"],init:function(){I(this.$el,"uk-responsive-width")},update:{read:function(){return!!(bt(this.$el)&&this.width&&this.height)&&{width:X(this.$el.parentNode),height:this.height}},write:function(t){U(this.$el,wt.contain({height:this.height,width:this.width},t).height)},events:["load","resize"]}})}function Sn(t){t.component("scroll",{props:{duration:Number,offset:Number},defaults:{duration:1e3,offset:0},methods:{scrollTo:function(t){var e=this,i=R(t=t&&Ht(t)||s.body).top-this.offset,n=U(s),r=U(o);if(i+r>n&&(i=n-r),ue(this.$el,"beforescroll",[this,t])){var a=Date.now(),h=o.pageYOffset,u=function(){var n,s=h+(i-h)*(n=oi((Date.now()-a)/e.duration),.5*(1-Math.cos(Math.PI*n)));o.scrollTo(o.pageXOffset,s),s!==i?l(u):ue(e.$el,"scrolled",[e,t])};u()}}},events:{click:function(t){t.defaultPrevented||(t.preventDefault(),this.scrollTo(re(this.$el.hash).substr(1)))}}})}function An(t){t.component("scrollspy",{args:"cls",props:{cls:"list",target:String,hidden:Boolean,offsetTop:Number,offsetLeft:Number,repeat:Boolean,delay:Number},defaults:{cls:["uk-scrollspy-inview"],target:!1,hidden:!0,offsetTop:0,offsetLeft:0,repeat:!1,delay:0,inViewClass:"uk-scrollspy-inview"},computed:{elements:function(t,e){var i=t.target;return i?zt(i,e):[e]}},update:[{write:function(){this.hidden&&M(Vt(this.elements,":not(."+this.inViewClass+")"),"visibility","hidden")}},{read:function(e){var i=this;if(!t._initialized)return!1;this.elements.forEach(function(t,n){var o=e[n];if(!o){var s=$(t,"uk-scrollspy-class");o={toggles:s&&s.split(",")||i.cls}}o.show=pt(t,i.offsetTop,i.offsetLeft),e[n]=o})},write:function(e){var i=this,n=1===this.elements.length?1:0;this.elements.forEach(function(o,s){var r=e[s],a=r.toggles[s]||r.toggles[0];if(r.show){if(!r.inview&&!r.timer){var l=function(){M(o,"visibility",""),I(o,i.inViewClass),S(o,a),ue(o,"inview"),t.update(null,o),r.inview=!0,delete r.timer};i.delay&&n?r.timer=setTimeout(l,i.delay*n):l(),n++}}else r.inview&&i.repeat&&(r.timer&&(clearTimeout(r.timer),delete r.timer),M(o,"visibility",i.hidden?"hidden":""),T(o,i.inViewClass),S(o,a),ue(o,"outview"),t.update(null,o),r.inview=!1)})},events:["scroll","load","resize"]}]})}function Nn(t){t.component("scrollspy-nav",{props:{cls:String,closest:String,scroll:Boolean,overflow:Boolean,offset:Number},defaults:{cls:"uk-active",closest:!1,scroll:!1,overflow:!0,offset:0},computed:{links:function(t,e){return zt('a[href^="#"]',e).filter(function(t){return t.hash})},elements:function(){return this.closest?Qt(this.links,this.closest):this.links},targets:function(){return zt(this.links.map(function(t){return t.hash}).join(","))}},update:[{read:function(){this.scroll&&t.scroll(this.links,{offset:this.offset||0})}},{read:function(t){var e=this,i=o.pageYOffset+this.offset+1,n=U(s)-U(o)+this.offset;t.active=!1,this.targets.every(function(o,s){var r=R(o).top,a=s+1===e.targets.length;if(!e.overflow&&(0===s&&r>i||a&&r+o.offsetTop<i))return!1;if(!a&&R(e.targets[s+1]).top<=i)return!0;if(i>=n)for(var l=e.targets.length-1;l>s;l--)if(pt(e.targets[l])){o=e.targets[l];break}return!(t.active=Ht(Vt(e.links,'[href="#'+o.id+'"]')))})},write:function(t){var e=t.active;this.links.forEach(function(t){return t.blur()}),T(this.elements,this.cls),e&&ue(this.$el,"active",[e,I(this.closest?Qt(e,this.closest):e,this.cls)])},events:["scroll","load","resize"]}]})}function Dn(t){t.component("sticky",{mixins:[ji],attrs:!0,props:{top:null,bottom:Boolean,offset:Number,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,widthElement:"query",showOnUp:Boolean,media:"media",target:Number},defaults:{top:0,bottom:!1,offset:0,animation:"",clsActive:"uk-active",clsInactive:"",clsFixed:"uk-sticky-fixed",clsBelow:"uk-sticky-below",selTarget:"",widthElement:!1,showOnUp:!1,media:!1,target:!1},computed:{selTarget:function(t,e){var i=t.selTarget;return i&&Ht(i,e)||e}},connected:function(){this.placeholder=Ht('<div class="uk-sticky-placeholder"></div>'),this.widthElement=this.$props.widthElement||this.placeholder,this.isActive||this.hide()},disconnected:function(){this.isActive&&(this.isActive=!1,this.hide(),T(this.selTarget,this.clsInactive)),_t(this.placeholder),this.placeholder=null,this.widthElement=null},ready:function(){var t=this;if(this.target&&location.hash&&o.pageYOffset>0){var e=Ht(location.hash);e&&hi.read(function(){var i=R(e).top,n=R(t.$el).top,s=t.$el.offsetHeight;n+s>=i&&n<=i+e.offsetHeight&&o.scrollTo(0,i-s-t.target-t.offset)})}},events:[{name:"active",self:!0,handler:function(){E(this.selTarget,this.clsInactive,this.clsActive)}},{name:"inactive",self:!0,handler:function(){E(this.selTarget,this.clsActive,this.clsInactive)}}],update:[{write:function(){var t,e=this,i=this.placeholder,n=(this.isActive?i:this.$el).offsetHeight;M(i,ii({height:"absolute"!==M(this.$el,"position")?n:""},M(this.$el,["marginTop","marginBottom","marginLeft","marginRight"]))),Rt(i,r)||(Ct(this.$el,i),b(i,"hidden","")),b(this.widthElement,"hidden",null),this.width=this.widthElement.offsetWidth,b(this.widthElement,"hidden",this.isActive?null:""),this.topOffset=R(this.isActive?i:this.$el).top,this.bottomOffset=this.topOffset+n,["top","bottom"].forEach(function(i){e[i]=e.$props[i],e[i]&&(Ye(e[i])?e[i]=e[i+"Offset"]+Je(e[i]):Ve(e[i])&&e[i].match(/^-?\d+vh$/)?e[i]=U(o)*Je(e[i])/100:(t=!0===e[i]?e.$el.parentNode:Lt(e[i],e.$el))&&(e[i]=R(t).top+t.offsetHeight))}),this.top=Math.max(Je(this.top),this.topOffset)-this.offset,this.bottom=this.bottom&&this.bottom-n,this.inactive=this.media&&!o.matchMedia(this.media).matches,this.isActive&&this.update()},events:["load","resize"]},{read:function(t,e){var i=e.scrollY;return void 0===i&&(i=o.pageYOffset),{scroll:this.scroll=i,visible:bt(this.$el)}},write:function(t,e){var i=this,n=t.visible,o=t.scroll;void 0===e&&(e={});var s=e.dir;if(!(o<0||!n||this.disabled||this.showOnUp&&!s))if(this.inactive||o<this.top||this.showOnUp&&(o<=this.top||"down"===s||"up"===s&&!this.isActive&&o<=this.bottomOffset)){if(!this.isActive)return;this.isActive=!1,this.animation&&o>this.topOffset?(ft.cancel(this.$el),ft.out(this.$el,this.animation).then(function(){return i.hide()},si)):this.hide()}else this.isActive?this.update():this.animation?(ft.cancel(this.$el),this.show(),ft.in(this.$el,this.animation).catch(si)):this.show()},events:["scroll"]}],methods:{show:function(){this.isActive=!0,this.update(),b(this.placeholder,"hidden",null)},hide:function(){this.isActive&&!_(this.selTarget,this.clsActive)||ue(this.$el,"inactive"),T(this.$el,this.clsFixed,this.clsBelow),M(this.$el,{position:"",top:"",width:""}),b(this.placeholder,"hidden","")},update:function(){var t=Math.max(0,this.offset),e=0!==this.top||this.scroll>this.top;this.bottom&&this.scroll>this.bottom-this.offset&&(t=this.bottom-this.scroll),M(this.$el,{position:"fixed",top:t+"px",width:this.width}),_(this.selTarget,this.clsActive)?e||ue(this.$el,"inactive"):e&&ue(this.$el,"active"),S(this.$el,this.clsBelow,this.scroll>this.bottomOffset),I(this.$el,this.clsFixed)}}})}var Mn,Bn,On={};function Pn(t){t.component("svg",{attrs:!0,props:{id:String,icon:String,src:String,style:String,width:Number,height:Number,ratio:Number,class:String},defaults:{ratio:1,id:!1,exclude:["src"],class:""},init:function(){this.class+=" uk-svg"},connected:function(){var t=this;if(!this.icon&&Oe(this.src,"#")){var n=this.src.split("#");n.length>1&&(this.src=n[0],this.icon=n[1])}this.svg=this.getSvg().then(function(n){var o;if(Ve(n)?(t.icon&&Oe(n,"<symbol")&&(n=function(t,n){if(!i[t]){var o;for(i[t]={};o=e.exec(t);)i[t][o[3]]='<svg xmlns="http://www.w3.org/2000/svg"'+o[1]+"svg>"}return i[t][n]}(n,t.icon)||n),o=Ht(n.substr(n.indexOf("<svg")))):o=n.cloneNode(!0),!o)return be.reject("SVG not found.");var s=b(o,"viewBox");for(var r in s&&(s=s.split(" "),t.width=t.$props.width||s[2],t.height=t.$props.height||s[3]),t.width*=t.ratio,t.height*=t.ratio,t.$options.props)t[r]&&!Oe(t.exclude,r)&&b(o,r,t[r]);t.id||x(o,"id"),t.width&&!t.height&&x(o,"height"),t.height&&!t.width&&x(o,"width");var a=t.$el;if(vt(a)||"CANVAS"===a.tagName){b(a,{hidden:!0,id:null});var l=a.nextElementSibling;l&&o.isEqualNode(l)?o=l:Ct(a,o)}else{var h=a.lastElementChild;h&&o.isEqualNode(h)?o=h:It(a,o)}return t.svgEl=o,o},si)},disconnected:function(){var t=this;vt(this.$el)&&b(this.$el,{hidden:null,id:this.id||null}),this.svg&&this.svg.then(function(e){return(!t._connected||e!==t.svgEl)&&_t(e)},si),this.svg=this.svgEl=null},methods:{getSvg:function(){var t=this;return this.src?On[this.src]?On[this.src]:(On[this.src]=new be(function(e,i){Se(t.src,"data:")?e(decodeURIComponent(t.src.split(",")[1])):li(t.src).then(function(t){return e(t.response)},function(){return i("SVG not found.")})}),On[this.src]):be.reject()}}});var e=/<symbol(.*?id=(['"])(.*?)\2[^]*?<\/)symbol>/g,i={}}function Hn(t){t.component("switcher",{mixins:[Vi],args:"connect",props:{connect:String,toggle:String,active:Number,swiping:Boolean},defaults:{connect:"~.uk-switcher",toggle:"> *",active:0,swiping:!0,cls:"uk-active",clsContainer:"uk-switcher",attrItem:"uk-switcher-item",queued:!0},computed:{connects:function(t,e){return jt(t.connect,e)},toggles:function(t,e){return zt(t.toggle,e)}},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),this.show(t.current)}},{name:"click",el:function(){return this.connects},delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.show($(t.current,this.attrItem))}},{name:"swipeRight swipeLeft",filter:function(){return this.swiping},el:function(){return this.connects},handler:function(t){_i(t)&&(t.preventDefault(),o.getSelection().toString()||this.show("swipeLeft"===t.type?"next":"previous"))}}],update:function(){var t=this;this.connects.forEach(function(e){return t.updateAria(e.children)}),this.show(Vt(this.toggles,"."+this.cls)[0]||this.toggles[this.active]||this.toggles[0])},methods:{show:function(t){for(var e,i=this,n=this.toggles.length,o=!!this.connects.length&&Ot(Vt(this.connects[0].children,"."+this.cls)[0]),s=o>=0,r=mt(t,this.toggles,o),a="previous"===t?-1:1,l=0;l<n;l++,r=(r+a+n)%n)if(!Gt(i.toggles[r],".uk-disabled, [disabled]")){e=i.toggles[r];break}!e||o>=0&&_(e,this.cls)||o===r||(T(this.toggles,this.cls),b(this.toggles,"aria-expanded",!1),I(e,this.cls),b(e,"aria-expanded",!0),this.connects.forEach(function(t){s?i.toggleElement([t.children[o],t.children[r]]):i.toggleNow(t.children[r])}))}}})}function zn(t){t.component("tab",t.components.switcher.extend({mixins:[ji],name:"tab",props:{media:"media"},defaults:{media:960,attrItem:"uk-tab-item"},init:function(){var e=_(this.$el,"uk-tab-left")?"uk-tab-left":!!_(this.$el,"uk-tab-right")&&"uk-tab-right";e&&t.toggle(this.$el,{cls:e,mode:"media",media:this.media})}}))}function Wn(t){t.component("toggle",{mixins:[t.mixin.togglable],args:"target",props:{href:String,target:null,mode:"list",media:"media"},defaults:{href:!1,target:!1,mode:"click",queued:!0,media:!1},computed:{target:function(t,e){var i=t.href,n=t.target;return n=jt(n||i,e),n.length&&n||[e]}},events:[{name:m+" "+g,filter:function(){return Oe(this.mode,"hover")},handler:function(t){_i(t)||this.toggle("toggle"+(t.type===m?"show":"hide"))}},{name:"click",filter:function(){return Oe(this.mode,"click")||c},handler:function(t){var e;(_i(t)||Oe(this.mode,"click"))&&((Qt(t.target,'a[href="#"], button')||(e=Qt(t.target,"a[href]"))&&(this.cls||!bt(this.target)||e.hash&&Gt(this.target,e.hash)))&&he(s,"click",function(t){return t.preventDefault()}),this.toggle())}}],update:{write:function(){if(Oe(this.mode,"media")&&this.media){var t=this.isToggled(this.target);(o.matchMedia(this.media).matches?!t:t)&&this.toggle()}},events:["load","resize"]},methods:{toggle:function(t){ue(this.target,t||"toggle",[this])&&this.toggleElement(this.target)}}})}function Ln(t){t.component("video",{props:{automute:Boolean,autoplay:Boolean},defaults:{automute:!1,autoplay:!0},computed:{inView:function(t){return"inview"===t.autoplay}},ready:function(){this.player=new bi(this.$el),this.automute&&this.player.mute()},update:[{read:function(t,e){var i=e.type;return!(!this.player||!("scroll"!==i&&"resize"!==i||this.inView))&&{visible:bt(this.$el)&&"hidden"!==M(this.$el,"visibility"),inView:this.inView&&pt(this.$el)}},write:function(t){var e=t.visible,i=t.inView;!e||this.inView&&!i?this.player.pause():(!0===this.autoplay||this.inView&&i)&&this.player.play()},events:["load","resize","scroll"]}]})}function jn(t,e){return void 0===t&&(t=0),void 0===e&&(e="%"),"translateX("+t+(t?e:"")+")"}function Fn(t){return"scale3d("+t+", "+t+", 1)"}function Vn(t){if(!Vn.installed){var e,i,n,o,s,r,a,l,h,u,c,d,f,p,m,g,v,w,b,y,x,k,$,I,T,C,E,_=t.util,S=_.$,A=_.assign,N=_.clamp,D=_.fastdom,M=_.getIndex,B=_.hasClass,O=_.isNumber,P=_.isRtl,H=_.Promise,z=_.toNodes,W=_.trigger;t.mixin.slider={attrs:!0,mixins:[(I=t,T=I.util,C=T.doc,E=T.pointerDown,{props:{autoplay:Boolean,autoplayInterval:Number,pauseOnHover:Boolean},defaults:{autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},connected:function(){this.startAutoplay()},disconnected:function(){this.stopAutoplay()},events:[{name:"visibilitychange",el:C,handler:function(){C.hidden?this.stopAutoplay():this.startAutoplay()}},{name:E,handler:"stopAutoplay"},{name:"mouseenter",filter:function(){return this.autoplay},handler:function(){this.isHovering=!0}},{name:"mouseleave",filter:function(){return this.autoplay},handler:function(){this.isHovering=!1}}],methods:{startAutoplay:function(){var t=this;this.stopAutoplay(),this.autoplay&&(this.interval=setInterval(function(){return!(t.isHovering&&t.pauseOnHover)&&!t.stack.length&&t.show("next")},this.autoplayInterval))},stopAutoplay:function(){this.interval&&clearInterval(this.interval)}}}),(h=t,u=h.util,c=u.doc,d=u.getPos,f=u.includes,p=u.isRtl,m=u.isTouch,g=u.off,v=u.on,w=u.pointerDown,b=u.pointerMove,y=u.pointerUp,x=u.preventClick,k=u.trigger,$=u.win,{defaults:{threshold:10,preventCatch:!1},init:function(){var t=this;["start","move","end"].forEach(function(e){var i=t[e];t[e]=function(e){var n=d(e).x*(p?-1:1);t.prevPos=n!==t.pos?t.pos:t.prevPos,t.pos=n,i(e)}})},events:[{name:w,delegate:function(){return this.slidesSelector},handler:function(t){var e;!m(t)&&!(e=t.target).children.length&&e.childNodes.length||t.button>0||this.length<2||this.preventCatch||this.start(t)}},{name:"dragstart",handler:function(t){t.preventDefault()}}],methods:{start:function(){this.drag=this.pos,this._transitioner?(this.percent=this._transitioner.percent(),this.drag+=this._transitioner.getDistance()*this.percent*this.dir,this._transitioner.translate(this.percent),this._transitioner.cancel(),this.dragging=!0,this.stack=[]):this.prevIndex=this.index,this.unbindMove=v(c,b,this.move,{capture:!0,passive:!1}),v($,"scroll",this.unbindMove),v(c,y,this.end,!0)},move:function(t){var e=this,i=this.pos-this.drag;if(!(0===i||this.prevPos===this.pos||!this.dragging&&Math.abs(i)<this.threshold)){t.cancelable&&t.preventDefault(),this.dragging=!0,this.dir=i<0?1:-1;for(var n=this.slides,o=this.prevIndex,s=Math.abs(i),r=this.getIndex(o+this.dir,o),a=this._getDistance(o,r)||n[o].offsetWidth;r!==o&&s>a;)e.drag-=a*e.dir,o=r,s-=a,r=e.getIndex(o+e.dir,o),a=e._getDistance(o,r)||n[o].offsetWidth;this.percent=s/a;var l,h=n[o],u=n[r],c=this.index!==r,d=o===r;[this.index,this.prevIndex].filter(function(t){return!f([r,o],t)}).forEach(function(t){k(n[t],"itemhidden",[e]),l=!0,d&&(e.prevIndex=o)}),(this.index===o&&this.prevIndex!==o||l&&d)&&k(n[this.index],"itemshown",[this]),c&&(this.prevIndex=o,this.index=r,!d&&k(h,"beforeitemhide",[this]),k(u,"beforeitemshow",[this])),l&&this._transitioner&&this._transitioner.reset(),this._transitioner=this._translate(Math.abs(this.percent),h,!d&&u),c&&(!d&&k(h,"itemhide",[this]),k(u,"itemshow",[this]))}},end:function(){if(g($,"scroll",this.unbindMove),this.unbindMove(),g(c,y,this.end,!0),this.dragging){if(this.dragging=null,this.index===this.prevIndex)this.percent=1-this.percent,this.dir*=-1,this._show(!1,this.index,!0),this._transitioner=null;else{var t=(p?this.dir*(p?1:-1):this.dir)<0==this.prevPos>this.pos;this.index=t?this.index:this.prevIndex,t&&(this.percent=1-this.percent),this.show(this.dir>0&&!t||this.dir<0&&t?"next":"previous",!0)}x()}this.drag=this.percent=null}}}),(e=t,i=e.util,n=i.$,o=i.$$,s=i.data,r=i.html,a=i.toggleClass,l=i.toNumber,{defaults:{selNav:!1},computed:{nav:function(t,e){var i=t.selNav;return n(i,e)},navItemSelector:function(t){var e=t.attrItem;return"["+e+"],[data-"+e+"]"},navItems:function(t,e){return o(this.navItemSelector,e)}},update:[{write:function(){var t=this;this.nav&&this.length!==this.nav.children.length&&r(this.nav,this.slides.map(function(e,i){return"<li "+t.attrItem+'="'+i+'"><a href="#"></a></li>'}).join("")),a(o(this.navItemSelector,this.$el).concat(this.nav),"uk-hidden",!this.maxIndex),this.updateNav()},events:["load","resize"]}],events:[{name:"click",delegate:function(){return this.navItemSelector},handler:function(t){t.preventDefault(),t.current.blur(),this.show(s(t.current,this.attrItem))}},{name:"itemshow",handler:"updateNav"}],methods:{updateNav:function(){var t=this,e=this.getValidIndex();this.navItems.forEach(function(i){var n=s(i,t.attrItem);a(i,t.clsActive,l(n)===e),a(i,"uk-invisible",t.finite&&("previous"===n&&0===e||"next"===n&&e>=t.maxIndex))})}}})],props:{clsActivated:Boolean,easing:String,index:Number,finite:Boolean,velocity:Number},defaults:{easing:"ease",finite:!1,velocity:1,index:0,stack:[],percent:0,clsActive:"uk-active",clsActivated:!1,Transitioner:!1,transitionOptions:{}},computed:{duration:function(t,e){var i=t.velocity;return Rn(e.offsetWidth/i)},length:function(){return this.slides.length},list:function(t,e){var i=t.selList;return S(i,e)},maxIndex:function(){return this.length-1},slidesSelector:function(t){return t.selList+" > *"},slides:function(){return z(this.list.children)}},update:[{read:function(){this._resetComputeds()},events:["load","resize"]}],methods:{show:function(t,e){var i=this;if(void 0===e&&(e=!1),!this.dragging&&this.length){var n=this.stack,o=e?0:n.length,s=function(){n.splice(o,1),n.length&&i.show(n.shift(),!0)};if(n[e?"unshift":"push"](t),!e&&n.length>1)2===n.length&&this._transitioner.forward(Math.min(this.duration,200));else{var r=this.index,a=B(this.slides,this.clsActive)&&this.slides[r],l=this.getIndex(t,this.index),h=this.slides[l];if(a!==h){var u;if(this.dir="next"===(u=t)?1:"previous"===u?-1:u<r?-1:1,this.prevIndex=r,this.index=l,a&&W(a,"beforeitemhide",[this]),!W(h,"beforeitemshow",[this,a]))return this.index=this.prevIndex,void s();var c=this._show(a,h,e).then(function(){return a&&W(a,"itemhidden",[i]),W(h,"itemshown",[i]),new H(function(t){D.write(function(){n.shift(),n.length?i.show(n.shift(),!0):i._transitioner=null,t()})})});return a&&W(a,"itemhide",[this]),W(h,"itemshow",[this]),c}s()}}},getIndex:function(t,e){return void 0===t&&(t=this.index),void 0===e&&(e=this.index),N(M(t,this.slides,e,this.finite),0,this.maxIndex)},getValidIndex:function(t,e){return void 0===t&&(t=this.index),void 0===e&&(e=this.prevIndex),this.getIndex(t,e)},_show:function(t,e,i){if(this._transitioner=this._getTransitioner(t,e,this.dir,A({easing:i?e.offsetWidth<600?"cubic-bezier(0.25, 0.46, 0.45, 0.94)":"cubic-bezier(0.165, 0.84, 0.44, 1)":this.easing},this.transitionOptions)),!i&&!t)return this._transitioner.translate(1),H.resolve();var n=this.stack.length;return this._transitioner[n>1?"forward":"show"](n>1?Math.min(this.duration,75+75/(n-1)):this.duration,this.percent)},_getDistance:function(t,e){return new this._getTransitioner(t,t!==e&&e).getDistance()},_translate:function(t,e,i){void 0===e&&(e=this.prevIndex),void 0===i&&(i=this.index);var n=this._getTransitioner(e!==i&&e,i);return n.translate(t),n},_getTransitioner:function(t,e,i,n){return void 0===t&&(t=this.prevIndex),void 0===e&&(e=this.index),void 0===i&&(i=this.dir||1),void 0===n&&(n=this.transitionOptions),new this.Transitioner(O(t)?this.slides[t]:t,O(e)?this.slides[e]:e,i*(P?-1:1),n)}}}}}function Rn(t){return.5*t+300}function Yn(t){if(!Yn.installed){t.use(Vn);var e,i,n=t.mixin,o=t.util,s=o.addClass,r=o.assign,a=o.fastdom,l=o.isNumber,h=o.removeClass,u=(e=t.util.css,i={slide:{show:function(t){return[{transform:jn(-100*t)},{transform:jn()}]},percent:function(t){return i.translated(t)},translate:function(t,e){return[{transform:jn(-100*e*t)},{transform:jn(100*e*(1-t))}]}},translated:function(t){return Math.abs(e(t,"transform").split(",")[4]/t.offsetWidth)||0}}),c=function(t){var e=t.util,i=e.createEvent,n=e.clamp,o=e.css,s=e.Deferred,r=e.noop,a=e.Promise,l=e.Transition,h=e.trigger;function u(t,e,n){h(t,i(e,!1,!1,n))}return function(t,e,i,h){var c=h.animation,d=h.easing,f=c.percent,p=c.translate,m=c.show;void 0===m&&(m=r);var g=m(i),v=new s;return{dir:i,show:function(o,s,h){var c=this;void 0===s&&(s=0);var f=h?"linear":d;return o-=Math.round(o*n(s,-1,1)),this.translate(s),u(e,"itemin",{percent:s,duration:o,timing:f,dir:i}),u(t,"itemout",{percent:1-s,duration:o,timing:f,dir:i}),a.all([l.start(e,g[1],o,f),l.start(t,g[0],o,f)]).then(function(){c.reset(),v.resolve()},r),v.promise},stop:function(){return l.stop([e,t])},cancel:function(){l.cancel([e,t])},reset:function(){for(var i in g[0])o([e,t],i,"")},forward:function(i,n){return void 0===n&&(n=this.percent()),l.cancel([e,t]),this.show(i,n,!0)},translate:function(n){var s=p(n,i);o(e,s[1]),o(t,s[0]),u(e,"itemtranslatein",{percent:n,dir:i}),u(t,"itemtranslateout",{percent:1-n,dir:i})},percent:function(){return f(t||e,e,i)},getDistance:function(){return t.offsetWidth}}}}(t);t.mixin.slideshow={mixins:[n.slider],props:{animation:String},defaults:{animation:"slide",clsActivated:"uk-transition-active",Animations:u,Transitioner:c},computed:{animation:function(t){var e=t.animation,i=t.Animations;return r(e in i?i[e]:i.slide,{name:e})},transitionOptions:function(){return{animation:this.animation}}},events:{"itemshow itemhide itemshown itemhidden":function(e){var i=e.target;t.update(null,i)},itemshow:function(){l(this.prevIndex)&&a.flush()},beforeitemshow:function(t){var e=t.target;s(e,this.clsActive)},itemshown:function(t){var e=t.target;s(e,this.clsActivated)},itemhidden:function(t){var e=t.target;h(e,this.clsActive,this.clsActivated)}}}}}function qn(t){if(!qn.installed){t.use(Yn);var e,i,n,o,s,r=t.mixin,a=t.util,l=a.$,h=a.addClass,u=a.ajax,c=a.append,d=a.assign,f=a.attr,p=a.css,m=a.doc,g=a.getImage,v=a.html,w=a.index,b=a.on,y=a.pointerDown,x=a.pointerMove,k=a.removeClass,$=a.Transition,I=a.trigger,T=(i=(e=t).mixin,n=e.util,o=n.assign,s=n.css,o({},i.slideshow.defaults.Animations,{fade:{show:function(){return[{opacity:0},{opacity:1}]},percent:function(t){return 1-s(t,"opacity")},translate:function(t){return[{opacity:1-t},{opacity:t}]}},scale:{show:function(){return[{opacity:0,transform:Fn(.8)},{opacity:1,transform:Fn(1)}]},percent:function(t){return 1-s(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:Fn(1-.2*t)},{opacity:t,transform:Fn(.8+.2*t)}]}}}));t.component("lightbox-panel",{mixins:[r.container,r.modal,r.togglable,r.slideshow],functional:!0,defaults:{preload:1,videoAutoplay:!1,delayControls:3e3,items:[],cls:"uk-open",clsPage:"uk-lightbox-page",selList:".uk-lightbox-items",attrItem:"uk-lightbox-item",selClose:".uk-close-large",pauseOnHover:!1,velocity:2,Animations:T,template:'<div class="uk-lightbox uk-overflow-hidden"> <ul class="uk-lightbox-items"></ul> <div class="uk-lightbox-toolbar uk-position-top uk-text-right uk-transition-slide-top uk-transition-opaque"> <button class="uk-lightbox-toolbar-icon uk-close-large" type="button" uk-close></button> </div> <a class="uk-lightbox-button uk-position-center-left uk-position-medium uk-transition-fade" href="#" uk-slidenav-previous uk-lightbox-item="previous"></a> <a class="uk-lightbox-button uk-position-center-right uk-position-medium uk-transition-fade" href="#" uk-slidenav-next uk-lightbox-item="next"></a> <div class="uk-lightbox-toolbar uk-lightbox-caption uk-position-bottom uk-text-center uk-transition-slide-bottom uk-transition-opaque"></div> </div>'},created:function(){var t=this;this.$mount(c(this.container,this.template)),this.caption=l(".uk-lightbox-caption",this.$el),this.items.forEach(function(){return c(t.list,"<li></li>")})},events:[{name:x+" "+y+" keydown",handler:"showControls"},{name:"click",self:!0,delegate:function(){return this.slidesSelector},handler:function(t){t.preventDefault(),this.hide()}},{name:"shown",self:!0,handler:"showControls"},{name:"hide",self:!0,handler:function(){this.hideControls(),k(this.slides,this.clsActive),$.stop(this.slides),delete this.index,delete this.percent,delete this._transitioner}},{name:"keyup",el:function(){return m},handler:function(t){if(this.isToggled(this.$el))switch(t.keyCode){case 37:this.show("previous");break;case 39:this.show("next")}}},{name:"beforeitemshow",handler:function(t){this.isToggled()||(this.preventCatch=!0,t.preventDefault(),this.animation=T.scale,k(t.target,this.clsActive),this.stack.splice(1,0,this.index),this.toggleNow(this.$el,!0))}},{name:"itemshow",handler:function(t){var e=t.target,i=w(e),n=this.getItem(i).caption;p(this.caption,"display",n?"":"none"),v(this.caption,n);for(var o=0;o<=this.preload;o++)this.loadItem(this.getIndex(i+o)),this.loadItem(this.getIndex(i-o));delete this._computeds.animation}},{name:"itemshown",handler:function(){this.preventCatch=!1}},{name:"itemload",handler:function(t,e){var i,n=this,o=e.source,s=e.type,r=e.alt;if(this.setItem(e,"<span uk-spinner></span>"),o)if("image"===s||o.match(/\.(jp(e)?g|png|gif|svg)$/i))g(o).then(function(t){return n.setItem(e,'<img width="'+t.width+'" height="'+t.height+'" src="'+o+'" alt="'+(r||"")+'">')},function(){return n.setError(e)});else if("video"===s||o.match(/\.(mp4|webm|ogv)$/i)){var a=l("<video controls playsinline"+(e.poster?' poster="'+e.poster+'"':"")+' uk-video="autoplay: '+this.videoAutoplay+'"></video>');f(a,"src",o),b(a,"error",function(){return n.setError(e)}),b(a,"loadedmetadata",function(){f(a,{width:a.videoWidth,height:a.videoHeight}),n.setItem(e,a)})}else if("iframe"===s)this.setItem(e,'<iframe class="uk-lightbox-iframe" src="'+o+'" frameborder="0" allowfullscreen></iframe>');else if(i=o.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/watch\?v=([^&\s]+)/)||o.match(/(y)outu\.be\/(.*)/)){var h=i[2],c=function(t,o){return void 0===t&&(t=640),void 0===o&&(o=450),n.setItem(e,C("//www.youtube"+(i[1]||"")+".com/embed/"+h,t,o,n.videoAutoplay))};g("//img.youtube.com/vi/"+h+"/maxresdefault.jpg").then(function(t){var e=t.width,i=t.height;120===e&&90===i?g("//img.youtube.com/vi/"+h+"/0.jpg").then(function(t){var e=t.width,i=t.height;return c(e,i)},c):c(e,i)},c)}else(i=o.match(/(\/\/.*?)vimeo\.[a-z]+\/([0-9]+).*?/))&&u("//vimeo.com/api/oembed.json?maxwidth=1920&url="+encodeURI(o),{responseType:"json"}).then(function(t){var o=t.response,s=o.height,r=o.width;return n.setItem(e,C("//player.vimeo.com/video/"+i[2],r,s,n.videoAutoplay))})}}],methods:{loadItem:function(t){void 0===t&&(t=this.index);var e=this.getItem(t);e.content||I(this.$el,"itemload",[e])},getItem:function(t){return void 0===t&&(t=this.index),this.items[t]||{}},setItem:function(e,i){d(e,{content:i});var n=v(this.slides[this.items.indexOf(e)],i);I(this.$el,"itemloaded",[this,n]),t.update(null,n)},setError:function(t){this.setItem(t,'<span uk-icon="icon: bolt; ratio: 2"></span>')},showControls:function(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),h(this.$el,"uk-active","uk-transition-active")},hideControls:function(){k(this.$el,"uk-active","uk-transition-active")}}})}function C(t,e,i,n){return'<iframe src="'+t+'" width="'+e+'" height="'+i+'" style="max-width: 100%; box-sizing: border-box;" frameborder="0" allowfullscreen uk-video="autoplay: '+n+'" uk-responsive></iframe>'}}function Un(t){if(!Un.installed){var e=t.mixin,i=t.util,n=i.css,o=i.Dimensions,s=i.each,r=i.getImage,a=i.includes,l=i.isNumber,h=i.isUndefined,u=i.toFloat,c=i.win,d=["x","y","bgx","bgy","rotate","scale","color","backgroundColor","borderColor","opacity","blur","hue","grayscale","invert","saturate","sepia","fopacity"];e.parallax={props:d.reduce(function(t,e){return t[e]="list",t},{media:"media"}),defaults:d.reduce(function(t,e){return t[e]=void 0,t},{media:!1}),computed:{props:function(t,e){var i=this;return d.reduce(function(o,s){if(h(t[s]))return o;var r,l,c,d=s.match(/color/i),f=d||"opacity"===s,p=t[s].slice(0);f&&n(e,s,""),p.length<2&&p.unshift(("scale"===s?1:f?n(e,s):0)||0);var m=a(p.join(""),"%")?"%":"px";if(d){var g=e.style.color;p=p.map(function(t){return n(n(e,"color",t),"color").split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(function(t){return u(t)})}),e.style.color=g}else p=p.map(u);if(s.match(/^bg/))if(n(e,"background-position-"+s[2],""),l=n(e,"backgroundPosition").split(" ")["x"===s[2]?0:1],i.covers){var v=Math.min.apply(Math,p),w=Math.max.apply(Math,p),b=p.indexOf(v)<p.indexOf(w);c=w-v,p=p.map(function(t){return t-(b?v:w)}),r=(b?-c:0)+"px"}else r=l;return o[s]={steps:p,unit:m,pos:r,bgPos:l,diff:c},o},{})},bgProps:function(){var t=this;return["bgx","bgy"].filter(function(e){return e in t.props})},covers:function(t,e){return"cover"===n(""!==e.style.backgroundSize?n(e,"backgroundSize",""):e,"backgroundSize")}},disconnected:function(){delete this._image},update:[{read:function(t){var e=this;if(this._resetComputeds(),t.active=!this.media||c.matchMedia(this.media).matches,t.image&&(t.image.dimEl={width:this.$el.offsetWidth,height:this.$el.offsetHeight}),!("image"in t)&&this.covers&&this.bgProps.length){var i=n(this.$el,"backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");i&&(t.image=!1,r(i).then(function(i){t.image={width:i.naturalWidth,height:i.naturalHeight},e.$emit()}))}},write:function(t){var e=this,i=t.image,s=t.active;if(i)if(s){var r=i.dimEl,a=o.cover(i,r);this.bgProps.forEach(function(t){var n=e.props[t],s=n.diff,l=n.bgPos,h=n.steps,u="bgy"===t?"height":"width",c=a[u]-r[u];l.match(/%$|0px/)&&(c<s?r[u]=a[u]+s-c:c>s&&(l=parseFloat(l))&&(e.props[t].steps=h.map(function(t){return t-(c-s)/(100/l)})),a=o.cover(i,r))}),n(this.$el,{backgroundSize:a.width+"px "+a.height+"px",backgroundRepeat:"no-repeat"})}else n(this.$el,{backgroundSize:"",backgroundRepeat:""})},events:["load","resize"]}],methods:{reset:function(){var t=this;s(this.getCss(0),function(e,i){return n(t.$el,i,"")})},getCss:function(t){var e=!1,i=this.props;return Object.keys(i).reduce(function(n,o){var s=i[o],r=s.steps,a=s.unit,l=s.pos,h=p(r,t);switch(o){case"x":case"y":if(e)break;var c=["x","y"].map(function(e){return o===e?h+a:i[e]?p(i[e].steps,t)+i[e].unit:0}),d=c[0],m=c[1];e=n.transform+=" translate3d("+d+", "+m+", 0)";break;case"rotate":n.transform+=" rotate("+h+"deg)";break;case"scale":n.transform+=" scale("+h+")";break;case"bgy":case"bgx":n["background-position-"+o[2]]="calc("+l+" + "+(h+a)+")";break;case"color":case"backgroundColor":case"borderColor":var g=f(r,t),v=g[0],w=g[1],b=g[2];n[o]="rgba("+v.map(function(t,e){return t+=b*(w[e]-t),3===e?u(t):parseInt(t,10)}).join(",")+")";break;case"blur":n.filter+=" blur("+h+"px)";break;case"hue":n.filter+=" hue-rotate("+h+"deg)";break;case"fopacity":n.filter+=" opacity("+h+"%)";break;case"grayscale":case"invert":case"saturate":case"sepia":n.filter+=" "+o+"("+h+"%)";break;default:n[o]=h}return n},{transform:"",filter:""})}}}}function f(t,e){var i=t.length-1,n=Math.min(Math.floor(i*e),i-1),o=t.slice(n,n+2);return o.push(1===e?1:e%(1/i)*i),o}function p(t,e){var i=f(t,e),n=i[0],o=i[1],s=i[2];return(l(n)?n+Math.abs(n-o)*s*(n<o?1:-1):+o).toFixed(2)}}function Xn(t){var e=t.util,i=e.fastdom,n=e.removeClass;return{ready:function(){var t=this;i.write(function(){return t.show(t.getValidIndex())})},update:[{read:function(){this._resetComputeds()},write:function(){if(!this.stack.length&&!this.dragging){var t=this.getValidIndex();delete this.index,n(this.slides,this.clsActive,this.clsActivated),this.show(t)}},events:["load","resize"]}]}}function Jn(t,e){t.use(Un);var i=t.mixin,n=t.util,o=n.closest,s=n.css,r=n.endsWith,a=n.noop,l=n.Transition;return{mixins:[i.parallax],computed:{item:function(){var i=t.getComponent(o(this.$el,".uk-"+e),e);return i&&o(this.$el,i.slidesSelector)}},events:[{name:"itemshown",self:!0,el:function(){return this.item},handler:function(){s(this.$el,this.getCss(.5))}},{name:"itemin itemout",self:!0,el:function(){return this.item},handler:function(t){var e=t.type,i=t.detail,n=i.percent,o=i.duration,r=i.timing,c=i.dir;l.cancel(this.$el),s(this.$el,this.getCss(u(e,c,n))),l.start(this.$el,this.getCss(h(e)?.5:c>0?1:0),o,r).catch(a)}},{name:"transitioncanceled transitionend",self:!0,el:function(){return this.item},handler:function(){l.cancel(this.$el)}},{name:"itemtranslatein itemtranslateout",self:!0,el:function(){return this.item},handler:function(t){var e=t.type,i=t.detail,n=i.percent,o=i.dir;l.cancel(this.$el),s(this.$el,this.getCss(u(e,o,n)))}}]};function h(t){return r(t,"in")}function u(t,e,i){return i/=2,h(t)?e<0?1-i:i:e<0?i:1-i}}return zi.version="3.0.0-beta.39",(Mn=zi).mixin.class=ji,Mn.mixin.container=Fi,Mn.mixin.modal=Ri,Mn.mixin.position=Yi,Mn.mixin.togglable=Vi,(Bn=zi).use(Wn),Bn.use(qi),Bn.use(Ui),Bn.use(Ln),Bn.use(Ji),Bn.use(Gi),Bn.use(Zi),Bn.use(Qi),Bn.use(en),Bn.use(nn),Bn.use($n),Bn.use(Ki),Bn.use(tn),Bn.use(kn),Bn.use(In),Bn.use(Tn),Bn.use(Cn),Bn.use(En),Bn.use(_n),Bn.use(Sn),Bn.use(An),Bn.use(Nn),Bn.use(Dn),Bn.use(Pn),Bn.use(xn),Bn.use(Hn),Bn.use(zn),Bn.use(Xi),zi.use(function t(e){if(!t.installed){var i=e.util,n=i.$,o=i.doc,s=i.empty,r=i.html;e.component("countdown",{mixins:[e.mixin.class],attrs:!0,props:{date:String,clsWrapper:String},defaults:{date:"",clsWrapper:".uk-countdown-%unit%"},computed:{date:function(t){var e=t.date;return Date.parse(e)},days:function(t,e){var i=t.clsWrapper;return n(i.replace("%unit%","days"),e)},hours:function(t,e){var i=t.clsWrapper;return n(i.replace("%unit%","hours"),e)},minutes:function(t,e){var i=t.clsWrapper;return n(i.replace("%unit%","minutes"),e)},seconds:function(t,e){var i=t.clsWrapper;return n(i.replace("%unit%","seconds"),e)},units:function(){var t=this;return["days","hours","minutes","seconds"].filter(function(e){return t[e]})}},connected:function(){this.start()},disconnected:function(){var t=this;this.stop(),this.units.forEach(function(e){return s(t[e])})},events:[{name:"visibilitychange",el:o,handler:function(){o.hidden?this.stop():this.start()}}],update:{write:function(){var t,e,i=this,n=(t=this.date,{total:e=t-Date.now(),seconds:e/1e3%60,minutes:e/1e3/60%60,hours:e/1e3/60/60%24,days:e/1e3/60/60/24});n.total<=0&&(this.stop(),n.days=n.hours=n.minutes=n.seconds=0),this.units.forEach(function(t){var e=String(Math.floor(n[t]));e=e.length<2?"0"+e:e;var o=i[t];o.textContent!==e&&((e=e.split("")).length!==o.children.length&&r(o,e.map(function(){return"<span></span>"}).join("")),e.forEach(function(t,e){return o.children[e].textContent=t}))})}},methods:{start:function(){var t=this;this.stop(),this.date&&this.units.length&&(this.$emit(),this.timer=setInterval(function(){return t.$emit()},1e3))},stop:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}}})}}),zi.use(function t(e){if(!t.installed){var i=e.util,n=i.addClass,o=i.css,s=i.scrolledOver,r=i.sortBy,a=i.toFloat;e.component("grid-parallax",e.components.grid.extend({props:{target:String,translate:Number},defaults:{target:!1,translate:150},computed:{translate:function(t){var e=t.translate;return Math.abs(e)}},init:function(){n(this.$el,"uk-grid")},disconnected:function(){this.reset(),o(this.$el,"marginBottom","")},update:[{read:function(t){var e=t.rows;return{columns:e&&e[0]&&e[0].length||0,rows:e&&e.map(function(t){return r(t,"offsetLeft")})}},write:function(t){var e=t.columns;o(this.$el,"marginBottom",e>1?this.translate+a(o(o(this.$el,"marginBottom",""),"marginBottom")):"")},events:["load","resize"]},{read:function(){return{scrolled:s(this.$el)*this.translate}},write:function(t){var e=t.rows,i=t.columns,n=t.scrolled;if(!e||1===i||!n)return this.reset();e.forEach(function(t){return t.forEach(function(t,e){return o(t,"transform","translateY("+(e%2?n:n/8)+"px)")})})},events:["scroll","load","resize"]}],methods:{reset:function(){o(this.$el.children,"transform","")}}})),e.components.gridParallax.options.update.unshift({read:function(){this.reset()},events:["load","resize"]})}}),zi.use(function t(e){if(!t.installed){e.use(qn);var i=e.util,n=i.$$,o=i.assign,s=i.data,r=i.index,a=e.components.lightboxPanel.options;e.component("lightbox",{attrs:!0,props:o({toggle:String},a.props),defaults:o({toggle:"a"},Object.keys(a.props).reduce(function(t,e){return t[e]=a.defaults[e],t},{})),computed:{toggles:function(t,e){var i=t.toggle;return n(i,e)}},disconnected:function(){this._destroy()},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),t.current.blur(),this.show(r(this.toggles,t.current))}}],update:function(t){var e,i;this.panel&&this.animation&&(this.panel.$props.animation=this.animation,this.panel.$emit()),!this.panel||t.toggles&&(e=t.toggles,i=this.toggles,e.length===i.length&&e.every(function(t,e){return t!==i[e]}))||(t.toggles=this.toggles,this._destroy(),this._init())},methods:{_init:function(){return this.panel=this.panel||e.lightboxPanel(o({},this.$props,{items:this.toggles.reduce(function(t,e){return t.push(["href","caption","type","poster","alt"].reduce(function(t,i){return t["href"===i?"source":i]=s(e,i),t},{})),t},[])}))},_destroy:function(){this.panel&&(this.panel.$destroy(!0),this.panel=null)},show:function(t){return this.panel||this._init(),this.panel.show(t)},hide:function(){return this.panel&&this.panel.hide()}}})}}),zi.use(function t(e){var i;if(!t.installed){var n=e.util,o=n.append,s=n.closest,r=n.css,a=n.each,l=n.pointerEnter,h=n.pointerLeave,u=n.remove,c=n.toFloat,d=n.Transition,f=n.trigger,p={};e.component("notification",{functional:!0,args:["message","status"],defaults:{message:"",status:"",timeout:5e3,group:null,pos:"top-center",clsClose:"uk-notification-close",clsMsg:"uk-notification-message"},created:function(){p[this.pos]||(p[this.pos]=o(e.container,'<div class="uk-notification uk-notification-'+this.pos+'"></div>'));var t=r(p[this.pos],"display","block");this.$mount(o(t,'<div class="'+this.clsMsg+(this.status?" "+this.clsMsg+"-"+this.status:"")+'"> <a href="#" class="'+this.clsClose+'" data-uk-close></a> <div>'+this.message+"</div> </div>"))},ready:function(){var t=this,e=c(r(this.$el,"marginBottom"));d.start(r(this.$el,{opacity:0,marginTop:-this.$el.offsetHeight,marginBottom:0}),{opacity:1,marginTop:0,marginBottom:e}).then(function(){t.timeout&&(t.timer=setTimeout(t.close,t.timeout))})},events:(i={click:function(t){s(t.target,'a[href="#"]')&&t.preventDefault(),this.close()}},i[l]=function(){this.timer&&clearTimeout(this.timer)},i[h]=function(){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},i),methods:{close:function(t){var e=this,i=function(){f(e.$el,"close",[e]),u(e.$el),p[e.pos].children.length||r(p[e.pos],"display","none")};this.timer&&clearTimeout(this.timer),t?i():d.start(this.$el,{opacity:0,marginTop:-this.$el.offsetHeight,marginBottom:0}).then(i)}}}),e.notification.closeAll=function(t,i){a(e.instances,function(e){"notification"!==e.$options.name||t&&t!==e.group||e.close(i)})}}}),zi.use(function t(e){if(!t.installed){e.use(Un);var i=e.mixin,n=e.util,o=n.clamp,s=n.css,r=n.scrolledOver,a=n.query;e.component("parallax",{mixins:[i.parallax],props:{target:String,viewport:Number,easing:Number},defaults:{target:!1,viewport:1,easing:1},computed:{target:function(t,e){var i=t.target;return i&&a(i,e)||e}},update:[{read:function(t){var e,i;return{prev:t.percent,percent:(e=r(this.target)/(this.viewport||1),i=this.easing,o(e*(1-(i-i*e))))}},write:function(t,e){var i=t.prev,n=t.percent,o=t.active;"scroll"!==e.type&&(i=!1),o?i!==n&&s(this.$el,this.getCss(n)):this.reset()},events:["scroll","load","resize"]}]})}}),zi.use(function t(e){if(!t.installed){e.use(Vn);var i=e.mixin,n=e.util,o=n.$,s=n.$$,r=n.addClass,a=n.css,l=n.data,h=n.includes,u=n.isNumeric,c=n.isUndefined,d=n.toggleClass,f=n.toFloat,p=function(t){var e=t.util,i=e.assign,n=e.clamp,o=e.createEvent,s=e.css,r=e.Deferred,a=e.includes,l=e.index,h=e.isRtl,u=e.noop,c=e.sortBy,d=e.toNodes,f=e.Transition,p=e.trigger,m=i(function(t,e,i,o){var d=o.center,p=o.easing,w=o.list,b=new r,y=t?m.getLeft(t,w,d):m.getLeft(e,w,d)+e.offsetWidth*i,x=e?m.getLeft(e,w,d):y+t.offsetWidth*i*(h?-1:1);return{dir:i,show:function(e,o,s){void 0===o&&(o=0);var r=s?"linear":p;return e-=Math.round(e*n(o,-1,1)),this.translate(o),t&&this.updateTranslates(),o=t?o:n(o,0,1),g(this.getItemIn(),"itemin",{percent:o,duration:e,timing:r,dir:i}),t&&g(this.getItemIn(!0),"itemout",{percent:1-o,duration:e,timing:r,dir:i}),f.start(w,{transform:jn(-x*(h?-1:1),"px")},e,r).then(b.resolve,u),b.promise},stop:function(){return f.stop(w)},cancel:function(){f.cancel(w)},reset:function(){s(w,"transform","")},forward:function(t,e){return void 0===e&&(e=this.percent()),f.cancel(w),this.show(t,e,!0)},translate:function(e){var o=this.getDistance()*i*(h?-1:1);s(w,"transform",jn(n(o-o*e-x,-m.getWidth(w),w.offsetWidth)*(h?-1:1),"px")),this.updateTranslates(),t&&(e=n(e,-1,1),g(this.getItemIn(),"itemtranslatein",{percent:e,dir:i}),g(this.getItemIn(!0),"itemtranslateout",{percent:1-e,dir:i}))},percent:function(){return Math.abs((s(w,"transform").split(",")[4]*(h?-1:1)+y)/(x-y))},getDistance:function(){return Math.abs(x-y)},getItemIn:function(e){void 0===e&&(e=!1);var n=this.getActives(),o=c(v(w),"offsetLeft"),s=l(o,n[i*(e?-1:1)>0?n.length-1:0]);return~s&&o[s+(t&&!e?i:0)]},getActives:function(){var i=m.getLeft(t||e,w,d);return c(v(w).filter(function(t){var e=m.getElLeft(t,w);return e>=i&&e+t.offsetWidth<=w.offsetWidth+i}),"offsetLeft")},updateTranslates:function(){var t=this.getActives();v(w).forEach(function(i){var n=a(t,i);g(i,"itemtranslate"+(n?"in":"out"),{percent:n?1:0,dir:i.offsetLeft<=e.offsetLeft?1:-1})})}}},{getLeft:function(t,e,i){var n=this.getElLeft(t,e);return i?n-this.center(t,e):Math.min(n,this.getMax(e))},getMax:function(t){return Math.max(0,this.getWidth(t)-t.offsetWidth)},getWidth:function(t){return v(t).reduce(function(t,e){return e.offsetWidth+t},0)},getMaxWidth:function(t){return v(t).reduce(function(t,e){return Math.max(t,e.offsetWidth)},0)},center:function(t,e){return e.offsetWidth/2-t.offsetWidth/2},getElLeft:function(t,e){return(t.offsetLeft+(h?t.offsetWidth-e.offsetWidth:0))*(h?-1:1)}});function g(t,e,i){p(t,o(e,!1,!1,i))}function v(t){return d(t.children)}return m}(e);e.component("slider-parallax",Jn(e,"slider")),e.component("slider",{mixins:[i.class,i.slider,Xn(e)],props:{center:Boolean,sets:Boolean},defaults:{center:!1,sets:!1,attrItem:"uk-slider-item",selList:".uk-slider-items",selNav:".uk-slider-nav",clsContainer:"uk-slider-container",Transitioner:p},computed:{avgWidth:function(){return p.getWidth(this.list)/this.length},finite:function(t){var e=t.finite;return e||p.getWidth(this.list)<this.list.offsetWidth+p.getMaxWidth(this.list)+this.center},maxIndex:function(){if(!this.finite||this.center&&!this.sets)return this.length-1;if(this.center)return this.sets[this.sets.length-1];a(this.slides,"order","");for(var t=p.getMax(this.list),e=this.length;e--;)if(p.getElLeft(this.list.children[e],this.list)<t)return Math.min(e+1,this.length-1);return 0},sets:function(t){var e=this,i=t.sets,n=this.list.offsetWidth/(this.center?2:1),o=0,s=n;return a(this.slides,"order",""),i=i&&this.slides.reduce(function(t,i,r){var a=i.offsetWidth,l=p.getElLeft(i,e.list);if(l+a>o&&(!e.center&&r>e.maxIndex&&(r=e.maxIndex),!h(t,r))){var u=e.slides[r+1];e.center&&u&&a<s-u.offsetWidth/2?s-=a:(s=n,t.push(r),o=l+n+(e.center?a/2:0))}return t},[]),i&&i.length&&i},transitionOptions:function(){return{center:this.center,list:this.list}}},connected:function(){d(this.$el,this.clsContainer,!o("."+this.clsContainer,this.$el))},update:{write:function(){var t=this;s("["+this.attrItem+"],[data-"+this.attrItem+"]",this.$el).forEach(function(e){var i=l(e,t.attrItem);t.maxIndex&&d(e,"uk-hidden",u(i)&&(t.sets&&!h(t.sets,f(i))||i>t.maxIndex))})},events:["load","resize"]},events:{beforeitemshow:function(t){!this.dragging&&this.sets&&this.stack.length<2&&!h(this.sets,this.index)&&(this.index=this.getValidIndex());var e=Math.abs(this.index-this.prevIndex+(this.dir>0&&this.index<this.prevIndex||this.dir<0&&this.index>this.prevIndex?(this.maxIndex+1)*this.dir:0));if(!this.dragging&&e>1){for(var i=0;i<e;i++)this.stack.splice(1,0,this.dir>0?"next":"previous");t.preventDefault()}else this.duration=Rn(this.avgWidth/this.velocity)*((this.dir<0||!this.slides[this.prevIndex]?this.slides[this.index]:this.slides[this.prevIndex]).offsetWidth/this.avgWidth),this.reorder()},itemshow:function(){!c(this.prevIndex)&&r(this._getTransitioner().getItemIn(),this.clsActive)},itemshown:function(){var t=this,e=this._getTransitioner(this.index).getActives();this.slides.forEach(function(i){return d(i,t.clsActive,h(e,i))}),(!this.sets||h(this.sets,f(this.index)))&&this.slides.forEach(function(i){return d(i,t.clsActivated,h(e,i))})}},methods:{reorder:function(){var t=this;if(a(this.slides,"order",""),!this.finite&&(this.slides.forEach(function(e,i){return a(e,"order",t.dir>0&&i<t.prevIndex?1:t.dir<0&&i>=t.index?-1:"")}),this.center))for(var e=this.dir>0&&this.slides[this.prevIndex]?this.prevIndex:this.index,i=this.slides[e],n=this.list.offsetWidth/2-i.offsetWidth/2,o=0;n>0;){var s=t.getIndex(--o+e,e),r=t.slides[s];a(r,"order",s>e?-2:-1),n-=r.offsetWidth}},getValidIndex:function(t,e){var i;if(void 0===t&&(t=this.index),void 0===e&&(e=this.prevIndex),t=this.getIndex(t,e),!this.sets)return t;do{if(h(this.sets,t))return t;i=t,t=this.getIndex(t+this.dir,e)}while(t!==i);return t}}})}}),zi.use(function t(e){if(!t.installed){e.use(Yn);var i,n,o,s,r,a,l=e.mixin,h=e.util.height,u=(n=(i=e).mixin,o=i.util,s=o.assign,r=o.css,a=s({},n.slideshow.defaults.Animations,{fade:{show:function(){return[{opacity:0,zIndex:0},{zIndex:-1}]},percent:function(t){return 1-r(t,"opacity")},translate:function(t){return[{opacity:1-t,zIndex:0},{zIndex:-1}]}},scale:{show:function(){return[{opacity:0,transform:Fn(1.5),zIndex:0},{zIndex:-1}]},percent:function(t){return 1-r(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:Fn(1+.5*t),zIndex:0},{zIndex:-1}]}},pull:{show:function(t){return t<0?[{transform:jn(30),zIndex:-1},{transform:jn(),zIndex:0}]:[{transform:jn(-100),zIndex:0},{transform:jn(),zIndex:-1}]},percent:function(t,e,i){return i<0?1-a.translated(e):a.translated(t)},translate:function(t,e){return e<0?[{transform:jn(30*t),zIndex:-1},{transform:jn(-100*(1-t)),zIndex:0}]:[{transform:jn(100*-t),zIndex:0},{transform:jn(30*(1-t)),zIndex:-1}]}},push:{show:function(t){return t<0?[{transform:jn(100),zIndex:0},{transform:jn(),zIndex:-1}]:[{transform:jn(-30),zIndex:-1},{transform:jn(),zIndex:0}]},percent:function(t,e,i){return i>0?1-a.translated(e):a.translated(t)},translate:function(t,e){return e<0?[{transform:jn(100*t),zIndex:0},{transform:jn(-30*(1-t)),zIndex:-1}]:[{transform:jn(-30*t),zIndex:-1},{transform:jn(100*(1-t)),zIndex:0}]}}}));e.component("slideshow-parallax",Jn(e,"slideshow")),e.component("slideshow",{mixins:[l.class,l.slideshow,Xn(e)],props:{ratio:String,minHeight:Boolean,maxHeight:Boolean},defaults:{ratio:"16:9",minHeight:!1,maxHeight:!1,selList:".uk-slideshow-items",attrItem:"uk-slideshow-item",selNav:".uk-slideshow-nav",Animations:u},update:{read:function(){var t=this.ratio.split(":").map(Number),e=t[0],i=t[1];return i=i*this.$el.offsetWidth/e,this.minHeight&&(i=Math.max(this.minHeight,i)),this.maxHeight&&(i=Math.min(this.maxHeight,i)),{height:i}},write:function(t){var e=t.height;h(this.list,Math.floor(e))},events:["load","resize"]}})}}),zi.use(function t(e){var i;if(!t.installed){var n=e.mixin,o=e.util,s=o.addClass,r=o.after,a=o.assign,l=o.append,h=o.attr,u=o.before,c=o.closest,d=o.css,f=o.doc,p=o.docEl,m=o.height,g=o.fastdom,v=o.getPos,w=o.includes,b=o.index,y=o.isInput,x=o.noop,k=o.offset,$=o.off,I=o.on,T=o.pointerDown,C=o.pointerMove,E=o.pointerUp,_=o.position,S=o.preventClick,A=o.Promise,N=o.remove,D=o.removeClass,M=o.toggleClass,B=o.toNodes,O=o.Transition,P=o.trigger,H=o.win,z=o.within;e.component("sortable",{mixins:[n.class],props:{group:String,animation:Number,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},defaults:{group:!1,animation:150,threshold:5,clsItem:"uk-sortable-item",clsPlaceholder:"uk-sortable-placeholder",clsDrag:"uk-sortable-drag",clsDragState:"uk-drag",clsBase:"uk-sortable",clsNoDrag:"uk-sortable-nodrag",clsEmpty:"uk-sortable-empty",clsCustom:"",handle:!1},init:function(){var t=this;["init","start","move","end"].forEach(function(e){var i=t[e];t[e]=function(e){t.scrollY=H.pageYOffset;var n=v(e),o=n.x,s=n.y;t.pos={x:o,y:s},i(e)}})},events:(i={},i[T]="init",i),update:{write:function(){if(this.clsEmpty&&M(this.$el,this.clsEmpty,!this.$el.children.length),this.drag){k(this.drag,{top:this.pos.y+this.origin.top,left:this.pos.x+this.origin.left});var t,e=k(this.drag).top,i=e+this.drag.offsetHeight;e>0&&e<this.scrollY?t=this.scrollY-5:i<m(f)&&i>m(H)+this.scrollY&&(t=this.scrollY+5),t&&setTimeout(function(){return H.scrollTo(H.scrollX,t)},5)}}},methods:{init:function(t){var e=t.target,i=t.button,n=t.defaultPrevented,o=B(this.$el.children).filter(function(t){return z(e,t)})[0];!o||y(t.target)||this.handle&&!z(e,this.handle)||i>0||z(e,"."+this.clsNoDrag)||n||(t.preventDefault(),this.touched=[this],this.placeholder=o,this.origin=a({target:e,index:b(o)},this.pos),I(p,C,this.move),I(p,E,this.end),I(H,"scroll",this.scroll),this.threshold||this.start(t))},start:function(t){this.drag=l(e.container,this.placeholder.outerHTML.replace(/^<li/i,"<div").replace(/li>$/i,"div>")),d(this.drag,a({boxSizing:"border-box",width:this.placeholder.offsetWidth,height:this.placeholder.offsetHeight},d(this.placeholder,["paddingLeft","paddingRight","paddingTop","paddingBottom"]))),h(this.drag,"uk-no-boot",""),s(this.drag,this.clsDrag,this.clsCustom),m(this.drag.firstElementChild,m(this.placeholder.firstElementChild));var i=k(this.placeholder),n=i.left,o=i.top;a(this.origin,{left:n-this.pos.x,top:o-this.pos.y}),s(this.placeholder,this.clsPlaceholder),s(this.$el.children,this.clsItem),s(p,this.clsDragState),P(this.$el,"start",[this,this.placeholder,this.drag]),this.move(t)},move:function(t){if(this.drag){this.$emit();var e="mousemove"===t.type?t.target:f.elementFromPoint(this.pos.x-f.body.scrollLeft,this.pos.y-f.body.scrollTop),i=W(e),n=W(this.placeholder),o=i!==n;if(i&&!z(e,this.placeholder)&&(!o||i.group&&i.group===n.group)){if(e=i.$el===e.parentNode&&e||B(i.$el.children).filter(function(t){return z(e,t)})[0],o)n.remove(this.placeholder);else if(!e)return;i.insert(this.placeholder,e),w(this.touched,i)||this.touched.push(i)}}else(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t)},scroll:function(){var t=H.pageYOffset;t!==this.scrollY&&(this.pos.y+=t-this.scrollY,this.scrollY=t,this.$emit())},end:function(t){if($(p,C,this.move),$(p,E,this.end),$(H,"scroll",this.scroll),this.drag){S();var e=W(this.placeholder);this===e?this.origin.index!==b(this.placeholder)&&P(this.$el,"moved",[this,this.placeholder]):(P(e.$el,"added",[e,this.placeholder]),P(this.$el,"removed",[this,this.placeholder])),P(this.$el,"stop",[this]),N(this.drag),this.drag=null;var i=this.touched.map(function(t){return t.clsPlaceholder+" "+t.clsItem}).join(" ");this.touched.forEach(function(t){return D(t.$el.children,i)}),D(p,this.clsDragState)}else"mouseup"!==t.type&&z(t.target,"a[href]")&&(location.href=c(t.target,"a[href]").href)},insert:function(t,e){var i=this;s(this.$el.children,this.clsItem);var n=function(){var n,o;e?!z(t,i.$el)||(o=e,(n=t).parentNode===o.parentNode&&b(n)>b(o))?u(e,t):r(e,t):l(i.$el,t)};this.animation?this.animate(n):n()},remove:function(t){z(t,this.$el)&&(this.animation?this.animate(function(){return N(t)}):N(t))},animate:function(t){var e=this,i=[],n=B(this.$el.children),o={position:"",width:"",height:"",pointerEvents:"",top:"",left:"",bottom:"",right:""};n.forEach(function(t){i.push(a({position:"absolute",pointerEvents:"none",width:t.offsetWidth,height:t.offsetHeight},_(t)))}),t(),n.forEach(O.cancel),d(this.$el.children,o),this.$update("update",!0),g.flush(),d(this.$el,"minHeight",m(this.$el));var s=n.map(function(t){return _(t)});A.all(n.map(function(t,n){return O.start(d(t,i[n]),s[n],e.animation)})).then(function(){d(e.$el,"minHeight",""),d(n,o),e.$update("update",!0),g.flush()},x)}}})}function W(t){return t&&(e.getComponent(t,"sortable")||W(t.parentNode))}}),zi.use(function t(e){var i;if(!t.installed){var n=e.util,o=e.mixin,s=n.append,r=n.attr,a=n.doc,l=n.flipPosition,h=n.hasAttr,u=n.includes,c=n.isTouch,d=n.isVisible,f=n.matches,p=n.on,m=n.pointerDown,g=n.pointerEnter,v=n.pointerLeave,w=n.remove,b=n.within,y=[];e.component("tooltip",{attrs:!0,args:"title",mixins:[o.container,o.togglable,o.position],props:{delay:Number,title:String},defaults:{pos:"top",title:"",delay:0,animation:["uk-animation-scale-up"],duration:100,cls:"uk-active",clsPos:"uk-tooltip"},beforeConnect:function(){this._hasTitle=h(this.$el,"title"),r(this.$el,{title:"","aria-expanded":!1})},disconnected:function(){this.hide(),r(this.$el,{title:this._hasTitle?this.title:null,"aria-expanded":null})},methods:{show:function(){var t=this;u(y,this)||(y.forEach(function(t){return t.hide()}),y.push(this),this._unbind=p(a,"click",function(e){return!b(e.target,t.$el)&&t.hide()}),clearTimeout(this.showTimer),this.tooltip=s(this.container,'<div class="'+this.clsPos+'" aria-hidden><div class="'+this.clsPos+'-inner">'+this.title+"</div></div>"),r(this.$el,"aria-expanded",!0),this.positionAt(this.tooltip,this.$el),this.origin="y"===this.getAxis()?l(this.dir)+"-"+this.align:this.align+"-"+l(this.dir),this.showTimer=setTimeout(function(){t.toggleElement(t.tooltip,!0),t.hideTimer=setInterval(function(){d(t.$el)||t.hide()},150)},this.delay))},hide:function(){var t=y.indexOf(this);!~t||f(this.$el,"input")&&this.$el===a.activeElement||(y.splice(t,1),clearTimeout(this.showTimer),clearInterval(this.hideTimer),r(this.$el,"aria-expanded",!1),this.toggleElement(this.tooltip,!1),this.tooltip&&w(this.tooltip),this.tooltip=!1,this._unbind())}},events:(i={},i["focus "+g+" "+m]=function(t){t.type===m&&c(t)||this.show()},i.blur="hide",i[v]=function(t){c(t)||this.hide()},i)})}}),zi.use(function t(e){if(!t.installed){var i=e.util,n=i.addClass,o=i.ajax,s=i.matches,r=i.noop,a=i.on,l=i.removeClass,h=i.trigger;e.component("upload",{props:{allow:String,clsDragover:String,concurrent:Number,maxSize:Number,mime:String,msgInvalidMime:String,msgInvalidName:String,msgInvalidSize:String,multiple:Boolean,name:String,params:Object,type:String,url:String},defaults:{allow:!1,clsDragover:"uk-dragover",concurrent:1,maxSize:0,mime:!1,msgInvalidMime:"Invalid File Type: %s",msgInvalidName:"Invalid File Name: %s",msgInvalidSize:"Invalid File Size: %s Bytes Max",multiple:!1,name:"files[]",params:{},type:"POST",url:"",abort:r,beforeAll:r,beforeSend:r,complete:r,completeAll:r,error:r,fail:r,load:r,loadEnd:r,loadStart:r,progress:r},events:{change:function(t){s(t.target,'input[type="file"]')&&(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop:function(t){c(t);var e=t.dataTransfer;e&&e.files&&(l(this.$el,this.clsDragover),this.upload(e.files))},dragenter:function(t){c(t)},dragover:function(t){c(t),n(this.$el,this.clsDragover)},dragleave:function(t){c(t),l(this.$el,this.clsDragover)}},methods:{upload:function(t){var e=this;if(t.length){h(this.$el,"upload",[t]);for(var i=0;i<t.length;i++){if(e.maxSize&&1e3*e.maxSize<t[i].size)return void e.fail(e.msgInvalidSize.replace("%s",e.allow));if(e.allow&&!u(e.allow,t[i].name))return void e.fail(e.msgInvalidName.replace("%s",e.allow));if(e.mime&&!u(e.mime,t[i].type))return void e.fail(e.msgInvalidMime.replace("%s",e.mime))}this.multiple||(t=[t[0]]),this.beforeAll(this,t);var n=function(t,e){for(var i=[],n=0;n<t.length;n+=e){for(var o=[],s=0;s<e;s++)o.push(t[n+s]);i.push(o)}return i}(t,this.concurrent),s=function(t){var i=new FormData;for(var r in t.forEach(function(t){return i.append(e.name,t)}),e.params)i.append(r,e.params[r]);o(e.url,{data:i,method:e.type,beforeSend:function(t){var i=t.xhr;i.upload&&a(i.upload,"progress",e.progress),["loadStart","load","loadEnd","abort"].forEach(function(t){return a(i,t.toLowerCase(),e[t])}),e.beforeSend(t)}}).then(function(t){e.complete(t),n.length?s(n.shift()):e.completeAll(t)},function(t){return e.error(t.message)})};s(n.shift())}}}})}function u(t,e){return e.match(new RegExp("^"+t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")+"$","i"))}function c(t){t.preventDefault(),t.stopPropagation()}}),function(t){var e=t.connect,i=t.disconnect;function n(){l(s.body,e),hi.flush(),new a(function(t){return t.forEach(o)}).observe(r,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),t._initialized=!0}function o(n){var o=n.target;("attributes"!==n.type?function(t){var n,o=t.addedNodes,s=t.removedNodes;for(n=0;n<o.length;n++)l(o[n],e);for(n=0;n<s.length;n++)l(s[n],i);return!0}(n):function(e){var i=e.target,n=e.attributeName;if("href"===n)return!0;var o=Di(n);if(o&&o in t.components){if(y(i,n))return t[o](i),!0;var s=t.getComponent(i,o);return s?(s.$destroy(),!0):void 0}}(n))&&t.update(ce("update",!0,!1,{mutation:!0}),o,!0)}function l(t,e){if(1===t.nodeType&&!y(t,"uk-no-boot"))for(e(t),t=t.firstElementChild;t;){var i=t.nextElementSibling;l(t,e),t=i}}a&&(s.body?n():new a(function(){s.body&&(this.disconnect(),n())}).observe(r,{childList:!0,subtree:!0}))}(zi),zi});