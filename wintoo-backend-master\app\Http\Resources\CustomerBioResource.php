<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomerBioResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'=>$this->id,
            'status'=>$this->status,
            'status_name'=>$this->status_name,
            'customer'=>new CustomerResource($this->customer),
            'date'=>\Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'time'=>\Carbon\Carbon::parse($this->created_at)->isoFormat('h:mm a'),
        ];
    }
}
