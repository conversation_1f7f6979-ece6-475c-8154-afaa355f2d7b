<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('orders', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('customer_id')->unsigned();
			$table->boolean('is_payment')->nullable()->default(0);
			$table->text('payment_method')->nullable();
			$table->text('ship_address')->nullable();
			$table->integer('address_id')->nullable();
			$table->integer('status')->nullable();
			$table->string('coupon_value', 191)->nullable();
			$table->integer('coupon_id')->nullable();
			$table->integer('sub_total')->nullable();
			$table->integer('total')->nullable();
			$table->timestamps();
			$table->integer('store_id')->nullable();
			$table->text('payment_info')->nullable();
			$table->string('delivery_cost', 191)->nullable()->default('0');
			$table->integer('currency_id')->nullable()->default(1);
			$table->string('delivery_reference_id', 191)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('orders');
	}

}
