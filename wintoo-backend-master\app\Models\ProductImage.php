<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON><PERSON>pi\LaravelUserActivity\Traits\Loggable;
class ProductImage extends Model
{
use \App\Traits\PropertyGetter;
    protected $connection = "mysql";
    protected $guarded=[];
    use HasFactory;
    use Loggable;

    protected $appends = ['image_url'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function temporaryUrl(){
        return asset('storage/product/'.$this->image);
    }
    public function getImageUrlAttribute(){
        if($this->image){
            return asset('storage/product/'.$this->image);
        }else{
            return asset('images/default.jpg');
        }
    }
}
