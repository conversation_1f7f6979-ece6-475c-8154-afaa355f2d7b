<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Maincolor extends Model
{
use \App\Traits\PropertyGetter;

 protected $guarded = [];
 public  static function  getColumnLang(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('maincolor.name') ,1,true,false,[]],
 	 	'code'=>[\Lang::get('maincolor.code') ,1,true,false,[]],
 	 	'human_name'=>[\Lang::get('maincolor.human_name') ,1,true,false,[]],
 	 	'category_id'=>[\Lang::get('maincolor.category_id') ,1,false,false,[]],
 	 	   'actions'=>['الخيارات',3,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('maincolor.name')],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["code"])){
 	 	   $query->where("code","LIKE","%".$data["code"]."%");}
 	 	 if(isset($data["human_name"])){
 	 	   $query->where("human_name","LIKE","%".$data["human_name"]."%");}
 	 	 if(isset($data["category_id"])){
 	 	   $query->where("category_id","LIKE","%".$data["category_id"]."%");}
 	 	 return $query ;
 	 	 }

}
