<?php

namespace App\Http\Resources;

use App\Models\Currency;
use App\Models\Product;
use App\Models\Size;
use Illuminate\Http\Resources\Json\JsonResource;

class SizeVariationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        //dump($this);
        $price =$this->display_selling_price;
        $product = Product::find($this->product_id);

        $new_price = $this->offer_type ? $this->price_after_offer : $this->display_selling_price ;
        $new_price = $this->price_after_offer ;


        return [
            'variation_id'=>$this->id,
            'id'=>is_null($this->product_size)?Size::find($this->product_size_id)->id:Size::where('name',$this->product_size)->first()->id,
            'size'=>is_null($this->product_size)?Size::find($this->product_size_id)->size_ar:Size::where('name',$this->product_size)->first()->size_ar,
            'price'=>getRound($price ),
            'new_price'=>getRound($new_price),
            "charged_price"=> getRound(getPriceForUser($price,Currency::find($product->currency_id))),
            "charged_new_price"=>getRound(getPriceForUser($new_price,Currency::find($product->currency_id))),
            'charged_currency'=> new CurrencyResource(getCurrentUserCurrency()),
        ];



    }
}
