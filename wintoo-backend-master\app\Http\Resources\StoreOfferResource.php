<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class StoreOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

      $end_date =   Carbon::parse($this->end_date)->format('Y-m-d');
        return [
            'id'=>$this->id,
            'image_url'=>$this->image_url,
            'title'=>$this->title,
            'store'=>new  StoreResource($this->store),
            "start_date"=> $this->start_date,
            "end_date"=> $end_date,
            "products"=>ProductHomeResource::collection($this->products)
        ];
    }
}
