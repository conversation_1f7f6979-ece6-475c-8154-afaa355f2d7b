<?php

namespace App\Traits;

trait PropertyGetter {

    public function getProperty($pathString, $delimiter = '->') {

        //split the string into an array
        $pathArray = explode($delimiter, $pathString);
        $original_array =explode($delimiter, $pathString);
        //get the first and last of the array
        $first = array_shift($pathArray);
        $last = array_pop($pathArray);

        if(count($original_array) > 1){

            return $this->{$first}->{$last};
        }

        $tmp = $this->{$first};
        return $tmp;
    }
    public function getModelName() {

       return $this->getTable();
    }
}



?>
