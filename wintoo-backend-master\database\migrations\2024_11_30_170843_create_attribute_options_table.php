<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttributeOptionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('attribute_options', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('title')->nullable()->comment('title');
			$table->integer('store_category_id')->nullable()->comment('store_category_id');
			$table->integer('customer_id')->nullable()->comment('customer_id');
			$table->boolean('status')->nullable()->comment('status');
			$table->text('value')->nullable();
			$table->integer('attribute_id')->nullable();
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('attribute_options');
	}

}
