<?php

namespace App\Http\Livewire;

use App\Models\ProductsRequestsCategory;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\StoreCategory;
class ProductsRequestsCategoryFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'storeCategory';
      public $image ;


      public $storeCategory;
      protected $listeners = ['ProductsRequestsCategory-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];

        protected function rules()
        {
            return [
                "productsRequestsCategory.icon"=>'required',
                "productsRequestsCategory.name.ar"=>'required',
                "productsRequestsCategory.name.tr"=>'required',
                "productsRequestsCategory.name.en"=>'required',
                "productsRequestsCategory.name.he"=>'required',
                "productsRequestsCategory.status"=>'nullable',
                "productsRequestsCategory.order"=>'required|numeric|unique:products_request_categories,order,'.$this->productsRequestsCategory->id,
            ];
        }

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(StoreCategory::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->productsRequestsCategory  = $id?ProductsRequestsCategory::find($id):new ProductsRequestsCategory();
          }
      public function render()
          {
              return view('dashboard/productsRequestsCategory/form')->extends('dashboard_layout.main');
          }

      public function save(){
           // $this->validate();
           \DB::beginTransaction();
           try {

               $filename = $this->image?$this->image->store('/','public'):$this->productsRequestsCategory->icon;
               $this->productsRequestsCategory->icon=$filename;
               $this->productsRequestsCategory->order = (int)$this->productsRequestsCategory->order;

               $this->productsRequestsCategory->type = "product_request" ;

               $this->productsRequestsCategory->save();

                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.productsRequestsCategory');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


