<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Customer;
class CustomerLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Customer-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'customer';
        public $message ;
        public $all;
        public $selected=[];
        public $notification=[];
        public function mount()
            {
                $searchable = Customer::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Customer::getColumnLang();
                $this->searchable =Customer::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =Customer::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
               return view('dashboard/customer/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }
        public function add(){
            $this->notification =[];
            $this->showDataModel('basic','show');
        }
        public function updating($name,$value){


        if($name==='all'){

            if($value===true){
                $data = Customer::search($this->search_array);
                $data_selected =$data->pluck('id')->toArray();;
                $this->selected=$data_selected;

            }
            if($value===false){

                $this->selected=[];
            }
        }


    }
       public function save_notification(){
           // dd($this->selected);
        if(!empty($this->selected)){
            //dd($this->notification['description']);
            foreach ($this->selected as $value){
                $customer = Customer::find($value);
                $data=[
                    'title' => $this->notification['title'],
                    'body' => $this->notification['description'],
                    'id'=>null,
                    'type'=>'general_notification',
                  'link'=>$this->notification['link']
                ];
                fcmNotification($customer,$data);
                \Notification::send($customer,
                    new \App\Notifications\GeneralNotification(
                        $data['title'],
                        'general_notification',
                        $data
                    ));
                $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
                $this->showDataModel('basic','hide');
            }

        }else{
            $this->message='الرجاء تحديد الموردين';
        }
    }
        public function search(){}
         public function resetSearch(){
            $this->search_array=[];
         }

            public function edit($id){
                 return redirect()->route('dashboard.customer.edit',$id);
             }

             public function delete($id){
                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Customer-livewire:conformDelete',['id'=>$id]);
             }

             public function conformDelete($id){
                 $customer = Customer::find($id['id']);
                 $customer->phone=$customer->phone."_delete".$customer->id;
                 $customer->save();
                 Customer::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }

    public function setStatus($id){
        /*********/
        $object = Customer::find($id);
        $object->sms_verify =!$object->sms_verify;
        $object->save();
    }
}

