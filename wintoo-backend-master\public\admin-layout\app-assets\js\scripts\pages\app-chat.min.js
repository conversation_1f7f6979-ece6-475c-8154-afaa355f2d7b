function enter_chat(a){var s=$(".message").val();if(""!=s){var e='<div class="chat-content"><p>'+s+"</p></div>";$(".chat:last-child .chat-body").append(e),$(".message").val(""),$(".user-chats").scrollTop($(".user-chats > .chats").height())}}!function(s){"use strict";if(s.app.menu.is_touch_device())s(".chat-user-list").css("overflow","scroll"),s(".profile-sidebar-area .scroll-area").css("overflow","scroll"),s(".user-chats").css("overflow","scroll"),s(".user-profile-sidebar-area").css("overflow","scroll");else{if(0<s(".chat-application .chat-user-list").length)new PerfectScrollbar(".chat-user-list");if(0<s(".chat-application .profile-sidebar-area .scroll-area").length)new PerfectScrollbar(".profile-sidebar-area .scroll-area");if(0<s(".chat-application .user-chats").length)new PerfectScrollbar(".user-chats",{wheelPropagation:!1});if(0<s(".chat-application .user-profile-sidebar-area").length)new PerfectScrollbar(".user-profile-sidebar-area")}s(".chat-application .sidebar-profile-toggle").on("click",function(){s(".chat-profile-sidebar").addClass("show"),s(".chat-overlay").addClass("show")}),s(".chat-application .user-profile-toggle").on("click",function(){s(".user-profile-sidebar").addClass("show"),s(".chat-overlay").addClass("show")}),s(".chat-application .user-status input:radio[name=userStatus]").on("change",function(){var a="avatar-status-"+this.value;s(".header-profile-sidebar .avatar span").removeClass(),s(".sidebar-profile-toggle .avatar span").removeClass(),s(".header-profile-sidebar .avatar span").addClass(a+" avatar-status-lg"),s(".sidebar-profile-toggle .avatar span").addClass(a)}),s(".chat-application .close-icon").on("click",function(){s(".chat-profile-sidebar").removeClass("show"),s(".user-profile-sidebar").removeClass("show"),s(".sidebar-content").hasClass("show")||s(".chat-overlay").removeClass("show")}),s(".chat-application .sidebar-close-icon").on("click",function(){s(".sidebar-content").removeClass("show"),s(".chat-overlay").removeClass("show")}),s(".chat-application .chat-overlay").on("click",function(){s(".app-content .sidebar-content").removeClass("show"),s(".chat-application .chat-overlay").removeClass("show"),s(".chat-profile-sidebar").removeClass("show"),s(".user-profile-sidebar").removeClass("show")}),s(".chat-application .chat-user-list ul li").on("click",function(){s(".chat-user-list ul li").hasClass("active")&&s(".chat-user-list ul li").removeClass("active"),s(this).addClass("active"),s(this).find(".badge").remove(),s(".chat-user-list ul li").hasClass("active")?(s(".start-chat-area").addClass("d-none"),s(".active-chat").removeClass("d-none")):(s(".start-chat-area").removeClass("d-none"),s(".active-chat").addClass("d-none"))});var a=s(".user-chats");s(".chat-users-list-wrapper li").on("click",function(){a.animate({scrollTop:a[0].scrollHeight},400)}),s(".chat-application .favorite i").on("click",function(a){s(this).parent(".favorite").toggleClass("warning"),a.stopPropagation()}),s(".chat-application .menu-toggle").on("click",function(a){s(".app-content .sidebar-left").removeClass("show"),s(".chat-application .chat-overlay").removeClass("show")}),s(window).width()<992&&s(".chat-application .sidebar-toggle").on("click",function(){s(".app-content .sidebar-content").addClass("show"),s(".chat-application .chat-overlay").addClass("show")}),992<s(window).width()&&s(".chat-application .chat-overlay").hasClass("show")&&s(".chat-application .chat-overlay").removeClass("show"),s(".user-chats").scrollTop(s(".user-chats > .chats").height()),s(".chat-application #chat-search").on("keyup",function(){var a=s(this).val().toLowerCase();""!=a?s(".chat-user-list .chat-users-list-wrapper li").filter(function(){s(this).toggle(-1<s(this).text().toLowerCase().indexOf(a))}):s(".chat-user-list .chat-users-list-wrapper li").show()})}(jQuery),$(window).on("resize",function(){992<$(window).width()&&$(".chat-application .chat-overlay").hasClass("show")&&($(".app-content .sidebar-left").removeClass("show"),$(".chat-application .chat-overlay").removeClass("show")),$(window).width()<992&&($(".chat-application .chat-profile-sidebar").hasClass("show")&&$(".chat-profile-sidebar").removeClass("show"),$(".chat-application .sidebar-toggle").on("click",function(){$(".app-content .sidebar-content").addClass("show"),$(".chat-application .chat-overlay").addClass("show")}))});