<?php

namespace App\Models;

use App\Http\Resources\AttributeResource;
use App\Services\SendNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
class AdsRequest extends Model
{
    
use \App\Traits\PropertyGetter;
protected $guarded = [];
     protected $fillable = ["title","description","customer_id","city","country","media"];
  protected $table = "paid_ads" ;
     public function scopeSearch($query, $data) {

             $data = json_decode($data,true);
            //  if(isset($data["post_id"])){
            //   $query->where("post_id","LIKE","%".$data["post_id"]."%");}
            //  if(isset($data["parent_id"])){
            //   $query->where("parent_id","LIKE","%".$data["parent_id"]."%");}
             if(isset($data["customer_id"])){
               $query->where("customer_id","LIKE","%".$data["customer_id"]."%");}
            //  if(isset($data["comment_text"])){
            //   $query->where("comment_text","LIKE","%".$data["comment_text"]."%");}
             return $query ;
             }


     public function customer() {
         return $this->belongsTo(Customer::class,"customer_id");
    }


    //  public function post() {
    //      return $this->belongsTo(Post::class,"post_id");
    // }




    //  public function replies() {
    //      return $this->hasMany(Comment::class,"parent_id");
    //  }


}