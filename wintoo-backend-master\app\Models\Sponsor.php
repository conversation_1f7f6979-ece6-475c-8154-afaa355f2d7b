<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sponsor extends Model
{
use \App\Traits\PropertyGetter;


 protected $guarded = [];
 protected $appends = ['logo_url',"video_url","media_array","media_thumbnail_url"];
        public  static function  getColumnLang(){
 	 	$columes=[
            'logo_url'=>[\Lang::get('sponsor.logo') ,1,true,false, ['type'=>'image','actions'=>null] ],
 	    	'name'=>[\Lang::get('sponsor.name') ,1,true,false, ['type'=>'string','actions'=>null] ],

 	 	//'media_json'=>[\Lang::get('sponsor.media_json') ,1,true,false, ['type'=>'longText','actions'=>null] ],
 	 	'status'=>[\Lang::get('sponsor.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
 	 	   'actions'=>['الخيارات',3,true,false,['type'=>'button','actions'=>['edit','delete']]],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('sponsor.name')],
// 	 	'logo'=>[\Lang::get('sponsor.logo')],
// 	 	'media_json'=>[\Lang::get('sponsor.media_json')],
// 	 	'status'=>[\Lang::get('sponsor.status')],

 	 	 ]; return $columes;
  }
        public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["logo"])){
 	 	   $query->where("logo","LIKE","%".$data["logo"]."%");}
 	 	 if(isset($data["media_json"])){
 	 	   $query->where("media_json","LIKE","%".$data["media_json"]."%");}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 return $query ;
 	 	 }

 	 	 public function getLogoUrlAttribute(){
             return $this->logo ? asset("storage/".$this->logo):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image))) ;
         }
         public function getVideoUrlAttribute(){
             return $this->video ? asset("storage/".$this->video):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->video))) ;

         }
        public function getMediaArrayAttribute(){
            $images = json_decode($this->media_json);
            $final_array =[];
            foreach ($images as $image){
                $final_array[]=asset("storage/".$image);
            }

            return $final_array ;
        }

    public function getMediaThumbnailUrlAttribute(){
        return $this->media_thumbnail?asset('storage/'.$this->media_thumbnail):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->media_thumbnail)));
    }
//    public function getIsVideoAttribute(){
//        $ext = pathinfo($this->video, PATHINFO_EXTENSION);
//        $videoExtension = ['mp4','mov','mpg','mpeg','wmv','mkv','avi','ts','TS'];
//        if (in_array( $ext,$videoExtension)){
//            return "video";
//        }
//        return "image";
//    }
}
