<?php

namespace App\Http\Livewire;

use App\Models\ProductImage;
use App\Services\VideoEdit;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Sponsor;
class SponsorFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
    public $title = "";
    public $file_name = 'sponsor';
    public $sponsor;
    public $logo;
    public $video;
    public $images_uploaders=[null];
    protected $listeners = [
          'Sponsor-livewire:conformDelete' => 'conformDelete',
          'reviewSectionRefresh' => '$refresh',
          "single_file_uploaded" => 'singleFileUploaded',
          "add_file" => 'add_image',
          "clear_file" => 'removeImage',
          ];
    protected $rules = [
                "sponsor.name"=>'required',
                "sponsor.logo"=>'required',
                "sponsor.media_json"=>'nullable',
                "sponsor.media_thumbnail"=>'nullable',
                "sponsor.status"=>'nullable',

       ];

    protected $validationAttributes;
    public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Sponsor::getColumnLang());
       }
    public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account');

              $this->sponsor  = $id?Sponsor::find($id):new Sponsor();

              $this->getProductImages($id);

          }
    public function render()
          {

              return view('dashboard/sponsor/form')->extends('dashboard_layout.main');
          }
    public function save(){

           \DB::beginTransaction();
           try {
                $video_online = $this->video ? $this->video->store('/','public') : $this->sponsor->video;
                $logo = $this->logo?$this->logo->store('/','public') : $this->sponsor->logo;
                $this->sponsor->video = $video_online;
                if ($video_online){

                    $this->sponsor->media_thumbnail = VideoEdit::generateVideoThumbnail($video_online);
                }
                $this->sponsor->logo = $logo;
                $this->sponsor->media_json = json_encode($this->uploadFiles()) ;
                $this->sponsor->save();
                \DB::commit();
                $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
                return redirect()->route('dashboard.sponsor');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }
    private function getProductImages($id){

        if($id){




            foreach ($this->sponsor->media_array as  $index => $image){
                $this->images_uploaders[]=['id'=>$index,'image_url'=>$image];
            }


        }else{
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];
            $this->images_uploaders[] =['id'=>0,'image_url'=>''];


        }


    }

    public function add_image(){
        $this->images_uploaders[] = ['id'=>0,'image_url'=>''];
    }

    public function singleFileUploaded($file){

        try {

            if($this->getFileInfo($file[0])["file_type"] == "image"){

                $this->images_uploaders[$file[1]]=['id'=>0,'image_url'=>$file[0]];

            }else{
                session()->flash("error", "Uploaded file must be an image");
            }
        } catch (Exception $e) {

        }
    }

    public function getFileInfo($file)
    {
        $info = [
            "decoded_file" => null,
            "file_meta" => null,
            "file_mime_type" => null,
            "file_type" => null,
            "file_extension" => null,
        ];

        try {
            $info['decoded_file'] = base64_decode(substr($file, strpos($file, ',') + 1));
            $info['file_meta'] = explode(';', $file)[0];
            $info['file_mime_type'] = explode(':', $info['file_meta'])[1];
            $info['file_type'] = explode('/', $info['file_mime_type'])[0];
            $info['file_extension'] = explode('/', $info['file_mime_type'])[1];
        } catch(Exception $e) {

        }

        return $info;
    }

    public function uploadFiles()
    {
        $images=[];
        $ids = array_column($this->images_uploaders,'id');
        foreach($this->images_uploaders as $value) {

            if($value['image_url']){
                if(str_starts_with($value['image_url'],'data:image')){
                    $file_data = $this->getFileInfo($value['image_url']);
                    $file_name = Str::random(20).'.'.$file_data['file_extension'];
                    $result = Storage::disk('public')
                              ->put($file_name, $file_data['decoded_file']);
                    $images[]=$file_name;
                }

            }
        }
        return $images;
    }

    private function  ImageSaveAndGetFileName($object){
        $class_name=["Livewire\TemporaryUploadedFile"];

        if(in_array(get_class($object), $class_name)){
            $filename = $object->store('/','public');
        }else{

            $filename= isset($object['image'])?$object['image']:null;
        }

        return $filename;
    }


}


