<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePostsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('posts', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('description')->nullable()->comment('description');
			$table->string('media', 191)->nullable()->comment('media');
			$table->text('last_media')->nullable();
			$table->string('media_type', 191)->nullable()->comment('media_type');
			$table->string('status', 191)->nullable()->comment('status');
			$table->timestamps();
			$table->integer('store_id')->nullable();
			$table->string('media_thumbnail', 191)->nullable();
			$table->string('type', 191)->nullable();
			$table->string('customer_id', 191)->nullable();
			$table->integer('views_count')->default(0);
			$table->dateTime('start_date')->nullable();
			$table->dateTime('end_date')->nullable();
			$table->integer('like_count')->nullable()->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('posts');
	}

}
