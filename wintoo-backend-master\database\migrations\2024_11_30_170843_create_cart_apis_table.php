<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCartApisTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('cart_apis', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('product_id');
			$table->integer('address_id')->nullable();
			$table->integer('customer_id');
			$table->text('options')->nullable();
			$table->integer('qty')->default(0);
			$table->timestamps();
			$table->string('variation_id', 191)->nullable();
			$table->integer('rate_id')->nullable();
			$table->integer('store_id')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('cart_apis');
	}

}
