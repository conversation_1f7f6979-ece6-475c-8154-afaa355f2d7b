.vertical-overlay-menu .content{margin-right:0}.vertical-overlay-menu .navbar .navbar-header{float:right;width:260px}.vertical-overlay-menu .navbar.header-navbar.floating-nav{width:calc(100vw - (100vw - 100%) - calc(2.2rem * 2))}.vertical-overlay-menu .main-menu,.vertical-overlay-menu.menu-hide .main-menu{opacity:0;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);-webkit-transition:width .25s,opacity .25s,-webkit-transform .25s;transition:width .25s,opacity .25s,-webkit-transform .25s;transition:width .25s,opacity .25s,transform .25s;transition:width .25s,opacity .25s,transform .25s,-webkit-transform .25s;width:260px;right:-260px}.vertical-overlay-menu .main-menu .navigation .navigation-header .icon-minus{display:none}.vertical-overlay-menu .main-menu .navigation>li>a>i{margin-left:14px;float:right}.vertical-overlay-menu .main-menu .navigation>li>a>i:before{-webkit-transition:.2s ease all;transition:.2s ease all;font-size:1.429rem}.vertical-overlay-menu .main-menu .navigation li.has-sub>a:not(.mm-next):after{content:'\f105';font-family:FontAwesome;font-size:1rem;display:inline-block;position:absolute;left:25px;top:10px;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);transition:-webkit-transform .2s ease-in-out}.vertical-overlay-menu .main-menu .navigation li.open>a:not(.mm-next):after{-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg)}.vertical-overlay-menu .main-menu .navigation li a i{font-size:1.1rem}.vertical-overlay-menu .main-menu .main-menu-footer{bottom:55px;width:260px}.vertical-overlay-menu.menu-open .main-menu{opacity:1;-webkit-transform:translate3d(-260px,0,0);transform:translate3d(-260px,0,0);-webkit-transition:width .25s,opacity .25s,-webkit-transform .25s;transition:width .25s,opacity .25s,-webkit-transform .25s;transition:width .25s,opacity .25s,transform .25s;transition:width .25s,opacity .25s,transform .25s,-webkit-transform .25s}.vertical-overlay-menu.menu-flipped .main-menu{left:-260px;right:inherit}.vertical-overlay-menu.menu-flipped .navbar .navbar-container{margin:0 0 0 260px}.vertical-overlay-menu.menu-flipped .navbar .navbar-header{float:left}.vertical-overlay-menu.menu-flipped.menu-open .main-menu{-webkit-transform:translate3d(260px,0,0);transform:translate3d(260px,0,0)}@media (max-width:991.98px){.vertical-overlay-menu .main-menu .main-menu-footer{bottom:0}}