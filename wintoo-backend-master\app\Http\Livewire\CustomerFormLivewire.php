<?php

namespace App\Http\Livewire;

use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Customer;
class CustomerFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'customer';
      public $customer;
      public $password;
      public $image;
      protected $listeners = ['Customer-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "customer.email"=>'nullable',
            "customer.phone"=>'nullable',
            "customer.username"=>'nullable',
           // "customer.password"=>'nullable',
            "customer.status"=>'nullable',
            "customer.image"=>'nullable',

       ];
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->customer  = $id?Customer::find($id):new Customer();
          }
      public function render()
          {
              return view('dashboard/customer/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               if($this->password){
                   $this->customer->password= Hash::make($this->password);
               }

               $this->customer->image=$this->image?$this->image->store('/','customer'):$this->customer->image;

               $this->customer->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.customer');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


