<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\JobType;
class JobTypeFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'jobType';
      public $image ;


      public $jobType;
      protected $listeners = ['JobType-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];

        protected function rules()
        {
            return [
                "jobType.name.ar"=>'required',
                "jobType.name.tr"=>'required',
                "jobType.name.en"=>'required',
                "jobType.name.he"=>'required',
                "jobType.status"=>'nullable',
            ];
        }

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(JobType::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->jobType  = $id?JobType::find($id):new JobType();
          }
      public function render()
          {
              return view('dashboard/jobType/form')->extends('dashboard_layout.main');
          }

      public function save(){
           // $this->validate();
           \DB::beginTransaction();
           try {


               $this->jobType->save();

                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.jobType');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


