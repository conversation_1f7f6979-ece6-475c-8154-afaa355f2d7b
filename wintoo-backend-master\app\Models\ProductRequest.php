<?php

namespace App\Models;

use App\Http\Resources\AttributeResource;
use App\Services\SendNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class ProductRequest extends Model
{
use \App\Traits\PropertyGetter;


         protected $guarded = [];
         protected $appends=['customer_name',"store_category_name","type_name","product_title",'full_address'];

    public  static function  getColumnLang(){
    $columes=[
        'product_name'=>["اسم المنتج ",1,true,false, ['type'=>'string','actions'=>null] ],
        'store_category_name'=>["التصنيف" ,1,true,false, ['type'=>'integer','actions'=>null] ],
        'customer_name'=>["المستخدم",1,true,false, ['type'=>'integer','actions'=>null] ],
        'type_name'=>["نوع",1,true,false, ['type'=>'integer','actions'=>null] ],
        'price'=>["السعر",1,true,false, ['type'=>'integer','actions'=>null] ],
        'count'=>["العدد",1,true,false, ['type'=>'integer','actions'=>null] ],
        'actions'=>['الخيارات',3,true,false,['type'=>'button','actions'=>['view']]],
    ];
     return $columes;
}
         public static function getSearchable(){
                $columes=[
                    'title'=>["العنوان"],
                    'store_category_id'=>["التصنيفات",['type'=>'select','name'=>'name','value'=>'id','model'=>'StoreCategory']],
                    'customer_id'=>["المستخدمين",['type'=>'select','name'=>'username','value'=>'id','model'=>'Customer']],

                 ];
                return $columes;
         }
         public function scopeSearch($query, $data) {
     if(isset($data["title"])){
       $query->where("title","LIKE","%".$data["title"]."%");}
     if(isset($data["store_category_id"])){
       $query->where("store_category_id","LIKE","%".$data["store_category_id"]."%");}
     if(isset($data["customer_id"])){
       $query->where("customer_id","LIKE","%".$data["customer_id"]."%");}
     if(isset($data["status"])){
       $query->where("status","LIKE","%".$data["status"]."%");}
     return $query ;
     }

         public function customer(){
            return $this->belongsTo(Customer::class,"customer_id");
         }

         public function store(){
            return $this->belongsTo(Store::class,"store_id");
         }

         public function store_category(){
            return $this->belongsTo(ProductsRequestsCategory::class,"store_category_id");
         }

         public function getCustomerNameAttribute(){

            return optional($this->customer)->username;
         }

         public function getTypeNameAttribute(){

                if($this->type =="sell"){
                    return Lang::get("lang.sell");
                }

             return Lang::get("lang.buy");

         }
         public function getStoreCategoryNameAttribute(){

            return optional($this->store_category)->name;
         }

         public function attributes(){

            return $this->belongsToMany(
                Attribute::class,
                "product_request_attributes",
                "product_id",
                "attribute_id"
            )->withPivot(['value',"payload"]);
         }

    public function media(){
        return $this->hasMany(ProductRequestsMedia::class,"request_id");
    }


    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');
    }


    public function governorate(){
        return $this->belongsTo(Governorate::class);

    }
    public function city(){
        return $this->belongsTo(City::class);

    }
    public function region(){
        return $this->belongsTo(Region::class);

    }
    public function country(){
        return $this->belongsTo(Country::class);

    }




    public function getProductTitleAttribute(){

           $attributes =  AttributeResource::collection($this->attributes()->get()->take(2)) ;
            return (  $attributes->reduce(function ( $out  , $item){
                $data = $item->jsonSerialize();
                  return   ($out??"") ./*" ".$data["title"] . */" ". $data["value_string"] ;

                })

             );

    }


    public function getFullAddressAttribute(){
      //  return optional($this->country)->name .' - '.optional($this->governorate)->name .' - '.optional($this->city)->name .' - '.optional($this->regoin)->name;

        return implode(" - " , array_filter([optional($this->governorate)->name ,optional($this->city)->name ,optional($this->regoin)->name],function ($item){
            return !is_null($item) ;
        }));


    }


    public function getMatchingProductsRequestsAndNotify($notify = false){
        $type  = $this->type ;
        $sell_price  = $this->sell_price ;
        $buy_price_from  = $this->buy_price_from ;
        $buy_price_to  = $this->buy_price_to ;
        $get_same_product = ProductRequest::where("id","!=",$this->id)
            ->where(function ($query1){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser){
                $query1->where("customer_id","!=",$currentUser->id);
            }

            $query1->where(function ($q) use ($currentUser){

                $q->where(function ($q) use($currentUser){

                    $q->whereIn("customer_id" ,function ($q){
                        $q->select('id')
                        ->from("customers")
                            ->whereNull("deleted_at")
                       ;
                    })->orWhereNull("customer_id");;


                });

            });



            if ($currentStore) {
                $query1->where("store_id", "!=", $currentStore->id);
                }

                $query1->where(function ($q) use ($currentStore){

                    $q->where(function ($q) use($currentStore){

                        $q->where("store_id","!=",$currentStore->id)
                            ->whereIn("store_id" ,function ($q){
                                $q->select('id')
                                    ->from("stores")
                                   ;

                                ;
                            })->orWhereNull("store_id");


                    });

                });




        })
            ->where("type",$this->type =="sell"?"buy":"sell")
            ->where( function ($query) use($type,$sell_price,$buy_price_from,$buy_price_to){
                if ($type =="sell") {

                    $query->where("buy_price_from","<=",(double)$sell_price);
                    $query->where("buy_price_to",">=",(double)$sell_price);

                }else if ($type =="buy"){

                    $query->where("sell_price",">=",(double)$buy_price_from);
                    $query->where("sell_price","<=",(double)$buy_price_to);

                }

            })
            ->where("hash",$this->hash)
->where(function ($query){

    $query->whereIn("id" , $this->type =="sell"?$this->getSellRequestMatchedBuyRequestsIds():$this->getBuyRequestMatchedSellRequestsIds() );

})

            ->orderBy("id","desc")->get();




        if ($notify){

            if (count($get_same_product)){

                if ($this->store_id){
                    SendNotification::sendNotificationOnProductRequestFirstMessageStore($this->store_id ,$this->id );


                } else if ($this->customer_id){

                 //   $customer = Customer::find($this->customer_id);

               //      dd($this->customer_id,$customer);
                    SendNotification::sendNotificationOnProductRequestFirstMessage($this->customer_id ,$this->id );
                }


            }


            foreach ($get_same_product as $item){

                if ($item->store_id){

                    SendNotification::sendNotificationOnProductRequestFirstMessageStore($item->store_id ,$item->id );

                } else if ($item->customer_id){

                    try{
                 //   dd($item->customer_id , Customer::find(90));
                    SendNotification::sendNotificationOnProductRequestFirstMessage($item->customer_id ,$item->id );

                       }catch (\Exception $exception){
                       // dd($item->customer_id , $exception->getMessage());
                    }

                }

            }
        }

        return $get_same_product;

    }

    function getSellRequestMatchedBuyRequestsIds(){


    $target_type = "buy" ;


    $results = DB::select(DB::raw('
    SELECT prs.id
    FROM product_requests prs
    WHERE prs.type = :request_type
      AND prs.hash = :hash
      AND (
        SELECT COUNT(*)
        FROM product_request_attributes AS pattrs
        JOIN product_request_attributes AS target_attrs
          ON pattrs.attribute_id = target_attrs.attribute_id
          AND target_attrs.product_id = :product_id_2
        JOIN attributes attrs
          ON pattrs.attribute_id = attrs.id
        WHERE pattrs.product_id = prs.id
          AND attrs.type = "range"
          AND JSON_VALID(pattrs.value)
          AND CAST(JSON_UNQUOTE(JSON_EXTRACT(target_attrs.value, "$")) AS SIGNED)
            BETWEEN CAST(JSON_UNQUOTE(JSON_EXTRACT(pattrs.value, "$[0]")) AS SIGNED)
            AND CAST(JSON_UNQUOTE(JSON_EXTRACT(pattrs.value, "$[1]")) AS SIGNED)
      ) = (
        SELECT COUNT(*)
        FROM product_request_attributes AS pattrs
        JOIN attributes attrs
          ON pattrs.attribute_id = attrs.id
        WHERE pattrs.product_id = :product_id_3
          AND attrs.type = "range"
      )
      AND prs.id <> :product_id_4
'), [
        'product_id_2' => $this->id,
        'product_id_3' => $this->id,
        'product_id_4' => $this->id,
        'request_type' => "buy",
        'hash' => $this->hash
    ]);



        return(collect($results)->pluck("id")->toArray());
}

//function getBuyRequestMatchedSellRequestsIds(){
//
//
//$results = DB::select(
//
// DB::raw('SELECT   prs.*  from product_requests prs
//
//where
//
// prs.type = :request_type and
// prs.hash = :hash and
//(SELECT count(*)  from
//		 product_request_attributes as pattrs
//		join product_request_attributes as target_attrs on ( pattrs.attribute_id  = target_attrs.attribute_id and target_attrs.product_id = :product_id_2   )
//        join attributes attrs ON (pattrs.attribute_id = attrs.id)
//
//       where
// 		pattrs.product_id = prs.id
//        and  attrs.type = "range"
//and CAST( pattrs.value AS SIGNED)
// between CAST( target_attrs.value->"$[0]"  AS SIGNED)
// and CAST( target_attrs.value->"$[1]"  AS SIGNED)
//)
//=
//(SELECT count(*)  from  product_request_attributes as pattrs
//        join attributes attrs ON (pattrs.attribute_id = attrs.id)
//       where
//		pattrs.product_id = :product_id_3
//         and attrs.type = "range"
//)
//
//and prs.id <> :product_id_4 ;
//'), array(
//            'product_id_2' => $this->id,
//            'product_id_3' => $this->id,
//            'product_id_4' => $this->id,
//            'request_type' => "sell",
//            'hash' => $this->hash
//));
//
//
//return(collect($results)->pluck("id")->toArray());
//
//}


    function getBuyRequestMatchedSellRequestsIds() {
        $results = DB::select(
            DB::raw('SELECT prs.* FROM product_requests prs
                 WHERE prs.type = :request_type
                   AND prs.hash = :hash
                   AND (
                        SELECT COUNT(*)
                        FROM product_request_attributes AS pattrs
                        JOIN product_request_attributes AS target_attrs
                          ON pattrs.attribute_id = target_attrs.attribute_id
                          AND target_attrs.product_id = :product_id_2
                        JOIN attributes attrs
                          ON pattrs.attribute_id = attrs.id
                        WHERE pattrs.product_id = prs.id
                          AND attrs.type = "range"
                          AND JSON_VALID(pattrs.value)
                          AND CAST(pattrs.value AS SIGNED)
                            BETWEEN CAST(JSON_UNQUOTE(JSON_EXTRACT(target_attrs.value, "$[0]")) AS SIGNED)
                            AND CAST(JSON_UNQUOTE(JSON_EXTRACT(target_attrs.value, "$[1]")) AS SIGNED)
                   ) = (
                        SELECT COUNT(*)
                        FROM product_request_attributes AS pattrs
                        JOIN attributes attrs
                          ON pattrs.attribute_id = attrs.id
                        WHERE pattrs.product_id = :product_id_3
                          AND attrs.type = "range"
                   )
                   AND prs.id <> :product_id_4;
                '),
            [
                'product_id_2' => $this->id,
                'product_id_3' => $this->id,
                'product_id_4' => $this->id,
                'request_type' => "sell",
                'hash' => $this->hash
            ]
        );

        return collect($results)->pluck("id")->toArray();
    }




}
