<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNoorsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('noors', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('name', 191)->nullable()->comment('الاسم');
			$table->string('bd', 191)->nullable()->comment('تاريخ الميلاد');
			$table->string('gender', 191)->nullable()->comment('الجنس');
			$table->integer('related_to')->nullable()->comment('الانتماء');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('noors');
	}

}
