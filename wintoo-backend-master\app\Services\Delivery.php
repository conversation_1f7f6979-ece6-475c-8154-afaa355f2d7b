<?php

namespace App\Services;



use App\Models\Currency;
use App\Models\Order;

class Delivery{



    /*


    {
"jsonrpc": "2.0",
        "params": {
        "login":"0569033501",
        "password": "123123123",
        "db":"redline",
        "customer_address": "test",
        "customer_mobile": "0569894922",
        "customer_name": "Eman",
        "customer_area": "10729",
        "customer_sub_area": "",
        "reference_id":"",
        "cost":200,
        "order_type_id":"1",
        "show_alt_address":true,
        "business_alt_area":"80",
        "business_alt_sub_area":"" ,
        "business_alt_address":"Test new From address" ,
        "alt_business_name"  : "Test Store" ,
        "alt_mobile_number" : "0592106097"
    }
}


     */

    public $username = "0569033501";
    public $password = "123123123";
    public $db = "redline";

    public  function create_order($order){

        $url = 'https://'.$this->db.'.olivery.app/create_order';
        $data = [

            "login"=>$this->username,
            "password"=> $this->password,
            "db"=>$this->db,
            "customer_address"=> $order->address->full_address,
            "customer_mobile"=> "0".$order->customer->phone,
            "customer_name"=> $order->customer->username,
            "customer_area"=>$order->address->city->olivery_area_code??"",
            "customer_sub_area"=> "",
            "reference_id"=>"",//$order->id,
            "cost"=>currency_convert($order->total,$order->currency,Currency::ils()),
            "order_type_id"=>"1", //1 -> Normal   2 -> VIP
            "show_alt_address"=>true,
            "inclusive_delivery"=>true,
            "business_alt_area"=>$order->store->city->olivery_area_code??"",//$order->store->city->olivery_area_code??"",
            "business_alt_sub_area"=>"",
            "business_alt_address"=>$order->store->full_address ,
            "alt_business_name"  => $order->store->name ,
            "alt_mobile_number" => $order->store->mobile

        ];


        $body = [
            "jsonrpc" => "2.0" ,
            "params" => $data
        ];

        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type:application/json',
            'Accept:application/json'
        ));

        // execute!
        $response = curl_exec($ch);
        $response = json_decode($response, true);

//        $order->delivery_cost = $response['Delivery cost'];
//        $order->save();


        file_put_contents('delivery.log',print_r(array($url , $data ,$response),true),FILE_APPEND);

        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);


        if($httpcode == 200){

            $order->delivery_reference_id = $response['result']['Sequence'];

            $order->save();
            return true ;
        }else
        file_put_contents('delivery_error.log',curl_error($ch),FILE_APPEND);

        return false;
    }


    public function get_delivery_costForUserCurrency( $address , $store)
    {

        return getPriceForUser($this->get_delivery_cost($address,$store),Currency::ils()); // ils

    }
    public function get_delivery_cost( $address , $store)
    {
        file_put_contents('delivery.log',print_r(array($address , $store ),true),FILE_APPEND);

        $url = 'https://'.$this->db.'.olivery.app/get_delivery_cost';
        $data = [

            "login"=>$this->username,
            "password"=> $this->password,
            "db"=>$this->db,

            "customer_area"=>$address->city->olivery_area_code??"",
            "business_alt_area"=>$store->city->olivery_area_code??"",

            "order_type_id"=>"1", //1 -> Normal   2 -> VIP
        ];


        $body = [
            "jsonrpc" => "2.0" ,
            "params" => $data
        ];

        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type:application/json',
            'Accept:application/json'
        ));


        // execute!
        $response = curl_exec($ch);
        $response = json_decode($response, true);

        file_put_contents('delivery.log',print_r(array($url , $data ,$response),true),FILE_APPEND);

        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);


        if($httpcode == 200){

        return ($response['result']['delivery_cost']);

        }else
            file_put_contents('delivery_error.log',curl_error($ch),FILE_APPEND);

        return 0;


    }



}
?>
