<?php

namespace App\Jobs;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use Illuminate\Support\Facades\Http;
class SendToStorjanJob implements ShouldQueue
{
    use  Batchable,Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $tries = 1;
    public $timeout = 0;
    public $data;
    public $order_id;

    public function __construct($data,$order_id)
    {
        $this->order_id=$order_id;
        $this->data=$data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $order = Order::find($this->order_id);
            if($order->is_send ==0){
                $url = 'https://custome.storejan.com/api/orders';

                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'X-Storjan-Key' => 'JMR0S2ADO0M0Q9GMOFSJ60R5B525OY8N',

                ])->withBody(json_encode($this->data),'application/json')->post($url);

                file_put_contents('/home/<USER>/system/storage/api_order_response.log',print_r($response->body(),true).'\n',FILE_APPEND);
                file_put_contents('/home/<USER>/system/storage/api_order_data.log',json_encode($this->data).'\n',FILE_APPEND);

                $order_id = json_decode($response->body());
                $order->store_jan_order_id = $order_id->order_id;
                $order->is_send = true;
                $order->save();
            }


        } catch (\Exception $e){
            file_put_contents('/home/<USER>/system/storage/api_order_error.log',print_r($e->getMessage(),true).'\n',FILE_APPEND);
        }

    }
}
