<?php

namespace App\Http\Resources;

use App\Models\Product;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class OffersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $res =  parent::toArray($request);
        $res['start_date'] =   Carbon::parse($res['start_date'])->format('Y-m-d');
        $res['end_date'] =   Carbon::parse($res['end_date'])->format('Y-m-d');
        $res['products'] =ProductResource::collection($this->products);

        return  $res   ;
    }
}
