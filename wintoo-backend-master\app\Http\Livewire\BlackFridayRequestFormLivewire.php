<?php

namespace App\Http\Livewire;

use App\Models\BlackFridayRequest;
use App\Models\Country;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\City;
class BlackFridayRequestFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'country';
      public $black_friday;

       protected $rules = [
                "black_friday.end_at"=>'nullable',
                "black_friday.price"=>'required',
                "black_friday.note"=>'nullable',
                "black_friday.status"=>'nullable',
                "black_friday.store_id"=>'nullable',
       ];

    protected $validationAttributes;
    public function __construct($id = null)
    {

        parent::__construct($id);

        $this->validationAttributes = $this->getColNameForValidation(Country::getColumnLang());

    }

    public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->black_friday  = $id?BlackFridayRequest::find($id):new BlackFridayRequest();
          }
      public function render()
          {
              return view('dashboard/join_black_friday/form')->extends('dashboard_layout.main');

//              if(in_array('country_create',$this->actions_permission()) ){
////                  wreturn view('dashboard/city/index',[ 'data'=>$data])->extends('dashboard_layout.main');
//                  return view('dashboard/country/form')->extends('dashboard_layout.main');
//              }else{
//                  return view('dashboard.not-authorized')->extends('dashboard_layout.main');
//              }


          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {

               $startDate=\Carbon\Carbon::now()->format('Y-m-d H:i:s');
               $this->black_friday->start_at = $startDate ;
               $this->black_friday->save();

               \DB::commit();

               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.join_black');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


