<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomersBiosTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('customers_bios', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('place', 191);
			$table->enum('type', array('living','study','work'));
			$table->boolean('current')->default(0);
			$table->timestamps();
			$table->string('customer_id', 191);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('customers_bios');
	}

}
