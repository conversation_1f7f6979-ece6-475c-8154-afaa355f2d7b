<?php

namespace App\Traits;

trait OrderByNewestRandomly
{


    function scopeOrderByNewestRandomly($query  , $seed = false){

            if ($seed){

                $seed = (int)$seed ;

                $query->orderByRaw(
                    "  YEAR(created_at) DESC,MONTH(created_at) DESC, WEEK(created_at) DESC, RAND($seed) "
                );

            }else
            $query->orderByRaw(
                "  YEAR(created_at) DESC,MONTH(created_at) DESC, WEEK(created_at) DESC, RAND() "
            );
    }

}
