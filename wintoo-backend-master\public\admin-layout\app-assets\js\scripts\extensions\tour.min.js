$(document).ready(function(){e(),$(window).resize(e);var t=new Shepherd.Tour({classes:"shadow-md bg-purple-dark",scrollTo:!0});function e(){window.resizeEvt,576<$(window).width()?$("#tour").on("click",function(){clearTimeout(window.resizeEvt),t.start()}):$("#tour").on("click",function(){clearTimeout(window.resizeEvt),t.cancel(),window.resizeEvt=setTimeout(function(){alert("Tour only works for large screens!")},250)})}t.addStep("step-1",{text:"Here is page title.",attachTo:".breadcrumbs-top .content-header-title bottom",buttons:[{text:"Skip",action:t.complete},{text:"Next",action:t.next}]}),t.addStep("step-2",{text:"Check your notifications from here.",attachTo:".dropdown-notification .icon-bell bottom",buttons:[{text:"Skip",action:t.complete},{text:"previous",action:t.back},{text:"Next",action:t.next}]}),t.addStep("step-3",{text:"Click here for user options.",attachTo:".dropdown-user-link img bottom",buttons:[{text:"Skip",action:t.complete},{text:"previous",action:t.back},{text:"Next",action:t.next}]}),t.addStep("step-4",{text:"Buy this awesomeness at affordable price!",attachTo:".buy-now bottom",buttons:[{text:"previous",action:t.back},{text:"Finish",action:t.complete}]})});