<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Currency;
class CurrencyFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'currency';
      public $image ;


      public $currency;
      protected $listeners = ['Currency-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];

        protected function rules()
        {
            return [
                "currency.name.ar"=>'required',
                "currency.name.tr"=>'required',
                "currency.name.en"=>'required',
                "currency.name.he"=>'required',
                "currency.iso_code"=>'nullable',
                "currency.symbol"=>'nullable',
                "currency.usd_exchange_rate"=>'nullable',
                "currency.status"=>'required',
            ];
        }

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Currency::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->currency  = $id?Currency::find($id):new Currency();
          }
      public function render()
          {
              return view('dashboard/currency/form')->extends('dashboard_layout.main');
          }

      public function save(){
           // $this->validate();
           \DB::beginTransaction();
           try {

               $this->currency->save();

                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.currency');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


