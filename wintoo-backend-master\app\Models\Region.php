<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Region extends Model
{
use \App\Traits\PropertyGetter;
use HasFactory;

    use HasTranslations;
    public $translatable = ['name'];

 protected $guarded = [];
 protected  $appends=['status_name'];


 public  static function  getColumnLang(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('region.name') ,1,true,false,[]],
 	 	'status'=>[\Lang::get('region.status') ,1,true,false,[]],
 	 	'city_id'=>[\Lang::get('region.city_id') ,1,true,false,[]],
 	 	   'actions'=>['الخيارات',2,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }
 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('region.name')],
 	 	//'status'=>[\Lang::get('region.status')],
 	 	'city_id'=>[\Lang::get('region.city_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'City']],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){
 	 	   $query->where("name","LIKE","%".$data["name"]."%");}
 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 if(isset($data["city_id"])){
 	 	   $query->where("city_id","LIKE","%".$data["city_id"]."%");}
 	 	 return $query ;
 	 	 }


    public function getStatusNameAttribute(){
        if($this->status){
            return ' مفعل';
        }
        return 'غير مفعل';
    }


    public function city(){
        return $this->belongsTo(City::class);

    }

}
