<?php

namespace App\Http\Livewire;

use App\Models\User;
use Illuminate\Notifications\Notification;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use Mo<PERSON>y\Matcher\Not;

class NotificationListLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $title = "";

    public function render()
    {

        $data=\App\Models\Notification::all();
        // $data='';

        return view('dashboard.notification.list',[ 'data'=>$data])->extends('dashboard_layout.main');
    }

    public  function  read_notification($notification_id)
    {


        $notification = auth()->user()->notifications()->where('id', $notification_id)->first();

        if ($notification) {
            $notification->markAsRead();
            return redirect($notification->data['actionURL']);

        }
    }

   }

