<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Spatie\Translatable\HasTranslations;

class City extends Model
{
use \App\Traits\PropertyGetter;

    use HasFactory;
    protected $guarded = [];
    protected  $appends=['status_name'];

    use HasTranslations;
    public $translatable = ['name'];

    public  static function  getColumnLang(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('city.name') ,1,true,false,[]],
 	 	'status'=>[\Lang::get('city.status') ,1,true,false,[]],
 	 	'governorate_id'=>[\Lang::get('city.governorate_id') ,1,true,false,[]],
 	 	   'actions'=>['الخيارات',2,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }

 	 	public static function getSearchable(){
 	 	$columes=['name'=>[\Lang::get('city.name')],

 	 	'governorate_id'=>[\Lang::get('city.governorate_id'),['type'=>'select','name'=>'name','value'=>'id','model'=>'Governorate']],

 	 	 ]; return $columes;
  }
 	 		 public function scopeSearch($query, $data) {
 	 	 if(isset($data["name"])){

          $query->where(function ($query) use ($data){
/**/
            foreach (getSupportedLocales() as $locale)
 	 	     $query->orWhere("name->".$locale,"LIKE","%".$data["name"]."%");

          });

       }

 	 	 if(isset($data["status"])){
 	 	   $query->where("status","LIKE","%".$data["status"]."%");}
 	 	 if(isset($data["governorate_id"])){
 	 	   $query->where("governorate_id",$data["governorate_id"]);}
 	 	 return $query ;
 	 	 }

    public function getStatusNameAttribute(){
        if($this->status){
            return ' مفعل';
        }
        return 'غير مفعل';
    }


    public function governorate(){
        return $this->belongsTo(Governorate::class);

    }

    public function regions()
    {
        return $this->hasMany(Region::class );
    }

}
