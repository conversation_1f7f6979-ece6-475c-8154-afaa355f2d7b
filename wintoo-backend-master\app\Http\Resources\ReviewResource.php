<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
      //  dump($this);
        return [
            'id'=>$this->id,
            'user_name'=>optional($this->customer)->username,
            'image_url'=>optional($this->customer)->image_url,
            'customer_id'=>$this->customer->id,
            'product_id'=>$this->product_id,
            'review'=>$this->review,
            'rating'=>$this->rating,
        ];//parent::toArray($request);
    }
}
