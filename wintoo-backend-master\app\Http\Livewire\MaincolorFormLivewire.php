<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Maincolor;
class MaincolorFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'maincolor';
      public $maincolor;
      protected $listeners = ['Maincolor-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "maincolor.name"=>'nullable',
"maincolor.code"=>'nullable',
"maincolor.human_name"=>'nullable',
"maincolor.category_id"=>'nullable',

       ];

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Maincolor::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->maincolor  = $id?Maincolor::find($id):new Maincolor();
          }
      public function render()
          {
              return view('dashboard/maincolor/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->maincolor->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.maincolor');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


