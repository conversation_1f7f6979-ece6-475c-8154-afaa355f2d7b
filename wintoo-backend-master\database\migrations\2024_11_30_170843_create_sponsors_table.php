<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSponsorsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('sponsors', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('name', 191)->nullable()->comment('name');
			$table->string('logo', 191)->nullable()->comment('logo');
			$table->string('video', 191)->nullable()->comment('video');
			$table->text('media_json')->nullable()->comment('media_json');
			$table->boolean('status')->nullable()->comment('status');
			$table->timestamps();
			$table->string('media_thumbnail', 191)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('sponsors');
	}

}
