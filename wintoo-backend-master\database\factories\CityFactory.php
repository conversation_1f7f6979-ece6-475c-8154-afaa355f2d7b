<?php

namespace Database\Factories;

use App\Models\City;
use App\Models\Governorate;
use Illuminate\Database\Eloquent\Factories\Factory;

class CityFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = City::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $random_number =random_int(0,9999);
        $governorate = Governorate::inRandomOrder()->first();
        return [
            'name'=>"مدينة {$random_number}",
            'governorate_id'=>$governorate->id,
            'status'=>$this->faker->randomElement([1,0])
        ];
    }
}
