<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
class SubCategory extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory,HasTranslations;
    protected $connection = "mysql";
    protected $fillable = ['parent_id', 'name','image','main_category_id'];
    protected $appends = ['image_url'];

    public $translatable = ['name'];
    public function getImageUrlAttribute(){
        return $this->image?asset('storage/'.$this->image):'https://www.gravatar.com/brand/'.md5(strtolower(trim($this->image)));

    }
    public function main_category()
    {
        return $this->belongsTo('App\Models\Category', 'main_category_id');
    }
    public function parent()
    {
        return $this->belongsTo('App\Models\SubCategory', 'parent_id');
    }
    public function children()
    {
        return $this->hasMany('App\Models\SubCategory', 'parent_id');
    }
    public function active_children()
    {
        return $this->hasMany('App\Models\SubCategory', 'parent_id')->where('status', 1); //parent_id
    }


    public function sub_products()
    {
        return $this->hasMany(Product::class,'sub_category_id');
    }
    public function sub_sub_products()
    {
        return $this->hasMany(Product::class,'category_id');
    }

    public function scopeIsMain($q){
        return $q->whereNull('parent_id');
    }
    public function scopeIsActive($query){
        return $query->where('status',1);
    }



    public  static function  getColumnLang(){

        $columes=[
            'image'=>['الشعار',1,true,false,[]],
            'name'=>['الاسم',2,true,true,[]],
            'id'=>['رقم التصنيف',2,true,true,[]],
            'category_type'=>['رئيسي/فرعي',3,false,false,[]],
            'belong_to'=>['التصنيف الرئيسي',3,false,false,[]],
            'status'=>['الحالة',3,true,false,[]],
            'actions'=>['الخيارات',4,true,false,['edit','delete']],
        ];

        return $columes;


    }
    public static $searchable=[
        'title'=>['اسم التصنيف'],

        'main_category_id'=>['التصنيف الاساسي',[
            'type'=>'multi_select',
            'model'=>'Category',
            'name'=>'name',
            'value'=>'name'
        ],
        ] ,
        'status'=>['الحالة',[
            'type'=>'select',
            'model'=>[['name'=>'فعال','value'=>1],['name'=>'غير فعال','value'=>0]],
            'name'=>'name',
            'value'=>'value'
        ]],
    ];
    public function setStatus()
    {
        $this->status = !$this->status;
    }


    public function scopeSearch($query, $data)
    {
     //  dd($data);
        if(isset($data['title'])){
            $query->where('name', 'LIKE', '%'.$data['title'].'%');
        }


        if(isset($data['main_category_id'])){
            $search=$data['main_category_id'];

           // $query->where('main_category_id', $search);

           $query->whereHas('main_category', function($q) use($search){
                $q->where('name', 'LIKE', '%'.$search.'%');
            });

        }


        if(isset($data['is_available'])){
            $query->where('status',  $data['is_available']);
        }
        $query->whereNotNull('main_category_id');

        return $query;
    }
    public function scopeSearchForSub($query, $data)
    {
        //sdd($data);
        if(isset($data['title'])){
            $query->where('name', 'LIKE', '%'.$data['title'].'%');
        }



        if(isset($data['sub_category_id'])){
            $search=$data['sub_category_id'];
            $query->where('parent_id',  $data['sub_category_id']);


        }

        $query->whereNotNull('parent_id');

        return $query;
    }

}
