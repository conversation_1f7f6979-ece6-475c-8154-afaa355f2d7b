<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductImagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('product_images', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('image', 191)->nullable();
			$table->boolean('is_main')->nullable()->default(0);
			$table->integer('product_id')->unsigned()->nullable()->index('product_images_product_id_foreign');
			$table->timestamps();
			$table->string('name', 191)->nullable();
			$table->string('store_jan_product_id', 191)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('product_images');
	}

}
