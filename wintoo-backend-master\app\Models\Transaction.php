<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded = [];
    protected $table ='transactions' ;


    public function order()
    {
        return $this->belongsTo(Order::class ,'order_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class ,'store_id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class ,'customer_id');
    }


}
