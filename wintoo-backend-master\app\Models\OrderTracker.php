<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderTracker extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    protected $guarded = [];
    protected $appends = ['status_name'];

    public function getStatusNameAttribute(){
        if($this->status == -2){
            return "مرفوض";
        }
        if($this->status == -1){

            return "ملغي";
        }
        if($this->status == 0){

            return "جديد";
        }
        if($this->status == 1){

            return "قيد التجهيز";
        }
        if($this->status == 2){

            return "جاري التوصيل";
        }
        if($this->status == 3){

            return "تم الشحن";
        }
        if($this->status == 4){

            return "مكتمل";
        }
        return 'غير معرف';

    }

}
