<?php

namespace App\Http\Livewire;

use App\Models\Attribute;
use App\Models\Store;
use App\Models\SubCategory;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\ProductsRequestsCategory;
class RentalRequestsCategoryLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['RentalRequestsCategory-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'storeCategory';
        public function mount()
            {
                $searchable = ProductsRequestsCategory::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =ProductsRequestsCategory::getColumnLang();
                $this->searchable =ProductsRequestsCategory::getSearchable();
                $this->search_array["type"] = "rental_request" ;

                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $this->search_array["type"] = "rental_request" ;

               $data =ProductsRequestsCategory::search($this->search_array);
               $data=$data->orderBy("order","asc")->paginate($this->page_length);
               return view('dashboard/rentalRequestsCategory/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }

        public function search(){
                    $this->resetPage();
        }

        public function resetSearch(){
            $this->search_array=[];
         }

        public function edit($id){
                 return redirect()->route('dashboard.rentalRequestsCategory.edit',$id);
             }

    public function delete($id){
                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'RentalRequestsCategory-livewire:conformDelete',['id'=>$id]);
             }
    public function conformDelete($id){

        $object = ProductsRequestsCategory::find($id['id']);
        $getStoreConnectedToCategory = Attribute::where("store_category_id",$object->id)->get();
            if($getStoreConnectedToCategory->isNotEmpty()){
                $this->showModal("خطأ ","هذا التنصيف مرتبط بخصائص ولايمكن حذفه.",'error');
                return true;
            }
                 ProductsRequestsCategory::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
    public function setStatus($id){
        $object = ProductsRequestsCategory::find($id);
        $object->status =!$object->status;
        $object->save();
    }
}

