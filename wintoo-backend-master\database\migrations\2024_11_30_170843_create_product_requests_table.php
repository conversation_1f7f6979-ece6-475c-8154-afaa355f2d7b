<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductRequestsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('product_requests', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('product_name', 191)->nullable()->comment('product_name');
			$table->text('description')->nullable()->comment('description');
			$table->integer('store_category_id')->nullable()->comment('store_category_id');
			$table->integer('customer_id')->nullable()->comment('customer_id');
			$table->timestamps();
			$table->string('price', 191)->nullable();
			$table->text('hash')->nullable();
			$table->string('status', 191)->nullable();
			$table->string('type', 191)->nullable();
			$table->float('sell_price', 10, 0)->nullable()->default(0);
			$table->float('buy_price_from', 10, 0)->nullable()->default(0);
			$table->float('buy_price_to', 10, 0)->nullable()->default(0);
			$table->string('store_id', 191)->nullable();
			$table->integer('currency_id')->nullable()->default(1);
			$table->integer('country_id')->nullable();
			$table->integer('city_id')->nullable();
			$table->integer('region_id')->nullable();
			$table->integer('governorate_id')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('product_requests');
	}

}
