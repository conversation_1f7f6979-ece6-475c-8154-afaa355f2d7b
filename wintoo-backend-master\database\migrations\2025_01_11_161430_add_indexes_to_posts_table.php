<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexesToPostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->index('store_id');
            $table->index('media_thumbnail');
            $table->index('created_at');
            $table->index('type');
            $table->index('status');
            $table->index('customer_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropIndex(['store_id']);
            $table->dropIndex(['media_thumbnail']);
            $table->dropIndex(['created_at']);
            $table->dropIndex(['type']);
            $table->dropIndex(['status']);
            $table->dropIndex(['customer_id']);
        });
    }
}
