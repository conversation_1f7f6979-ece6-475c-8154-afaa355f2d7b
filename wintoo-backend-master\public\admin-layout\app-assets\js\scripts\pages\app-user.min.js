$(document).ready(function(){var l={defaultColDef:{sortable:!0},enableRtl:"rtl"==$("html").attr("data-textdirection"),columnDefs:[{headerName:"ID",field:"id",width:125,filter:!0,checkboxSelection:!0,headerCheckboxSelectionFilteredOnly:!0,headerCheckboxSelection:!0},{headerName:"Username",field:"username",filter:!0,width:175,cellRenderer:function(e){return"<span class='avatar'><img src='"+e.data.avatar+"' height='32' width='32'></span>"+e.value}},{headerName:"Email",field:"email",filter:!0,width:225},{headerName:"Name",field:"name",filter:!0,width:200},{headerName:"Country",field:"country",filter:!0,width:150},{headerName:"Role",field:"role",filter:!0,width:150},{headerName:"Status",field:"status",filter:!0,width:150,cellRenderer:function(e){return"active"==e.value?"<div class='badge badge-pill badge-light-"+"success"+"' >"+e.value+"</div>":"blocked"==e.value?"<div class='badge badge-pill badge-light-"+"danger"+"' >"+e.value+"</div>":"deactivated"==e.value?"<div class='badge badge-pill badge-light-"+"warning"+"' >"+e.value+"</div>":void 0},cellStyle:{"text-align":"center"}},{headerName:"Verified",field:"is_verified",filter:!0,width:125,cellRenderer:function(e){return 1==e.value?"<div class='bullet bullet-sm bullet-"+"success"+"' ></div>":0==e.value?"<div class='bullet bullet-sm bullet-"+"secondary"+"' ></div>":void 0},cellStyle:{"text-align":"center"}},{headerName:"Department",field:"department",filter:!0,width:150},{headerName:"Actions",field:"transactions",width:150,cellRenderer:function(e){var t=document.createElement("span"),i=document.createElement("i"),a=document.createAttribute("class");return a.value="users-delete-icon feather icon-trash-2",i.setAttributeNode(a),i.addEventListener("click",function(){deleteArr=[e.data],l.api.updateRowData({remove:deleteArr})}),t.appendChild($.parseHTML("<a href='app-user-edit.html'><i class= 'users-edit-icon feather icon-edit-1 mr-50'></i></a>")[0]),t.appendChild(i),t}}],rowSelection:"multiple",floatingFilter:!0,filter:!0,pagination:!0,paginationPageSize:20,pivotPanelShow:"always",colResizeDefault:"shift",animateRows:!0,resizable:!0};if(document.getElementById("myGrid")){var e=document.getElementById("myGrid");agGrid.simpleHttpRequest({url:"../../../app-assets/data/users-list.json"}).then(function(e){l.api.setRowData(e)}),$(".ag-grid-filter").on("keyup",function(){var e;e=$(this).val(),l.api.setQuickFilter(e)}),$(".sort-dropdown .dropdown-item").on("click",function(){var e,t=$(this);e=t.text(),l.api.paginationSetPageSize(Number(e)),$(".filter-btn").text("1 - "+t.text()+" of 50")}),$(".ag-grid-export-btn").on("click",function(e){l.api.exportDataAsCsv()});function t(e,t){var i=null;"all"!==t&&(i={type:"equals",filter:t}),l.api.getFilterInstance(e).setModel(i),l.api.onFilterChanged()}$("#users-list-role").on("change",function(){var e=$("#users-list-role").val();t("role",e)}),$("#users-list-verified").on("change",function(){var e=$("#users-list-verified").val();t("is_verified",e)}),$("#users-list-status").on("change",function(){var e=$("#users-list-status").val();t("status",e)}),$("#users-list-department").on("change",function(){var e=$("#users-list-department").val();t("department",e)}),$(".users-data-filter").click(function(){$("#users-list-role").prop("selectedIndex",0),$("#users-list-role").change(),$("#users-list-status").prop("selectedIndex",0),$("#users-list-status").change(),$("#users-list-verified").prop("selectedIndex",0),$("#users-list-verified").change(),$("#users-list-department").prop("selectedIndex",0),$("#users-list-department").change()}),new agGrid.Grid(e,l)}0<$("#users-language-select2").length&&$("#users-language-select2").select2({dropdownAutoWidth:!0,width:"100%"}),0<$("#users-music-select2").length&&$("#users-music-select2").select2({dropdownAutoWidth:!0,width:"100%"}),0<$("#users-movies-select2").length&&$("#users-movies-select2").select2({dropdownAutoWidth:!0,width:"100%"}),0<$(".birthdate-picker").length&&$(".birthdate-picker").pickadate({format:"mmmm, d, yyyy"}),0<$(".users-edit").length&&$("input,select,textarea").not("[type=submit]").jqBootstrapValidation()});