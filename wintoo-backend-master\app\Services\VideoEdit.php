<?php

namespace App\Services;


use FFMpeg\Filters\Video\ResizeFilter;
use FFMpeg\Filters\Video\VideoFilters;
use ProtoneMedia\LaravelFFMpeg\Exporters\EncodingException;

class VideoEdit
{


    public static function generateVideoThumbnail($video_path, $from_seconds = 0)
    {
        $file_name = basename($video_path);
        $file_generate_name =  md5($file_name . time());

        $thumbnail = "videos/thumbnails/" . $file_generate_name . ".png";
        $store =  \FFMpeg::fromDisk('public')
            ->open($video_path)
            ->getFrameFromSeconds($from_seconds)
            ->export()
            ->toDisk("public")
            ->save($thumbnail);

        return $thumbnail;
    }


    public static function compressVideo($video_path)
    {
        $out = str_replace(".", "_", $video_path) . "-compressed.mp4";
        try {
            $store =  \FFMpeg::fromDisk('public')
                ->open($video_path)
                ->export()
                ->toDisk("public")
                ->inFormat((new \FFMpeg\Format\Video\X264)->setKiloBitrate(1000))
                ->beforeSaving(function ($commands) {

                    // $commands[] ="-preset ultrafast" ;
                    //  $commands[] ="-threads 4" ;

                    return $commands;
                })
                ->addFilter(function (VideoFilters $filters) use ($video_path) {
                    // $filters->rotate();

                    //  $filters->resize(new \FFMpeg\Coordinate\Dimension(720 , 1280 ),ResizeFilter::RESIZEMODE_SCALE_WIDTH);


                    if (self::isHorizontal($video_path)) {
                        $filters->custom('scale=trunc(oh*a/2)*2:' . "720");
                    } else {
                        $filters->custom('scale=' . "720" . ':trunc(ow/a/2)*2');
                    }
                })
                ->save($out);
        } catch (EncodingException $exception) {
            $command = $exception->getCommand();
            $errorLog = $exception->getErrorOutput();
            dd($command, $errorLog);
        }
        return $out;
    }
    public static function compressVideo2($video_path)
    {
        $file_name = basename($video_path);

        $out = str_replace(".", "_", $video_path) . "mmm.mp4";
        try {
            $ffmpeg =  \FFMpeg::fromDisk('public')
                ->open($video_path)
                ->export()
                ->toDisk("public")
                ->inFormat(new \FFMpeg\Format\Video\X264)
                ->beforeSaving(function ($commands) {

                    // $commands[] ="-preset ultrafast" ;
                    //  $commands[] ="-threads 4" ;
                    //var_dump();

                    var_dump(implode(" ", $commands[0]));
                    var_dump(implode(" ", $commands[1]));
                    return $commands;
                })
                ->addFilter(function (VideoFilters $filters) use ($video_path) {
                    // $filters->rotate();

                    //  $filters->resize(new \FFMpeg\Coordinate\Dimension(720 , 1280 ),ResizeFilter::RESIZEMODE_SCALE_WIDTH);


                    if (self::isHorizontal($video_path)) {

                        $filters->custom('scale=trunc(oh*a/2)*2:' . "720");
                    } else {
                        $filters->custom('scale=' . "720" . ':trunc(ow/a/2)*2');
                    }
                })
                ->save($out);

            dd($ffmpeg);
        } catch (EncodingException $exception) {
            $command = $exception->getCommand();
            $errorLog = $exception->getErrorOutput();
            dd($command, $errorLog);
        }
        return $out;
    }


    public function generateFileName($length)
    {
        return mt_rand(pow(10, ($length - 1)), pow(10, $length) - 1);
    }


    public static function isHorizontal($file): bool
    {
        $dimensions = \FFMpeg::fromDisk('public')->open($file)->getVideoStream()->all();
        if (isset($dimensions['tags']['rotate']) && $dimensions['tags']['rotate'] == 90) {
            return true;
        } else {
            return false;
        }
    }
}
