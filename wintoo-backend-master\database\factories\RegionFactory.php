<?php

namespace Database\Factories;

use App\Models\City;
use App\Models\Region;
use Illuminate\Database\Eloquent\Factories\Factory;

class RegionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Region::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $random_number =random_int(0,9999);
        $city = City::inRandomOrder()->first();
        return [
            'name'=>"منطقة {$random_number}",
            'city_id'=>$city->id,
            'status'=>$this->faker->randomElement([1,0])
        ];
    }
}
