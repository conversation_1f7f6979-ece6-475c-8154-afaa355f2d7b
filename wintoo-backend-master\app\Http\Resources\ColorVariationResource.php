<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Maincolor;
use App\Models\Size;
use App\Models\Variation;
use Illuminate\Http\Resources\Json\JsonResource;

class ColorVariationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */



    public function toArray($request)
    {



        $size=Variation::where('product_color_id',$this->id)->get();




              return [
                  'id' => $this->id,
                  'color' => $this->color_ar,
                  'human_color' => $this->human_name ? Maincolor::find($this->main_color_id)->human_name: '',
                  'code' => $this->code,
                  'size' => SizeVariationResource::collection($size),

              ];



    }
}
