<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="512" width="512" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-18 -12 36 24">
  <defs>
    <clipPath id="a">
      <path fill-opacity=".67" d="M-18-18h36v36h-36z"/>
    </clipPath>
  </defs>
  <path fill="#fff" d="M-18-18h36v36h-36z"/>
  <g clip-path="url(#a)">
    <path fill="#fff" d="M-18-12h36v24h-36z"/>
    <path d="M0-18v36M-18 0h36" stroke="#e8112d" stroke-width="6" fill="none"/>
    <path id="b" fill="#f9dd16" d="M-9 2l1-1h9v-2h-9l-1-1z"/>
    <use xlink:href="#b" transform="rotate(90)" height="24" width="36"/>
    <use xlink:href="#b" transform="rotate(-90)" height="24" width="36"/>
    <use xlink:href="#b" transform="scale(-1)" height="24" width="36"/>
  </g>
</svg>
