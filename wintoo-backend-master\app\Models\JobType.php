<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
class JobType extends Model
{
use \App\Traits\PropertyGetter;

 use HasTranslations;
     protected $guarded = [];
     public $translatable = ['name'];
     public $table = "jobs_types";

     public  static function  getColumnLang(){
 	 	$columes=[
 	 //	'icon_url'=>[\Lang::get('storeCategory.icon') ,1,true,false, ['type'=>'image','actions'=>null] ],
 	 	'name'=>[\Lang::get('storeCategory.name') ,1,true,false, ['type'=>'string','actions'=>null] ],
 	 	'status'=>[\Lang::get('storeCategory.status') ,1,true,false, ['type'=>'boolean','actions'=>null] ],
 	 	   'actions'=>['الخيارات',2,true,false,['type'=>'button','actions'=>['edit','delete']]],
 	 	];

 	 	 return $columes;
  }
     public static function getSearchable(){
 	 	$columes=[
 	 	'name'=>[\Lang::get('lang.name')],

 	 	 ]; return $columes;
  }
     public function scopeSearch($query, $data)
     {

         if (isset($data["name"])) {

             $query->where(function ($query) use ($data) {
                 /**/
                 foreach (getSupportedLocales() as $locale)
                     $query->orWhere("name->" . $locale, "LIKE", "'%" . $data["name"] . "%'");

             });
         }
             return $query;

     }

    public function scopeIsActive($query){
        return $query->where('status',true);
    }


}
