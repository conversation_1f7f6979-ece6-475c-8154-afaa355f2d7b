<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Currency;
use App\Models\Maincolor;
use App\Models\Product;
use App\Models\Size;
use App\Models\Variation;
use Illuminate\Http\Resources\Json\JsonResource;

class VariationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

       // dump(Size::find($this->product_size_id));
        $size= is_null($this->product_size)?Size::find($this->product_size_id):Size::where('name',$this->product_size)->first();

       $product = Product::find($this->product_id);



        $price =$this->display_selling_price;

        $new_price = $this->offer_type ? $this->price_after_offer : $this->display_selling_price ;
        $new_price = $this->price_after_offer ;

        return [
           'id'=>$this->id,
            'price'=>getRound($price ),
            'new_price'=>getRound($new_price),


            "charged_price"=> getRound(getPriceForUser($price,Currency::find($product->currency_id))),
            "charged_new_price"=>getRound(getPriceForUser($new_price,Currency::find($product->currency_id))),
            'charged_currency'=> new CurrencyResource(getCurrentUserCurrency()),

//           'price'=>$price,
//           'new_price'=>$new_price,
            'size'=>new SizeResource($size),
             'colors'=>new MainColorResource(Maincolor::find($this->product_color_id)),
            'is_in_stock' =>$this->is_in_stock,
            'stock_quantity' =>$this->stock_quantity,
            'status' =>(bool)$this->status,
         //   'parent' =>parent::toArray($request)

        ];
    }
}
