<?php

namespace App\Http\Resources;

use App\Models\Currency;
use App\Models\Favorite;
use App\Models\Follower;
use App\Models\RentalRequest;

use App\Models\Reviews;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class RentalRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
     
     
     
    public function toArray($request)
    {




    $get_same_request = $this->getMatchingRentalRequestsAndNotify(false);


        $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');
        return [
            "id"=>$this->id,
            "name"=>$this->name,
            "store_category_id"=>$this->store_category_id,
            "image_url"=>$this->media[0]?$this->media[0]->media_url:null,
            "request_name"=>$this->request_name,
            "request_title"=>$this->request_title,
            "store_category"=>new StoreCategoryResource($this->store_category),
            "customer_id"=>$this->customer_id,
            "customer"=>new CustomerResource($this->customer),
            "store"=>new StoreResource($this->store),
            "hash"=>$this->hash,
            "type"=>$this->type,
            //"type"=>$this->type_name,
            "type_name"=>$this->type_name,
            "offer_price"=>$this->offer_price,
            "search_price_from"=>$this->search_price_from,
            "search_price_to"=>$this->search_price_to,
            "similarـrequest_count"=>count($get_same_request),
            "created_at"=>$created_at,
            "similarـrequests" => count($get_same_request)>0?RentalRequesSimilarResource::collection($get_same_request):[],
            "attributes"=>AttributeResource::collection($this->attributes),
            "media" => RentalRequestMediaResource::collection($this->media??[]) ,
            'country'=>new AreaResource($this->country),
            'governorate'=>new AreaResource($this->governorate),
            'city'=>new AreaResource($this->city),
            'region'=>new AreaResource($this->region),
            "full_address"=> $this->full_address,
            "rental_period"=>$this->rental_period,
            "rental_period_text"=>$this->rental_period_text,
            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource($this->currency),
        ];
    }
}
