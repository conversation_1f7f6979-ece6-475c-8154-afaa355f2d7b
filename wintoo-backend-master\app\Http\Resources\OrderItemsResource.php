<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            'id'=>$this->id,
            'price'=>$this->price,
            'product'=>new ProductCartResource($this->product),
            'variation'=>new VariationResource($this->variation),
            'qty'=>$this->qty
        ];//parent::toArray($request);
    }
}
