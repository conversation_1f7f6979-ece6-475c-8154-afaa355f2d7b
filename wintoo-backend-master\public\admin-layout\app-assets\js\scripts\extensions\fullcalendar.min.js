document.addEventListener("DOMContentLoaded",function(){var e={primary:"#7367f0",success:"#28c76f",danger:"#ea5455",warning:"#ff9f43"},n={primary:"Others",success:"Business",danger:"Personal",warning:"Work"},a=$(".cal-category-bullets").html(),t="",o="",d=document.getElementById("fc-default"),c=new FullCalendar.Calendar(d,{plugins:["dayGrid","timeGrid","interaction"],customButtons:{addNew:{text:" Add",click:function(){var a=(new Date).toISOString().slice(0,10);$(".modal-calendar").modal("show"),$(".modal-calendar .cal-submit-event").addClass("d-none"),$(".modal-calendar .remove-event").addClass("d-none"),$(".modal-calendar .cal-add-event").removeClass("d-none"),$(".modal-calendar .cancel-event").removeClass("d-none"),$(".modal-calendar .add-category .chip").remove(),$("#cal-start-date").val(a),$("#cal-end-date").val(a),$(".modal-calendar #cal-start-date").attr("disabled",!1)}}},header:{left:"addNew",center:"dayGridMonth,timeGridWeek,timeGridDay",right:"prev,title,next"},displayEventTime:!1,navLinks:!0,editable:!0,allDay:!0,navLinkDayClick:function(a){$(".modal-calendar").modal("show")},dateClick:function(a){$(".modal-calendar #cal-start-date").val(a.dateStr).attr("disabled",!0),$(".modal-calendar #cal-end-date").val(a.dateStr)},eventClick:function(a){$(".modal-calendar").modal("show"),$(".modal-calendar #cal-event-title").val(a.event.title),$(".modal-calendar #cal-start-date").val(moment(a.event.start).format("YYYY-MM-DD")),$(".modal-calendar #cal-end-date").val(moment(a.event.end).format("YYYY-MM-DD")),$(".modal-calendar #cal-description").val(a.event.extendedProps.description),$(".modal-calendar .cal-submit-event").removeClass("d-none"),$(".modal-calendar .remove-event").removeClass("d-none"),$(".modal-calendar .cal-add-event").addClass("d-none"),$(".modal-calendar .cancel-event").addClass("d-none"),$(".calendar-dropdown .dropdown-menu").find(".selected").removeClass("selected");var e=a.event.extendedProps.dataEventColor,d=n[e];$(".modal-calendar .chip-wrapper .chip").remove(),$(".modal-calendar .chip-wrapper").append($("<div class='chip chip-"+e+"'><div class='chip-body'><span class='chip-text'> "+d+" </span></div></div>"))}});c.render(),$("#basic-examples .fc-right").append(a),$(".modal-calendar .cal-submit-event").on("click",function(){$(".modal-calendar").modal("hide")}),$(".remove-event").on("click",function(){c.getEventById("newEvent").remove()}),0<$("td:not(.fc-event-container)").length&&$(".modal-calendar").on("hidden.bs.modal",function(a){$(".modal-calendar .form-control").val("")}),$(".modal-calendar .form-control").on("keyup",function(){1<=$(".modal-calendar #cal-event-title").val().length?$(".modal-calendar .modal-footer .btn").removeAttr("disabled"):$(".modal-calendar .modal-footer .btn").attr("disabled",!0)}),$(document).on("click",".fc-day",function(){$(".modal-calendar").modal("show"),$(".calendar-dropdown .dropdown-menu").find(".selected").removeClass("selected"),$(".modal-calendar .cal-submit-event").addClass("d-none"),$(".modal-calendar .remove-event").addClass("d-none"),$(".modal-calendar .cal-add-event").removeClass("d-none"),$(".modal-calendar .cancel-event").removeClass("d-none"),$(".modal-calendar .add-category .chip").remove(),$(".modal-calendar .modal-footer .btn").attr("disabled",!0),t=e.primary,o="primary"}),$(".calendar-dropdown .dropdown-menu .dropdown-item").on("click",function(){var a=$(this).data("color");t=e[a],eventTag=n[a],o=a,$(".cal-add-event").on("click",function(){c.addEvent({color:t,dataEventColor:o,className:o})}),$(".calendar-dropdown .dropdown-menu").find(".selected").removeClass("selected"),$(this).addClass("selected"),$(".modal-calendar .chip-wrapper .chip").remove(),$(".modal-calendar .chip-wrapper").append($("<div class='chip chip-"+a+"'><div class='chip-body'><span class='chip-text'> "+eventTag+" </span></div></div>"))}),$(".cal-add-event").on("click",function(){$(".modal-calendar").modal("hide");var a=$("#cal-event-title").val(),e=$("#cal-start-date").val(),d=$("#cal-end-date").val(),n=$("#cal-description").val(),l=new Date(d);c.addEvent({id:"newEvent",title:a,start:e,end:l,description:n,color:t,dataEventColor:o,allDay:!0})}),$(".pickadate").pickadate({format:"yyyy-mm-dd"})});