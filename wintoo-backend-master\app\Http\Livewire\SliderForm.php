<?php

namespace App\Http\Livewire;

use App\Models\City;
use App\Models\Governorate;
use Livewire\Component;
use App\Models\Slider;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use App\Traits\Alert;
class SliderForm extends Component
{
    use WithFileUploads,Alert,PublicFunction;
    public $title = "";
    public $image = null;
    public $selected=[];
    public $options=[];
    public $slider;
    public  $rules=[
                 'slider.link' => 'nullable',
                 'slider.type' => 'nullable',
                 'slider.image' => 'nullable',
                 'slider.product_or_category_id' => 'nullable',
                 'slider.description' => 'nullable',
                 'slider.is_main' => 'nullable',
                'slider.order' => 'required|numeric|unique:sliders,order',
    ];
    public function mount($id =null)
    {

        $this->title = \Lang::get('lang.add_slider')  ;
        $slider =  Slider::find($id);
        $this->slider= $id?Slider::find($id):new Slider();
        if($id){
            $this->slider->image=$slider->sliderUrl();
            $this->title = \Lang::get('lang.edit_slider')  ;
            $this->is_main= $slider->is_main;
        }

    }

    public function render()
    {
        $this->options=[
            ['value_id'=>'category_page','value'=>'category_page' ,'id'=>'category_page','name'=>'صفحة التصنيفات'],
            ['value_id'=>'product_page','value'=>'product_page' ,'id'=>'product_page','name'=>'صفحة المنتجات'],
            ['value_id'=>'other_page','value'=>'other_page' ,'id'=>'other_page','name'=>'صفحة اخرى'],

        ];

        if(in_array('sliders_create'||'sliders_edit',$this->actions_permission()) ) {
            return view('dashboard.sliders.form')->extends('dashboard_layout.main');
        }else{
            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }

    }

    public function save(){
        $this->validate();

        $filename = $this->image?$this->image->store('/','sliders'):null;
        $this->slider->image=$filename;
        $this->slider->save();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }

    public function update($id){
        $slider =  Slider::find($id);
        $this->validate(
            [
                'slider.order' => 'required|numeric|unique:sliders,order,'.$slider->id,
            ]);

        $filename = $this->image?$this->image->store('/','sliders'):$slider->image;
        $this->slider->image=$filename;
        $this->slider->save();
        $this->slider->image=$slider->sliderUrl();
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    }
}
