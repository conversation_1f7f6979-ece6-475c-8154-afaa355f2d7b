<?php

namespace App\Http\Resources;

use App\Models\Favorite;
use App\Models\Follower;
use App\Models\ProductRequest;
use App\Models\Reviews;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class RentalRequesSimilarResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


        $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');

        return [
            "id"=>$this->id,
            "store_category_id"=>$this->store_category_id,
            "name"=>$this->name,
            "store_category"=>new StoreCategoryResource($this->store_category),
            "customer_id"=>$this->customer_id,
            "customer"=>new CustomerResource($this->customer),
            "store"=>new StoreResource($this->store),
            "hash"=>$this->hash,
            "offer_price"=>$this->offer_price,
            "search_price_from"=>$this->search_price_from,
            "search_price_to"=>$this->search_price_to,
            "type"=>$this->type,
            "type_name"=>$this->type_name,
            "created_at"=>$created_at,
            "attributes"=>AttributeResource::collection($this->attributes),
            "media" => RentalRequestMediaResource::collection($this->media??[]),
            'country'=>new AreaResource($this->country),
            'governorate'=>new AreaResource($this->governorate),
            'city'=>new AreaResource($this->city),
            'region'=>new AreaResource($this->region),
            "full_address"=> $this->full_address,
            "rental_period"=>$this->rental_period,
            "rental_period_text"=>$this->rental_period_text,
            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource($this->currency),

        ];

    }
}
