<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
use App\Http\Resources\CategoryResource;
class Slider extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
    use Loggable;
    protected $fillable =['link','image','order','description','is_main'];


    public static $searchable=[
        'link'=>[],
    ];
    public function sliderUrl(){
        return $this->image?asset('storage/sliders/'.$this->image):'https://www.gravatar.com/avatar/'.md5(strtolower(trim($this->image)));
    }


    protected $appends=['image_url','slider_url','category'];
    public function getImageUrlAttribute(){
        return asset('storage/sliders/' . $this->image);
    }
    public function getSliderUrlAttribute(){
        return asset($this->link);
    }

    public  static function  getColumnLang(){

        $columes=[
            'image'=>['صورة السلايدر',1,true,false,[]],
            'order'=>['الترتيب',2,true,true,[]],
            'is_main'=>['رئيسي/فرعي',3,true,false,[]],
            'actions'=>['الخيارات',4,true,false,['edit','show','delete']],
        ];

        return $columes;


    }
    public  function  getDescriptionAttribute(){
      return strip_tags($this->decription)   ;
    }

    public function getCategoryAttribute(){

        if($this->type =='category_page'){
            return new CategoryResource(Category::find($this->product_or_category_id)) ;
        }
    }
}
