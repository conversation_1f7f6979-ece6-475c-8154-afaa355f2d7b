<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\App;

class NotificationApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $msg = json_decode($this->data['msg'],true);

        $msg = $msg!=null && is_array($msg)?$msg[App::getLocale()] : $this->data['msg'];

        return [
            "title"=>isset($this->data['msg'])?$msg:null,
            "details"=> isset($this->data['data']['body'])?$this->data['data']['body']:null,
            "object_id"=>isset($this->data['data']['id'])?(int)$this->data['data']['id']:null,
            "type"=>isset($this->data['type'])?$this->data['type']:null,
            "link"=>isset($this->data['data']['link'])?$this->data['data']['link']:null,
            "id"=>$this->id,
            "created_at"=> $this->created_at->format('d-m-Y h:i A'),
            "read_at"=> $this->read_at

        ];
    }
}
