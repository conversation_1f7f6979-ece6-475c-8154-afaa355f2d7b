<?php

namespace App\Http\Livewire;

use App\Models\Category;
use Livewire\Component;
use App\Traits\PublicFunction;
use App\Traits\Alert;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
class CategoryLivewire extends Component
{
    use PublicFunction , Alert,WithPagination,WithFileUploads;
    use Alert;
    public $columes;
    public $category=['image_url'=>''];
    public $image;
    public $banner;
    public $type =0;
    public $page_length = 10;
    public $search ,$user_id;
    public $model_title="";
    public $sortBy="created_at";
    public $store_categories=[];
    public $sortDirection="desc";
    protected $paginationTheme = 'bootstrap';
    protected $listeners = ['category-livewire:conformDelete' => 'conformDelete'];

    public function mount()
    {

        $this->columes =Category::getColumnLang();
        $this->model_title='اضافة تصنيف';
        //dd($this->columes);
        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);

    }

    public function render()
    {

        $data = Category::whereNull('parent_id');
        if($this->search){
            $this->resetPage();
            $search = $this->search;
            $data->where('name','LIKE','%'.$search.'%');
        }
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);


        if(in_array('categories_show',$this->actions_permission()) ) {
            return view('dashboard.categories.index',[ 'data'=>$data])->extends('dashboard_layout.main');
        }else{

            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }
    }

    public function add(){
        $this->category =[
            'name'=>'',
            'image'=>'',
            'banner_url'=>'',
            'banner'=>'',
            'image_url'=>'',
            'status'=>true
        ];
        $this->showDataModel('basic','show');
    }
    public function save(){
     //   dd($this->store_categories);
        $this->validate([
                'category.name.ar' => 'required',
                'category.name.tr' => 'required',
                'category.name.en' => 'required',

        ]);
        $this->showDataModel('basic','hide');
        $filename = $this->image?$this->image->store('/','public'):'';
        $banner_filename = $this->banner?$this->banner->store('/','public'):'';
        $this->category['image']=$filename;
        $this->category['banner']=$banner_filename;

        $create = Category::create($this->category);

        $create->store_category()->sync($this->store_categories);

        $this->showDataModel('basic','hide');
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }

    public  function edit($id){
        $this->image=null;
        $main_category = Category::find($id);
        $this->category  =Category::find($id)->toArray();
        foreach ($main_category->store_category as $main){
            array_push($this->store_categories,$main->id);
        }


       // dd($this->store_categories);

        $this->model_title=\Lang::get('lang.edit');
        $this->showDataModel('basic','show');
    }

    public  function update($id){


        $object = Category::find($id);

        $this->validate([
                'category.name' => 'required']
        );
        $this->showDataModel('basic','hide');
        $filename = $this->image?$this->image->store('/','public'):$object->image;
        $banner_filename = $this->banner?$this->banner->store('/','public'):$object->banner;
        $this->category['image']=$filename;
        $this->category['banner']=$banner_filename;
        $object->update($this->category);
        $object->store_category()->sync($this->store_categories);


    }

    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'category-livewire:conformDelete',['id'=>$id]);
    }

    public function conformDelete($id){
        $category = Category::find($id['id']);
        if($category->products->isNotEmpty()){
            $this->showModal('لا يمكن الحذف','لا يمكن حذف التصنيف  بسبب وجود منتجات مرتبطة بها','error');
            return  true;
        }
        if($category->children->isNotEmpty()){
             $this->showModal('لا يمكن الحذف','لا يمكن حذف التصنيف  بسبب وجود تصنيفات  مرتبطة بها','error');
            return  true;
        }
        Category::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }


    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }


    public function setStatus($id){
        $object = Category::find($id);
        $object->status =!$object->status;
        $object->save();
    }
    public function setMain($id){
        $object = Category::find($id);
        $object->is_main =!$object->is_main;
        $object->save();
    }
}
