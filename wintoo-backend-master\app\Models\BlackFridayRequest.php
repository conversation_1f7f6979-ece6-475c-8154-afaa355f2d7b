<?php

namespace App\Models;

use Carbon\Carbon;
use <PERSON>runcpi\LaravelUserActivity\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlackFridayRequest extends Model
{
use \App\Traits\PropertyGetter;


    use Loggable;
    use HasFactory;
    protected $guarded=[];
    protected $connection = "mysql";
    protected $appends=["status_name","store_name"];

    public  static function  getColumnLang(){
        $columes=[
            'store_name'=>["اسم التاجر" ,1,true,false, ['type'=>'string','actions'=>null] ],
            'price'=>["السعر" ,1,true,false, ['type'=>'string','actions'=>null] ],
            'note'=>["الملاحظات" ,1,true,false, ['type'=>'string','actions'=>null] ],
            'end_at'=>['تاريخ الانتهاء' ,1,true,false, ['type'=>'string','actions'=>null] ],
            'created_at'=>['تاريخ الانشاء' ,1,true,false, ['type'=>'string','actions'=>null] ],
            'status_name'=>['الحالة' ,1,true,false, ['type'=>'string','actions'=>null] ],
           // 'status'=>[\Lang::get('country.status') ,1,true,false, ['type'=>'switch','actions'=>null] ],
            'actions'=>['الخيارات',1,true,false,['type'=>'button','actions'=>['edit','delete']]],
        ];
        return $columes;
    }
    public function scopeSearch($query, $data) {
        if(isset($data["name"])){
            $query->where("name","LIKE","%".$data["name"]."%");}
        if(isset($data["status"])){
            $query->where("status","LIKE","%".$data["status"]."%");}
        return $query ;
    }

    public function scopeIsActive($query, $data) {
        $query->where("store_id",$data)
            ->whereDate('end_at','>=',Carbon::now())
        ->where('status',true);
        return $query ;
    }

    public function IsExpired() {

        return $this->status && strtotime($this->end_at ) < strtotime(Carbon::now());

    }
    public function IsPending() {

        return !$this->status && strtotime($this->end_at) > strtotime( Carbon::now());

    }
    public function IsActive() {

        return $this->status && strtotime($this->end_at) >= strtotime(Carbon::now());

    }

    public function scopeIsActiveRequest($query) {
        $query
            ->whereDate('end_at','>=',Carbon::now())
        ->where('status',true);
        return $query ;
    }
    public static function getSearchable(){
        $columes=[
            'store_id'=>['المتجر',
                ['type'=>'select','name'=>'name','value'=>'id','model'=>'Store']],
        ]; return $columes;
    }
    public function store(){
        return $this->belongsTo(Store::class,"store_id");
    }

    public function getStoreNameAttribute(){
        return $this->store->name ;
    }
    public function getStatusNameAttribute(){
        if($this->status){
            return "فعال";
        }
        return "غير فعال";
        //return $this->store->name ;
    }


    public function setStatus()
    {
        $this->status = !$this->status;
    }
}
