/*========================================================
        DARK LAYOUT
=========================================================*/
@-webkit-keyframes ball-grid-pulse {
  0% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
  50% {
    -webkit-transform : scale(0.5);
            transform : scale(0.5);
    opacity : 0.7;
  }
  100% {
    -webkit-transform : scale(1);
            transform : scale(1);
    opacity : 1;
  }
}
@keyframes ball-grid-pulse {
  0% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
  50% {
    -webkit-transform : scale(0.5);
            transform : scale(0.5);
    opacity : 0.7;
  }
  100% {
    -webkit-transform : scale(1);
            transform : scale(1);
    opacity : 1;
  }
}

.ball-grid-pulse {
  width : 57px;
}
.ball-grid-pulse > div:nth-child(1) {
  -webkit-animation-delay : 0.58s;
          animation-delay : 0.58s;
  -webkit-animation-duration : 1s;
          animation-duration : 1s;
}
.ball-grid-pulse > div:nth-child(2) {
  -webkit-animation-delay : 0.58s;
          animation-delay : 0.58s;
  -webkit-animation-duration : 1.49s;
          animation-duration : 1.49s;
}
.ball-grid-pulse > div:nth-child(3) {
  -webkit-animation-delay : 0.08s;
          animation-delay : 0.08s;
  -webkit-animation-duration : 0.9s;
          animation-duration : 0.9s;
}
.ball-grid-pulse > div:nth-child(4) {
  -webkit-animation-delay : 0.11s;
          animation-delay : 0.11s;
  -webkit-animation-duration : 0.78s;
          animation-duration : 0.78s;
}
.ball-grid-pulse > div:nth-child(5) {
  -webkit-animation-delay : -0.04s;
          animation-delay : -0.04s;
  -webkit-animation-duration : 1.06s;
          animation-duration : 1.06s;
}
.ball-grid-pulse > div:nth-child(6) {
  -webkit-animation-delay : 0.41s;
          animation-delay : 0.41s;
  -webkit-animation-duration : 1.54s;
          animation-duration : 1.54s;
}
.ball-grid-pulse > div:nth-child(7) {
  -webkit-animation-delay : 0.04s;
          animation-delay : 0.04s;
  -webkit-animation-duration : 0.79s;
          animation-duration : 0.79s;
}
.ball-grid-pulse > div:nth-child(8) {
  -webkit-animation-delay : -0.07s;
          animation-delay : -0.07s;
  -webkit-animation-duration : 1.26s;
          animation-duration : 1.26s;
}
.ball-grid-pulse > div:nth-child(9) {
  -webkit-animation-delay : -0.03s;
          animation-delay : -0.03s;
  -webkit-animation-duration : 1.07s;
          animation-duration : 1.07s;
}
.ball-grid-pulse > div {
  background-color : #B8C2CC;
  width : 15px;
  height : 15px;
  border-radius : 100%;
  margin : 2px;
  -webkit-animation-fill-mode : both;
          animation-fill-mode : both;
  display : inline-block;
  float : right;
  -webkit-animation-name : ball-grid-pulse;
          animation-name : ball-grid-pulse;
  -webkit-animation-iteration-count : infinite;
          animation-iteration-count : infinite;
  -webkit-animation-delay : 0;
          animation-delay : 0;
}