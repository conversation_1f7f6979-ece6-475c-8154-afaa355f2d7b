<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttributesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('attributes', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->text('title')->nullable()->comment('title');
			$table->string('type', 191)->nullable()->comment('type');
			$table->boolean('is_required')->nullable()->comment('is_required');
			$table->integer('store_category_id')->nullable()->comment('store_category_id');
			$table->boolean('status')->nullable()->comment('status');
			$table->text('json_data')->nullable()->comment('json_data');
			$table->text('options')->nullable()->comment('json_data');
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('attributes');
	}

}
