<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Variation extends Model
{
use \App\Traits\PropertyGetter;

     protected $guarded = [];
     public  static function  getColumnLang(){
    $columes=[
    'product_id'=>[\Lang::get('variation.product_id') ,1,true,false,[]],
    'variation_id'=>[\Lang::get('variation.variation_id') ,1,false,false,[]],
    'name'=>[\Lang::get('variation.name') ,1,true,false,[]],
    'is_in_stock'=>[\Lang::get('variation.is_in_stock') ,1,true,false,[]],
    'product_size'=>[\Lang::get('variation.product_size') ,1,true,false,[]],
    'product_color'=>[\Lang::get('variation.product_color') ,1,true,false,[]],
    'stock_code'=>[\Lang::get('variation.stock_code') ,1,true,false,[]],
    'currency'=>[\Lang::get('variation.currency') ,1,false,false,[]],
    'selling_price'=>[\Lang::get('variation.selling_price') ,1,true,false,[]],
    'vat_included'=>[\Lang::get('variation.vat_included') ,1,false,false,[]],
    'vat_percent'=>[\Lang::get('variation.vat_percent') ,1,false,false,[]],
    'stock_quantity'=>[\Lang::get('variation.stock_quantity') ,1,false,false,[]],
    'specific_sale_price'=>[\Lang::get('variation.specific_sale_price') ,1,false,false,[]],
    'display_selling_price'=>[\Lang::get('variation.display_selling_price') ,1,false,false,[]],
    'store_selling_price'=>[\Lang::get('variation.store_selling_price') ,1,false,false,[]],
    'display_symbol'=>[\Lang::get('variation.display_symbol') ,1,false,false,[]],
    'display_currency'=>[\Lang::get('variation.display_currency') ,1,false,false,[]],
    'color_en'=>[\Lang::get('variation.color_en') ,1,false,false,[]],
    'size_en'=>[\Lang::get('variation.size_en') ,1,false,false,[]],
    'offer_price'=>[\Lang::get('variation.offer_price') ,1,false,false,[]],
    'offer_type'=>[\Lang::get('variation.offer_type') ,1,false,false,[]],
    'price_after_offer'=>[\Lang::get('variation.price_after_offer') ,1,false,false,[]],
       'actions'=>['الخيارات',21,true,false,['edit','delete']],
    ];
     return $columes;
}
     public static function getSearchable(){
    $columes=['product_id'=>[\Lang::get('Variation.product_id')],
    'name'=>[\Lang::get('Variation.name')],
    'is_in_stock'=>[\Lang::get('Variation.is_in_stock')],

     ]; return $columes;
}
     public function scopeSearch($query, $data) {
     if(isset($data["product_id"])){
       $query->where("product_id","LIKE","%".$data["product_id"]."%");}
     if(isset($data["variation_id"])){
       $query->where("variation_id","LIKE","%".$data["variation_id"]."%");}
     if(isset($data["name"])){
       $query->where("name","LIKE","%".$data["name"]."%");}
     if(isset($data["is_in_stock"])){
       $query->where("is_in_stock","LIKE","%".$data["is_in_stock"]."%");}
     if(isset($data["product_size"])){
       $query->where("product_size","LIKE","%".$data["product_size"]."%");}
     if(isset($data["product_color"])){
       $query->where("product_color","LIKE","%".$data["product_color"]."%");}
     if(isset($data["stock_code"])){
       $query->where("stock_code","LIKE","%".$data["stock_code"]."%");}
     if(isset($data["currency"])){
       $query->where("currency","LIKE","%".$data["currency"]."%");}
     if(isset($data["selling_price"])){
       $query->where("selling_price","LIKE","%".$data["selling_price"]."%");}
     if(isset($data["vat_included"])){
       $query->where("vat_included","LIKE","%".$data["vat_included"]."%");}
     if(isset($data["vat_percent"])){
       $query->where("vat_percent","LIKE","%".$data["vat_percent"]."%");}
     if(isset($data["stock_quantity"])){
       $query->where("stock_quantity","LIKE","%".$data["stock_quantity"]."%");}
     if(isset($data["specific_sale_price"])){
       $query->where("specific_sale_price","LIKE","%".$data["specific_sale_price"]."%");}
     if(isset($data["display_selling_price"])){
       $query->where("display_selling_price","LIKE","%".$data["display_selling_price"]."%");}
     if(isset($data["store_selling_price"])){
       $query->where("store_selling_price","LIKE","%".$data["store_selling_price"]."%");}
     if(isset($data["display_symbol"])){
       $query->where("display_symbol","LIKE","%".$data["display_symbol"]."%");}
     if(isset($data["display_currency"])){
       $query->where("display_currency","LIKE","%".$data["display_currency"]."%");}
     if(isset($data["color_en"])){
       $query->where("color_en","LIKE","%".$data["color_en"]."%");}
     if(isset($data["size_en"])){
       $query->where("size_en","LIKE","%".$data["size_en"]."%");}
     if(isset($data["offer_price"])){
       $query->where("offer_price","LIKE","%".$data["offer_price"]."%");}
     if(isset($data["offer_type"])){
       $query->where("offer_type","LIKE","%".$data["offer_type"]."%");}
     if(isset($data["price_after_offer"])){
       $query->where("price_after_offer","LIKE","%".$data["price_after_offer"]."%");}
     return $query ;
     }

     public function colorsByname(){
          return  $this->belongsTo(Color::class,'product_color','name');
     }
}
