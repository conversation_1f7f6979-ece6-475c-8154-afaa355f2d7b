<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Spatie\Translatable\HasTranslations;

class GeneralNotification extends Notification
{
    use Queueable;




    public $data;
    public $msg;
    public $type;


    /**
     * Send message to all users to be displayed as a toast.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMessageToAllUsers(Request $request)
    {
        // Validate the request
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        // Get the title and description from the request
        $title = $request->input('title');
        $description = $request->input('description');

        // Broadcast the message to all users
        Broadcast::channel('app-notifications')->whisper('toastMessage', [
            'title' => $title,
            'description' => $description,
        ]);

        // Return a success response
        return response()->json(['message' => 'Toast message sent successfully!']);
    }

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($msg,$type,$data)
    {

        $this->msg = $msg;
        $this->type =$type;
        $this->data = $data;


    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->line('The introduction to the notification.')
                    ->action('Notification Action', url('/'))
                    ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'msg' =>$this->msg,
            'type' =>$this->type,
            'data'=>$this->data,
        ];
    }
}
