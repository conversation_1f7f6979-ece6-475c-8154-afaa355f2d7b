<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('users', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('name', 191);
			$table->string('email', 191)->unique();
			$table->dateTime('email_verified_at')->nullable();
			$table->string('password', 191);
			$table->text('two_factor_secret')->nullable();
			$table->text('two_factor_recovery_codes')->nullable();
			$table->string('remember_token', 100)->nullable();
			$table->bigInteger('current_team_id')->unsigned()->nullable();
			$table->text('profile_photo_path')->nullable();
			$table->timestamps();
			$table->integer('role_id')->unsigned()->nullable()->index('users_role_id_foreign');
			$table->string('avatar', 191)->nullable();
			$table->string('type', 191)->nullable();
			$table->integer('store_id')->nullable();
			$table->boolean('status')->nullable()->default(1);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('users');
	}

}
