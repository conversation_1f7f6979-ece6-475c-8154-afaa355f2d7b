<?php

namespace App\Http\Livewire;

use App\Models\JobRequest;
use App\Models\Store;
use App\Models\SubCategory;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\JobType;
class JobTypeLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['JobType-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'jobType';
        public function mount()
            {
                $searchable = JobType::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =JobType::getColumnLang();
                $this->searchable =JobType::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =JobType::search($this->search_array);
               $data=$data->orderBy("created_at","desc")->paginate($this->page_length);
               return view('dashboard/jobType/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }

        public function search(){
                    $this->resetPage();
        }

        public function resetSearch(){
            $this->search_array=[];
         }

        public function edit($id){
                 return redirect()->route('dashboard.jobType.edit',$id);
             }

    public function delete($id){

     $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'JobType-livewire:conformDelete',['id'=>$id]);

    }


    public function conformDelete($id){

        $getStoreConnectedToCategory = JobRequest::where("job_type_id",$id)->get();
            if($getStoreConnectedToCategory->isNotEmpty()){
                $this->showModal("خطأ ","هذا التنصيف مرتبط بطلبات ولا يمكن حذفه.",'error');
                return true;
            }
                 JobType::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
    public function setStatus($id){
        $object = JobType::find($id);
        $object->status =!$object->status;
        $object->save();
    }
}

