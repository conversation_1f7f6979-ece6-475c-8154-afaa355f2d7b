<?php

namespace App\Http\Livewire;

use App\Models\Customer;
use App\Models\Product;
use Livewire\Component;
use App\Traits\Alert;
use App\Traits\PublicFunction;
use Livewire\WithPagination;
class ProductLivewire extends Component
{
    use PublicFunction , Alert ,WithPagination;
    public $columes;
    public $page_length = 10;
    public $search ;
    public $model_title="";
    public $sortBy="created_at";
    public $sortDirection="desc";
    public $searchable;
    public  $search_array=[];
    protected $listeners = ['products-livewire:conformDelete' => 'conformDelete'];
    protected $paginationTheme = 'bootstrap';
    public  $product,$message,$notification;

    public function mount()
    {
        $searchable = Product::$searchable;
        $this->searchable =collect($searchable);
        $search_array['search']=request()->query('search',$this->search);

        $this->columes =Product::getColumnLang();

        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);

    }
    public function render()
    {
        if (auth()->user()->type=="STORE"){
            $this->search_array["store_id"]=auth()->user()->store->id;
        }

        $data = Product::search($this->search_array);
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);


        if(in_array('products_show',$this->actions_permission()) ) {
            return view('dashboard.products.index',[ 'data'=>$data])->extends('dashboard_layout.main');
        }else{

            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }
    }

    public function edit($id){
        return redirect()->route('dashboard.products.edit',$id);
    }


    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'products-livewire:conformDelete',['id'=>$id]);
    }
    public function conformDelete($id){


        Product::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }


    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }

    public function resetSearch(){
        $this->search_array=[];
    }


    public function show_notification_model($id){
        $this->product = Product::find($id);
        $this->showDataModel('basic','show');
    }


    public function save_notification(){
        $customers = Customer::all();
        foreach ($customers as $customer){
            $data=[
                'title' => $this->notification['title'],
                'body' => $this->notification['description'],
                'id'=>$this->product->id,
                'type'=>'product',
                'link'=>route('product.index',$this->product->id)
            ];
            fcmNotification($customer,$data);
            \Notification::send($customer,
                new \App\Notifications\GeneralNotification(
                    $data['title'],
                    'product',
                    $data
                ));
        }

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
        $this->showDataModel('basic','hide');
    }



    public function setStatus($id){
        $object = Product::find($id);
        $object->status =!$object->status;
        $object->save();
    }



    public function search(){
        $this->resetPage();
    }

    public function updatingPageLength(){
        $this->resetPage();
    }

    function updated($data){
        //    dd($data);
        if($data =="search_array.main_category"){

            $sub_categories=\App\Models\SubCategory::where('main_category_id',$this->search_array['main_category'])->get();
            $this->emit("product:main_category", [
                'data'=>$sub_categories,

            ]);
        }
        if($data =="search_array.sub_category"){

            $sub_categories=\App\Models\SubCategory::where('parent_id',$this->search_array['sub_category'])->get();
            $this->emit("product:sub_category", [
                'data'=>$sub_categories,

            ]);
        }
    }

}
