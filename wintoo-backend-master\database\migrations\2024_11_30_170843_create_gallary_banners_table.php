<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGallaryBannersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('gallary_banners', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('title', 191)->nullable();
			$table->boolean('is_active')->default(1);
			$table->string('image', 191)->nullable();
			$table->string('type', 191)->nullable();
			$table->text('link')->nullable();
			$table->text('iframe')->nullable();
			$table->string('page_type', 191)->nullable();
			$table->timestamps();
			$table->text('product_or_category_id')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('gallary_banners');
	}

}
