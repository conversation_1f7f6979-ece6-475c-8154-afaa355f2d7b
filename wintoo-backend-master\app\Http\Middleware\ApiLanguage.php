<?php

namespace App\Http\Middleware;


use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;

class ApiLanguage
{
    public function handle(Request $request, Closure $next)
    {


        if ($request->header('X-Client-Language')) {
            App::setLocale($request->header('X-Client-Language'));
        } else { // This is optional as <PERSON><PERSON> will automatically set the fallback language if there is none specified
            App::setLocale('ar');
        }


        $guards = ["customers","store"];
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                Auth::guard($guard)->user()->lang_code = App::getLocale();
                Auth::guard($guard)->user()->save();
            }
        }
        return $next($request);
    }


}
