<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSettingsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('settings', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('website_name', 191)->nullable();
			$table->string('email', 191)->nullable();
			$table->string('address_1', 191)->nullable();
			$table->string('address_2', 191)->nullable();
			$table->string('seo_keywords', 191)->nullable();
			$table->text('seo_description')->nullable();
			$table->string('phone', 191)->nullable();
			$table->string('mobile_1', 191)->nullable();
			$table->string('mobile_2', 191)->nullable();
			$table->string('facebook', 191)->nullable();
			$table->string('instagram', 191)->nullable();
			$table->string('whatsapp', 191)->nullable();
			$table->string('twitter', 191)->nullable();
			$table->string('linked_in', 191)->nullable();
			$table->string('logo', 191)->nullable();
			$table->string('favicon', 191)->nullable();
			$table->timestamps();
			$table->boolean('pay_ios_orders')->default(0);
			$table->boolean('pay_android_orders')->default(1);
			$table->boolean('pay_ios_hotsale')->default(0);
			$table->boolean('pay_android_hotsale')->default(1);
			$table->boolean('pay_android_ads')->default(0);
			$table->boolean('pay_ios_ads')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('settings');
	}

}
