<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlackFridayRequestsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('black_friday_requests', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('store_id')->nullable();
			$table->text('note')->nullable();
			$table->string('price', 191)->nullable();
			$table->dateTime('start_at')->nullable();
			$table->dateTime('end_at')->nullable();
			$table->boolean('status')->default(0);
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('black_friday_requests');
	}

}
