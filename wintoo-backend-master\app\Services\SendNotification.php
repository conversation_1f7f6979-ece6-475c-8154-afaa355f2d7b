<?php

namespace App\Services;


use App\Models\Customer;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;

class SendNotification{


    static function customerSendOrderToStore($order){

        // "customerSendOrderToStore" => طلب جديد رقم %s من قبل %s

        $msg = 'طلب جديد ';
        $msg .='رقم ';
        $msg .=$order->id;
        $msg .=' من قبل ';
        $msg .=optional($order->customer)->name;


        $store = Store::find($order->store_id);
        App::setLocale(optional($store)->lang_code??"ar");
        $msg = sprintf(Lang::get("lang.customerSendOrderToStore"), $order->id, optional($order->customer)->username);


        $data=[
            'title' => $msg,
            'body' => $msg,
            'id'=>$order->id,
            'type'=>'order',
        ];


        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales("lang.customerSendOrderToStore",$order->id, optional($order->customer)->username));

        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'order',
                $data
            ));


    }


    static  function storeChangeOrderStatus($order){

        // "storeChangeOrderStatus" => الطلب رقم  %s الآن %s

        $msg = 'الطلب رقم ';
        $msg .=$order->id;
        $msg .=' الان ';
        $msg .=$order->status_name;
        $customer = Customer::find($order->customer_id);
        App::setLocale($customer->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.storeChangeOrderStatus"), $order->id, $order->status_name);


        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$order->id,
            'type'=>'order',
        ];

        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales("lang.storeChangeOrderStatus", $order->id, $order->status_name_translations));

        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'order',
                $data
            ));


    }


    /*** Start  ***/

    static  function  drawSystemStoreNotification($draw,$customer_id,$store_id){





        $store = Store::find($store_id);
        $customer = Customer::find($customer_id);
        // drawSystemStoreNotification => مبروك تم فوزك في السحب على  %s من قبل %s
        $msg = 'مبروك تم فوزك في السحب على ';
        $msg .=$draw->prize_name;
        $msg .=' من قبل ';
        $msg .=$store->name;

        App::setLocale($customer->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.drawSystemStoreNotification"), $draw->prize_name, $store->name);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$draw->id,
            'type'=>'draw',
        ];


        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales(("lang.drawSystemStoreNotification"), $draw->prize_name, $store->name));

        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'draw',
                $data
            ));
        //drawSystemStoreNotificationFinished => تم انتهاء السحب الخاص بك على جائزة  %s
        $msg = 'تم انتهاء السحب الخاص بك على جائزة  ';
        $msg .=$draw->prize_name;

        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.drawSystemStoreNotificationFinished"), $draw->prize_name);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$draw->id,
            'type'=>'draw',
        ];

        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.drawSystemStoreNotificationFinished"), $draw->prize_name));


        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'draw',
                $data
            ));


    }


    static  function  drawSystemNotification($draw,$customer_id,$store_id){


        $store = Store::find($store_id);
        $customer = Customer::find($customer_id);
        // drawSystemNotification => مبروك تم فوزك في السحب على %s
        $msg = 'مبروك تم فوزك في السحب على ';
        $msg .=$draw->prize_name;

        App::setLocale($customer->lang_code??"ar");


        $msg = sprintf(Lang::get("lang.drawSystemNotification"), $draw->prize_name);

        $data = [
            'title' => $msg,
            'body' => '',
            'id'=>$draw->id,
            'type'=>'draw',
        ];



        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales(("lang.drawSystemNotification"), $draw->prize_name));


        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'draw',
                $data
            ));


        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.drawSystemNotification"), $draw->prize_name);
        $data = [
            'title' => $msg,
            'body' => '',
            'id'=>$draw->id,
            'type'=>'draw',
        ];
        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.drawSystemNotification"), $draw->prize_name));

        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'draw',
                $data
            ));


    }

    /*** End ***/

    static function  qrScanFromCustomerToStoreNotification($qr_scan){

        //qrScanFromCustomerToStoreNotification => قام %s  بسكان qr code الخاص بك
        $store = Store::find($qr_scan->store_id);
        $customer = Customer::find($qr_scan->customer_id);

        $msg = 'قام ';
        $msg .=$customer->username;
        $msg .= " بسكان qr code الخاص بك ";


        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.qrScanFromCustomerToStoreNotification"), $customer->username);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$qr_scan->id,
            'type'=>'qr',
        ];
        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.qrScanFromCustomerToStoreNotification"), $customer->username));


        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'qr',
                $data
            ));

    }
    static function  qrScanAcceptOrRejectFromStoreToCustomerNotification($qr_scan){

        //qrScanAcceptOrRejectFromStoreToCustomerNotificationRejected => قام %s  برفض السكان الخاص بك
        //qrScanAcceptOrRejectFromStoreToCustomerNotificationAccepted => قام %s  بقبول السكان الخاص بك

        $store = Store::find($qr_scan->store_id);
        $customer = Customer::find($qr_scan->customer_id);

        $msg = 'قام ';
        $msg .=$store->name;


        App::setLocale($customer->lang_code??"ar");


        if ($qr_scan->status=="Rejected"){
            $msg .= " برفض السكان الخاص بك";

            $msg = sprintf(Lang::get("lang.qrScanAcceptOrRejectFromStoreToCustomerNotificationRejected"), $store->name);

        }else{
            $msg .= " بقبول السكان الخاص بك";
            $msg = sprintf(Lang::get("lang.qrScanAcceptOrRejectFromStoreToCustomerNotificationAccepted"), $store->name);

        }


        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$qr_scan->id,
            'type'=>'general',
        ];
        fcmNotification($customer,$data);


        if ($qr_scan->status=="Rejected") {

            $msg = json_encode(formatInAllLocales(("lang.qrScanAcceptOrRejectFromStoreToCustomerNotificationRejected"), $store->name));

            \Notification::send($customer,
                new \App\Notifications\GeneralNotification(
                    $msg,
                    'general_notification',
                    $data
                ));

        }else{

            $msg = json_encode(formatInAllLocales(("lang.qrScanAcceptOrRejectFromStoreToCustomerNotificationAccepted"), $store->name));

            \Notification::send($customer,
                new \App\Notifications\GeneralNotification(
                    $msg,
                    'general_notification',
                    $data
                ));


        }


    }

    static function sendCommentOnPost($store, $current_user, $post){

        //sendCommentOnPost =>قام %s  بالتعليق على منشور لك

        $msg = 'قام ';
        $msg .=$current_user->username;
        $msg .=" بالتعليق على منشور لك ";
        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendCommentOnPost"), $current_user->username);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$post->id,
            'type'=>'comment',
        ];


        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendCommentOnPost"), $current_user->username));

        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'comment',
                $data
            ));
    }
   static function sendReplyOnComment($storeOrUser, $user, $post_id){

        //sendReplyOnComment => قام %s  بالرد على تعليقك على منشور له

        $post_owner_name = $storeOrUser->name??$storeOrUser->username ;
        $msg = 'قام ';
        $msg .=$post_owner_name;
        $msg .=" بالرد على تعليقك على منشور له ";

        App::setLocale($user->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendReplyOnComment"), $post_owner_name);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>(int)$post_id,
            'type'=>'comment',
        ];


        fcmNotification($user,$data);
        $msg = json_encode(formatInAllLocales(("lang.sendReplyOnComment"), $post_owner_name));

        \Notification::send($user,
            new \App\Notifications\GeneralNotification(
                $msg,
                'comment',
                $data
            ));
    }

    static  function sendNotificationOnProductRequestFirstMessage($user_id,$product_request_id){
        $customer = Customer::find($user_id);
        //sendNotificationOnProductRequestFirstMessage => يوجد طلبات جديدة  مطابقة لطلبك رقم ".$product_request_id.". اضغط للمراجعة
        App::setLocale($customer->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendNotificationOnProductRequestFirstMessage"), $product_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$product_request_id,
            'type'=>'product_request',
        ];

        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnProductRequestFirstMessage"), $product_request_id));

     //   dd($customer);

        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'product_request',
                $data
            ));
    }

    static  function sendNotificationOnProductRequestFirstMessageStore($user_id,$product_request_id){

        $store = Store::find($user_id);
        if (!$store) return ;



        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendNotificationOnProductRequestFirstMessage"), $product_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$product_request_id,
            'type'=>'product_request',
        ];

        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnProductRequestFirstMessage"), $product_request_id));


        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'product_request',
                $data
            ));

    }

    static function sendNotificationOnRentalRequestFirstMessage($user_id,$product_request_id){
        $customer = Customer::find($user_id);
        //sendNotificationOnProductRequestFirstMessage => يوجد طلبات جديدة  مطابقة لطلبك رقم ".$product_request_id.". اضغط للمراجعة
        App::setLocale($customer->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendNotificationOnRentalRequestFirstMessage"), $product_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$product_request_id,
            'type'=>'rental_request',
        ];

        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnRentalRequestFirstMessage"), $product_request_id));

     //   dd($customer);

        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'rental_request',
                $data
            ));
    }

    static  function sendNotificationOnRentalRequestFirstMessageStore($user_id,$product_request_id){

        $store = Store::find($user_id);
        if (!$store) return ;



        App::setLocale($store->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendNotificationOnRentalRequestFirstMessage"), $product_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$product_request_id,
            'type'=>'rental_request',
        ];

        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnRentalRequestFirstMessage"), $product_request_id));


        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'rental_request',
                $data
            ));

    }


    static function sendNotificationOnJobRequestFirstMessage($user_id,$job_request_id){
        $customer = Customer::find($user_id);
        //sendNotificationOnProductRequestFirstMessage => يوجد طلبات جديدة  مطابقة لطلبك رقم ".$product_request_id.". اضغط للمراجعة
        App::setLocale($customer->lang_code??"ar");

        $msg = sprintf(Lang::get("lang.sendNotificationOnJobRequestFirstMessage"), $job_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$job_request_id,
            'type'=>'job_request',
        ];

        fcmNotification($customer,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnJobRequestFirstMessage"), $job_request_id));

        \Notification::send($customer,
            new \App\Notifications\GeneralNotification(
                $msg,
                'job_request',
                $data
            ));
    }

    static function sendNotificationOnJobRequestFirstMessageStore($user_id,$job_request_id){

        $store = Store::find($user_id);
        if (!$store) return ;




        App::setLocale($store->lang_code??"ar");
        $msg = sprintf(Lang::get("lang.sendNotificationOnJobRequestFirstMessage"), $job_request_id);

        $data=[
            'title' => $msg,
            'body' => '',
            'id'=>$job_request_id,
            'type'=>'job_request',
        ];

        fcmNotification($store,$data);

        $msg = json_encode(formatInAllLocales(("lang.sendNotificationOnJobRequestFirstMessage"), $job_request_id));


        \Notification::send($store,
            new \App\Notifications\GeneralNotification(
                $msg,
                'job_request',
                $data
            ));

    }



  static function sendAddPublishedNotification($current_user, $post){

    //   App::setLocale($current_user->lang_code??"ar");

      $msg = "The Advertisement was added successfully";

       $data=[
           'title' => "The Advertisement was added successfully",
           'body' => '',
           'id'=>$post->id,
           'type'=>'general',
       ];


       fcmNotification($current_user,$data);

    //   $msg = json_encode(formatInAllLocales(("lang.post_published")));

       \Notification::send($current_user,
           new \App\Notifications\GeneralNotification(
               "The Advertisement was added successfully",
               'general',
               $data
           ));
       }



   static function sendPostPublishedNotification($current_user, $post){

       App::setLocale($current_user->lang_code??"ar");

       $msg = sprintf(Lang::get("lang.post_published"));

       $data=[
           'title' => $msg,
           'body' => '',
           'id'=>$post->id,
           'type'=>'post',
       ];


       fcmNotification($current_user,$data);

       $msg = json_encode(formatInAllLocales(("lang.post_published")));

       \Notification::send($current_user,
           new \App\Notifications\GeneralNotification(
               $msg,
               'post',
               $data
           ));
       }

   static function sendPostUpdatedNotification($current_user, $post){

       App::setLocale($current_user->lang_code??"ar");

       $msg = sprintf(Lang::get("lang.post_updated"));

       $data=[
           'title' => $msg,
           'body' => '',
           'id'=>$post->id,
           'type'=>'post',
       ];


       fcmNotification($current_user,$data);

       $msg = json_encode(formatInAllLocales(("lang.post_updated")));

       \Notification::send($current_user,
           new \App\Notifications\GeneralNotification(
               $msg,
               'post',
               $data
           ));
       }


}
?>
