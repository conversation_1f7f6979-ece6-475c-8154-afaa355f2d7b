<?php

namespace App\Http\Resources;

use App\Models\Favorite;
use App\Models\Follower;
use App\Models\ProductRequest;
use App\Models\Reviews;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductRequesSimilarResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


        $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');

        return [
            "id"=>$this->id,
            "store_category_id"=>$this->store_category_id,
            "product_name"=>$this->product_name,

            "store_category"=>new StoreCategoryResource($this->store_category),
            "customer_id"=>$this->customer_id,
            "customer"=>new CustomerResource($this->customer),
            "store"=>new StoreResource($this->store),
            "hash"=>$this->hash,
            "sell_price"=>$this->sell_price,
            "buy_price_from"=>$this->buy_price_from,
            "buy_price_to"=>$this->buy_price_to,
            "type"=>$this->type,
            "type_name"=>$this->type_name,
            "created_at"=>$created_at,
            "attributes"=>AttributeResource::collection($this->attributes),
            "media" => ProductRequestMediaResource::collection($this->media??[]),
            'country'=>new AreaResource($this->country),
            'governorate'=>new AreaResource($this->governorate),
            'city'=>new AreaResource($this->city),
            'region'=>new AreaResource($this->region),
            "full_address"=> $this->full_address,
            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource($this->currency),

        ];

    }
}
