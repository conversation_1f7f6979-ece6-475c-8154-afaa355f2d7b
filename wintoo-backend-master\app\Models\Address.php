<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Address extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;

    protected $appends =['full_address'];

    protected $fillable=[
        'country_id',
        'state',
        'zipcode',
        'addressline1',
        'addressline2',
        'fullname',
        'address',
        'phone',
        'city',
        'is_default',
        'customer_id',
        'name'
    ];



    public function country(){
        return $this->belongsTo(Country::class);

    }

    public function getFullAddressAttribute(){
        return optional($this->country)->name .' | '.$this->state .' | '.$this->city .' | '.$this->email;
    }

}
