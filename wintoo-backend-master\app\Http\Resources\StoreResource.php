<?php

namespace App\Http\Resources;

use App\Models\Currency;
use App\Models\Favorite;
use App\Models\Follower;
use App\Models\Reviews;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log; // Ensure you import the Log facade


class StoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $is_follow = false;

        if (auth('customers')->check()) {
            $follow = Follower::where('store_id', $this->id)
                ->where('customer_id', auth('customers')->id())
                ->first();

            // Log the follow information for Telescope
            Log::info('Customer Follow Data:', ['follow' => $follow]);

            // Use an if statement to set $is_follow
            if ($follow === null) {
                $is_follow = false;  // Set to true if $follow is null
            } else {
                $is_follow = true; // Set to false if $follow is not null
            }

        } else if (auth('store')->check()) {
            $follow = Follower::where('store_id', $this->id) // This is the store being followed
            ->where('customer_id', auth('store')->id()) // This is the store doing the following
            ->first();

            // Log the follow information for Telescope
            Log::info('Store Follow Data:', ['follow' => $follow]);
            Log::info('Store ID:', ['store_id' => $this->id]);
            Log::info('Authenticated Store ID:', ['authenticated_store_id' => auth('store')->id()]);
            // Use an if statement to set $is_follow
            if ($follow === null) {
                $is_follow = false;  // Set to true if $follow is null
            } else {
                $is_follow = true; // Set to false if $follow is not null
            }
        }



        $followers  = Follower::where('store_id',$this->id)->whereHas("customer")
            ->count();



        return [
            'id'=>$this->id,
            'name'=>$this->name,
            'logo'=>$this->logo,
            'logo_url'=>$this->logo_url,
            'logo_large_url'=>$this->logo_large_url,
            'bio'=>$this->bio,
            'mobile'=>$this->mobile,
            'full_mobile'=>$this->full_mobile,
            'email'=>$this->email,
            'phone'=>$this->phone,
            'address'=>$this->address,
            'category_id'=>$this->category_id,
            'category'=>new StoreCategoryResource($this->category),
            'open_time_from'=>$this->open_time_from,
            'open_time_to'=>$this->open_time_to,
            'whatsapp'=>$this->whatsapp,
            'full_whatsapp'=>getFullMobile($this->whatsapp_phone_code,$this->whatsapp),
            'whatsapp_phone_code'=>$this->whatsapp_phone_code,
            'open_days'=>$this->getOpenDays(),
            'status'=>$this->status,
            'country_id'=>$this->country_id,
            'country'=>new GeneralResource($this->country),
            'governorate_id'=>$this->governorate_id,
            'governorate'=>new GeneralResource($this->governorate),
            'city_id'=>$this->city_id,
            'city'=>new GeneralResource($this->city),
            'region_id'=>$this->region_id,
            'region'=>new GeneralResource($this->region),


            'currency_id'=>$this->currency_id,
            'currency'=>new CurrencyResource($this->currency),

            'device_type'=>$this->device_type,
            'fcm_token'=>$this->fcm_token,
            'sms_verify'=>$this->sms_verify,
            'facebook'=>$this->facebook,
            'instagram'=>$this->instagram,
            'open_type'=>$this->open_type,
            'is_follow'=>$is_follow,
            'qr_image'=>$this->qr_image_url,
            'is_open'=>false,
            "is_chat_register"=>$this->is_chat_register,
            "auth_token"=>$this->chat_auth,
            "uid"=>"store_".$this->id,
            'type'=>"".$this->type,
            'type_name'=>"".$this->type_name,
            'code'=>$this->code,
            'phone_code'=>$this->phone_code,
            'followers_count'=>$followers,



        ];
    }

    private function getOpenDays()
    {

        $days = [

            Lang::get('lang.saturday')=>"السبت",
            Lang::get('lang.sunday')=>"الاحد",
            Lang::get('lang.monday')=>"الاثنين",
            Lang::get('lang.tuesday')=>"الثلاثاء",
            Lang::get('lang.wednesday')=>"الاربعاء",
            Lang::get('lang.thursday')=>"الخميس",
            Lang::get('lang.friday')=>"الجمعة"
        ] ;


      $open_days =   str_replace(array_values($days),array_keys($days),$this->open_days);

      $open_days  =   json_decode($open_days) ;
      return $open_days??json_decode('[
{
"id": 1,
"name": "'.Lang::get("lang.saturday").'",
"is_selected": false
},
{
"id": 2,
"name": "'.Lang::get("lang.sunday").'",
"is_selected": false
},
{
"id": 3,
"name": "'.Lang::get("lang.monday").'",
"is_selected": false
},
{
"id": 4,
"name": "'.Lang::get("lang.tuesday").'",
"is_selected": false
},
{
"id": 5,
"name": "'.Lang::get("lang.wednesday").'",
"is_selected": false
},
{
"id": 6,
"name": "'.Lang::get("lang.thursday").'",
"is_selected": false
},
{
"id": 7,
"name": "'.Lang::get("lang.friday").'",
"is_selected": false
}
]');
    }
}
