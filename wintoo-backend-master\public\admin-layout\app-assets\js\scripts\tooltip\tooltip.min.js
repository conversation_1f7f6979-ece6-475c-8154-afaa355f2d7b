!function(t){"use strict";t("#show-tooltip").tooltip({title:"Tooltip Show Event",trigger:"click",placement:"right"}).on("show.bs.tooltip",function(){alert("Show event fired.")}),t("#shown-tooltip").tooltip({title:"Tooltip Shown Event",trigger:"click",placement:"top"}).on("shown.bs.tooltip",function(){alert("Shown event fired.")}),t("#hide-tooltip").tooltip({title:"Tooltip Hide Event",trigger:"click",placement:"bottom"}).on("hide.bs.tooltip",function(){alert("Hide event fired.")}),t("#hidden-tooltip").tooltip({title:"Tooltip Hidden Event",trigger:"click",placement:"left"}).on("hidden.bs.tooltip",function(){alert("Hidden event fired.")}),t("#show-method").on("click",function(){t(this).tooltip("show")}),t("#hide-method").on("mouseenter",function(){t(this).tooltip("show")}),t("#hide-method").on("click",function(){t(this).tooltip("hide")}),t("#toggle-method").on("click",function(){t(this).tooltip("toggle")}),t("#dispose").on("click",function(){t("#dispose-method").tooltip("dispose")}),t(".manual").on("click",function(){t(this).tooltip("show")}),t(".manual").on("mouseout",function(){t(this).tooltip("hide")}),t(".template").on("click",function(){console.log('<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>')})}((window,document,jQuery));