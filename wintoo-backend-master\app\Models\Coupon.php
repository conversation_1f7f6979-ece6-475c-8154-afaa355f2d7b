<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
use \App\Traits\PropertyGetter;
    use HasFactory;
  protected $guarded = [];
  protected $appends = ['coupon_status_name'];
  public  static function  getColumnLang(){
 	 	$columes=[
 	 	'coupon_code'=>[\Lang::get('coupon.coupon_code') ,1,true,false,[]],
 	 	'coupon_value'=>[\Lang::get('coupon.coupon_value') ,1,true,false,[]],
 	 	'coupon_type'=>[\Lang::get('coupon.coupon_type') ,1,true,false,[]],
 	 	'expired_date'=>[\Lang::get('coupon.expired_date') ,1,true,false,[]],

 	 	'coupon_status'=>[\Lang::get('coupon.coupon_status') ,1,true,false,[]],
        'number_of_used'=>[\Lang::get('coupon.number_of_used') ,1,true,false,[]],
 	 	   'actions'=>['الخيارات',6,true,false,['edit','delete']],
 	 	];
 	 	 return $columes;
  }

  public static function getSearchable(){
 	 	$columes=['coupon_code'=>[\Lang::get('coupon.coupon_code')],

 	 	 ]; return $columes;
  }

  public function scopeSearch($query, $data) {
 	 	 if(isset($data["coupon_code"])){
 	 	   $query->where("coupon_code","LIKE","%".$data["coupon_code"]."%");}
 	 	 if(isset($data["coupon_value"])){
 	 	   $query->where("coupon_value","LIKE","%".$data["coupon_value"]."%");}
 	 	 if(isset($data["coupon_type"])){
 	 	   $query->where("coupon_type","LIKE","%".$data["coupon_type"]."%");}
 	 	 if(isset($data["expired_date"])){
 	 	   $query->where("expired_date","LIKE","%".$data["expired_date"]."%");}
 	 	 if(isset($data["used_date"])){
 	 	   $query->where("used_date","LIKE","%".$data["used_date"]."%");}
 	 	 if(isset($data["coupon_status"])){
 	 	   $query->where("coupon_status","LIKE","%".$data["coupon_status"]."%");}
 	 	 if(isset($data["customer_id"])){
 	 	   $query->where("customer_id","LIKE","%".$data["customer_id"]."%");}
 	 	 return $query ;
    }

    public function getCouponStatusNameAttribute(){
        if($this->expired_date >=\Carbon\Carbon::today()){
            return 'فعال';
        }
        return 'غير فعال ';
    }

    public function scopeIsActive($query) {
         $query->whereDate('expired_date','>=',\Carbon\Carbon::today());
        return $query;
  }

}
