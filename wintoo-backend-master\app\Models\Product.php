<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>run<PERSON>pi\LaravelUserActivity\Traits\Loggable;
class Product extends Model
{
use \App\Traits\PropertyGetter;

    protected $guarded=[];
    use HasFactory;
    use Loggable;
    protected $casts=[
        "is_new"=>"boolean",
        "is_available"=>"boolean",

    ];

    protected $appends=['image_url',"identifier_name",'status_name','offer_discount_rate'];
    protected $connection = "mysql";
    protected static function boot()
    {

        parent::boot();
        Product::deleting(function ($model){
                Reviews::where('product_id',$model->id)->delete();
                ProductImage::where('product_id',$model->id)->delete();
                Favorite::where('product_id',$model->id)->delete();
                Variation::where('product_id',$model->id)->delete();
                CartApi::where('product_id',$model->id)->delete();
        });
    }
    public static $searchable=[
        'title'=>['اسم المنتج/البكج '],
        'product_type'=>['نوع المنتج',[
            'type'=>'select',
            'model'=>[['name'=>'منتج من لستور جان','value'=>'store_jan_product'],['name'=>' منتج عادي','value'=>'normal_product']],
            'name'=>'name',
            'value'=>'value'
        ]],
        'main_category'=>['التصنيف الاساسي',[
            'type'=>'multi_select',
            'model'=>'Category',
            'name'=>'name',
            'value'=>'id'
        ],
        ] ,
        'sub_category'=>['التصنيف الفرعي (الثاني )',[
            'type'=>'multi_select',
            'model'=>'SubCategory',
            'name'=>'name',
            'value'=>'id'
        ],
        ] ,
        'sub_sub_category'=>['التصنيف الفرعي (الثالث )',[
            'type'=>'multi_select',
            'model'=>'SubCategory',
            'name'=>'name',
            'value'=>'id'
        ],
        ] ,
        'brand_id'=>['اسم العلامة التجارية ',[
            'type'=>'multi_select',
            'model'=>'Brand',
            'name'=>'name',
            'value'=>'name'
        ],
        ] ,
        'is_available'=>['الحالة',[
            'type'=>'select',
            'model'=>[['name'=>'متوفر','value'=>1],['name'=>'غير متوفر','value'=>0]],
            'name'=>'name',
            'value'=>'value'
        ]],
    ];
    public  static function  getColumnLang(){

        $columes=[
            'image'=>['صورة المنتج',1,true,false,[]],
            'title'=>['اسم المنتج',2,true,true,[]],
            'id'=>['رقم المنتج',2,true,true,[]],
            'is_available'=>['متوفر في المخزون',3,true,false,[]],
            'status'=>['الحالة',3,true,false,[]],
            'actions'=>['الخيارات',5,true,false,['edit','show','delete']],
        ];
        return $columes;

    }

    public function getImageUrlAttribute(){
        if($this->product_images->isNotEmpty()){
            $there_main= ProductImage::where('is_main',true)
                ->where('product_id',$this->id)->orderBy('created_at','desc')
                ->first();
            if($there_main){
                return asset('storage/product/'.$there_main->image);

            }else{
                return asset('storage/product/'.$this->product_images->first()->image);
            }

        }else{
            return \App\Models\Setting::first()?\App\Models\Setting::first()->logo_url:null;
        }
    }

    public function category(){
        return $this->belongsTo(SubCategory::class,   'category_id');
    }

    public function main_category(){
        return $this->belongsTo(Category::class,   'main_category_id');
    }

    public function sub_category(){
        return $this->belongsTo(SubCategory::class,   'sub_category_id');
    }


    public function brand()
    {
        return $this->belongsTo(Brand::class,'brand_id');
    }


    public function currency()
    {
        return $this->belongsTo(Currency::class,'currency_id');

    }

    public function product_images()
    {
        return $this->hasMany(ProductImage::class);
    }


    public function favorite()
    {
        return $this->hasMany(Favorite::class,'product_id');
    }

    public function reviews()
    {
        return $this->hasMany(Reviews::class,'product_id');
    }

    public function getStatusNameAttribute(){
        if($this->product_type==='store_jan_product'){
            $output='منتج من لستور جان ';
            return $output;


        }
        $output='منتج عادي';
        return $output;

    }

    public function variations(){
        return $this->hasMany(Variation::class,'product_id');
    }

    public function getOfferDiscountRateAttribute(){
        $rate = \App\Models\ExchangRate::latest()->first()->rate;
        $new_price = $this->new_price *$rate ;
        $profit_price = $this->profit_price *$rate ;
        $profit_price = getRound($profit_price);
        $new_price = getRound($new_price);
        $number =$this->new_price && $profit_price?((($profit_price - $new_price)*100) /$profit_price):0;
        $number_get = numberFormat($number);
        return $number_get;

    }


    public function scopeSearch($query, $data)
    {


        if(isset($data['title'])){
            $query->where('title', 'LIKE', '%'.$data['title'].'%');
        }


        if(isset($data['sub_sub_category'])){
            $search=$data['sub_sub_category'];
            $query->where('category_id', $search);
//
//            $query->whereHas('category', function($q) use($search){
//                $q->where('name', 'LIKE', '%'.$search.'%');
//            });

        }

        if(isset($data['brand_id'])){
            $search=$data['brand_id'];
            $query->where('brand_id', $search);
//            $query->whereHas('brand', function($q) use($search){
//                $q->where('name', 'LIKE', '%'.$search.'%');
//            });

        }

        if(isset($data['is_available'])){
            $query->where('is_available',  $data['is_available']);
        }


        if(isset($data['product_type'])){
            $query->where('product_type', 'LIKE',$data['product_type']);
        }
        if(isset($data['store_id'])){
            $query->where('store_id',$data['store_id']);
        }

        return $query;
    }

    public function scopeOffer($query){
        return $query->whereNotNull('new_price')
            ->where('type','product');

    }

    public function scopeIsActive($query){
        return $query->where('status',1)
          //  ->variationHasQuantity()
            ->where('store_jan_status','!=','disabled')
            ->where('is_available',1);
    }
    public function scopeVariationHasQuantity($query){
        $query->whereHas('variations', function ($query2){
            $query2->havingRaw('sum(stock_quantity) > 0');
          //  $query2->whereSum('stock_quantity', '>', 0);
        });
    }

    public function scopeNew($query){
        return $query->where('is_new',true);
    }

    public function scopeDiscount($query){
        return $query->whereNotNull('new_price')->where('type','package');
    }

    public function scopeGetByCategory($query,$data){

        $sub=SubCategory::where('id',$data)->whereNull('parent_id')->pluck('id')->toArray();
        if(!empty($sub)){
            $sub_sub=SubCategory::whereIn('parent_id',$sub)->pluck('id')->toArray();
            return $query->whereIn('category_id',$sub_sub);
        }else{
            return $query->where('category_id',$data);
        }

    }

    public function scopeIsFavorite($query){
        $current_user = auth('customers')->user();

        return
        $query->whereHas('favorite', function($q) use($current_user){
            $q->where('customer_id',$current_user->id );
        });
    }

    public function scopeIsAllActive($query){


        $query->has("store")
            ->where('status',1)
           /* ->where('is_available',1)*/;

            return $query;

        if($this->store_jan_product ==='store_jan_product'){
            $query->whereHas('category', function($q) {
                $q->where('status',1);
            })->whereHas('brand', function($q){
                $q->where('status',1);
            })->where('store_jan_status','!=','disabled')  ->where('status',1)    ->where('is_available',1);
            // ->variationHasQuantity()
        }
        else{
            $query->whereHas('category', function($q) {
                $q->where('status',1);
            })
              //  ->variationHasQuantity()
                ->where('status',1)
                ->where('is_available',1);
        }

    }


    public function scopeOnProductTitle($query, $search_term)
    {

        $escaped_term = str_replace('+', '\\+', $search_term);
        $escaped_term = "+".str_replace(' ', '*+', $escaped_term)."*";
        //te*
//        SELECT * FROM documents WHERE title LIKE '%' + SOUNDEX('عنوان المقال') + '%';

        return $query->where("title","LIKE","%".$search_term."%")
        //  ->orWhereRaw("MATCH(title) AGAINST(? IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION)", [$escaped_term])
        ->orWhereHas("category",function($query) use($search_term ,$escaped_term ){
                $query->where("name","LIKE","%".$search_term."%");
                // ->orWhereRaw("MATCH(name) AGAINST(? IN BOOLEAN MODE)", [$escaped_term]);
            });

    }


    public function getFileImageAttribute(){
        if($this->product_images->isNotEmpty()){
            $there_featured = ProductImage::where('is_main',true)->where('product_id',$this->id)->orderBy('created_at','desc')->first();
            if($there_featured){
                $fileName = $there_featured->image;


                return  $fileName;

            }else{
                $fileName = $this->product_images->first()->image;

                return  $fileName;
            }

        }else{
            $fileName = 'storage/avatars/'.\App\Models\Setting::first()->logo;

            return  $fileName;
        }
    }

    public function setStatus()
    {
        $this->status = !$this->status;
    }


    public function getIdentifierNameAttribute(){

        if ($this->identifier =="المقاس"){
            return \Lang::get("lang.measure");
        }
        if ($this->identifier =="الاصدار"){
            return \Lang::get("lang.version");
        }
        if ($this->identifier =="الحجم"){
            return \Lang::get("lang.size");
        }
        if ($this->identifier =="المساحة"){
            return \Lang::get("lang.space");
        }
        if ($this->identifier =="لون فقط"){
            return \Lang::get("lang.color");
        }
        return $this->identifier??\Lang::get("lang.measure");
    }

    public function store(){
        return $this->belongsTo(Store::class,'store_id');
    }

    public function offer(){
        return $this->belongsTo(Offer::class,'offer_id');
    }

    public function blackFriday(){

        return $this->hasMany(BlackFridayRequest::class,"store_id" ,"store_id")->IsActiveRequest();

    }

    public function hasOffer(){
        return $this->offer_id&& ($this->new_price?true:false) ;
    }
}
