<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class MainColorsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $jsonFilePath = database_path('seeders/data/maincolors.json');

        // Check if the file exists
        if (!File::exists($jsonFilePath)) {
            $this->command->error("JSON file not found: {$jsonFilePath}");
            return;
        }

        // Read the JSON file
        $jsonData = File::get($jsonFilePath);

        // Decode the JSON data into an array
        $rows = json_decode($jsonData, true);

        // Check for JSON errors
        if ($rows === null) {
            $this->command->error("Error decoding JSON file: {$jsonFilePath}");
            return;
        }

        // Insert the data into the database
        DB::table('maincolors')->insert($rows);

        $this->command->info('maincolors table seeded successfully!');
    }
}
