$.contextMenu({selector:"#basic-context-menu",callback:function(e,o){var n="clicked "+e;window.console&&toastr.success(n)},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#left-click-context-menu",trigger:"left",callback:function(e,o){var n="clicked "+e;window.console&&toastr.success(n)},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#hover-context-menu",trigger:"hover",autoHide:!0,callback:function(e,o){var n="clicked "+e;window.console&&toastr.success(n)},items:{"Option 1":{name:"Option 1"},"Option 2":{name:"Option 2"}}}),$.contextMenu({selector:"#submenu-context-menu",callback:function(e,o){var n="clicked "+e;window.console&&toastr.success(n)},items:{"Option 1":{name:"Option 1"},name:{name:"Option 2"},fold1:{name:"Sub Group",items:{"Foo Bar":{name:"Foo bar"},fold1a:{name:"Other group",items:{Echo:{name:"echo"},Foxtrot:{name:"foxtrot"},Golf:{name:"golf"}}}}}}});