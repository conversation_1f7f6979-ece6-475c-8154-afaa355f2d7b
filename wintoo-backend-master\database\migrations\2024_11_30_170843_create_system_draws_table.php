<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemDrawsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('system_draws', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('title', 191)->nullable()->comment('title');
			$table->string('prize_name', 191)->nullable()->comment('prize_name');
			$table->dateTime('date')->nullable()->comment('date');
			$table->string('winer_id', 191)->nullable()->comment('winer_id');
			$table->string('winer_type', 191)->nullable()->comment('winer_type');
			$table->string('status')->nullable()->comment('status');
			$table->timestamps();
			$table->integer('sponsor_id')->nullable();
			$table->integer('store_id')->nullable();
			$table->integer('winner_store_id')->nullable();
			$table->integer('winner_customer_id')->nullable();
			$table->boolean('is_end')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('system_draws');
	}

}
