<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
class GallaryBanner extends Component
{

    use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $title = "";
    public $columes;
    public $page_length = 10;
    public $search ;
    public $model_title="";
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $listeners = ['gallary-banner-livewire:conformDelete' => 'conformDelete'];
    public function mount()
    {
        $this->columes =\App\Models\GallaryBanner::getColumnLang();
        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);
    }
    public function render()
    {
        $data = \App\Models\GallaryBanner::query();

        if($this->search){
            $this->resetPage();
            $search = $this->search;
            $data->where('title','LIKE','%'.$search.'%');
        }

        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);


        return    view('dashboard.gallary-banner.index', ['data'=>$data] )->extends('dashboard_layout.main');


    }
    public function edit($id){
        return redirect()->route('dashboard.gallary-banner.edit',$id);
    }
    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'gallary-banner-livewire:conformDelete',['id'=>$id]);
    }


    public function conformDelete($id){

        \App\Models\GallaryBanner::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    }


    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }
}
