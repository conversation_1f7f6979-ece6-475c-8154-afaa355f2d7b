<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\Currency;
use App\Models\Favorite;
use App\Models\Product;
use App\Models\Reviews;
use App\Models\Size;
use App\Models\Variation;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductCartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // dump($this);
        $product_reviews_user_count=$this->reviews?$this->reviews->count('customer_id'):0;

        $product_reviews=$this->reviews?$this->reviews->avg('rating'):0;

        if(auth('customers')->check()){
            $favorite  = Favorite::where('product_id',$this->id)
                ->where('customer_id',auth('customers')->id())
                ->first();

            $is_favorite =$favorite?true:false;
            $rate = Reviews::where('status',1)->where('customer_id',auth('customers')->id())->where('product_id',$this->id)->first();
        }else{
            $is_favorite = false;
            $rate = false;
        }
        $reviews = Reviews::where('status',1)->where('customer_id',auth('customers')->id())->where('product_id',$this->id)->orderBy('created_at','desc')->limit(12)->get();

        $currentURL = \URL::current();
        $url  = explode('/',$currentURL);
        $current_url_name = end($url);

        $image_url = 'https://wintoo.me/image/300/200/'.$this['file_image'];
        $this->currency =Currency::find($this->currency_id);

        return [
            'id'=>$this->id,
            'title'=>$this->title,
            "price"=> getRound(number_format($this->price, 2)),
            "is_available"=> $this->stock_quantity >0?1:0,
            "description"=>  strip_tags($this->description),
            "category"=> new CategoryResource($this->category),
            "brand"=> $this->brand,
            "is_new"=> $this->is_new,
            "created_at"=>$this->created_at,
            "updated_at"=> $this->created_at,
            //  "type"=>$this->type,
           // 'is_offer'=>$this->new_price?true:false,
            'is_offer'=>$this->offer_id&& ($this->new_price?true:false),

            "new_price"=>getRound(number_format($this->new_price, 2)) ,

            "currency_id"=>$this->currency_id,
            "currency"=>new CurrencyResource(Currency::find($this->currency_id)),

            "charged_price"=> getRound(getPriceForUser($this->price,$this->currency)),
            "charged_new_price"=>getRound(getPriceForUser($this->new_price,$this->currency)),
            'charged_currency'=> new CurrencyResource(getCurrentUserCurrency()),

            'offer_discount_rate'=> $this->offer_discount_rate,
            "image_url"=> $image_url,
            // "status_name"=>$this->status_name ,
            "product_images"=>$this->product_images?$this->product_images()->where('is_main',false)->get():[],
            "is_favorite"=>$is_favorite,
            "rated"=>$rate?true:false,
            'reviews_user_count'=>$product_reviews_user_count,
            'reviews_rate'=> $product_reviews?round($product_reviews,2):0,
            'reviews'=>ReviewResource::collection($reviews),

        ];
    }
}
