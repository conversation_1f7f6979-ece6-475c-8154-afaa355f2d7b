<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAddressesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('addresses', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->integer('city_id')->nullable();
			$table->integer('regoin_id')->nullable();
			$table->integer('governorate_id')->nullable();
			$table->integer('customer_id')->nullable();
			$table->text('address')->nullable();
			$table->boolean('is_default')->default(0);
			$table->timestamps();
			$table->string('name')->nullable();
			$table->integer('country_id')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('addresses');
	}

}
