<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Governorate;
class GovernorateLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme ='bootstrap';
        public  $search_array=[];
        protected $listeners = ['Governorate-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'governorate';
        public function mount()
            {

                $searchable = Governorate::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Governorate::getColumnLang();
                $this->searchable =Governorate::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);

            }


        public function render()
           {
            if(in_array('governorate_show',$this->actions_permission()) ){
                $data =Governorate::search($this->search_array);
                $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
                return view('dashboard/governorate/index',[ 'data'=>$data])->extends('dashboard_layout.main');
            }else{
                return view('dashboard.not-authorized')->extends('dashboard_layout.main');
            }


           }

        public function search(){
            $this->resetPage();
        }

        public function resetSearch(){
            $this->search_array=[];
         }

         public function edit($id){
                 return redirect()->route('dashboard.governorate.edit',$id);
             }

         public function delete($id){

                 $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Governorate-livewire:conformDelete',['id'=>$id]);
         }

         public function conformDelete($id){
            $governorate = Governorate::find($id['id']);
            if(in_array('governorate_delete',$this->actions_permission())){
                if($governorate->cites->isEmpty()){

                    Governorate::find($id['id'])->delete();

                    $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
                }else{
                    $this->showModal('لا يمكن الحذف','لا يمكن حذف المحافظة بسبب وجود مدن مرتبطة بها','error');
                }
            }else{
                return view('dashboard.not-authorized')->extends('dashboard_layout.main');
            }

         }


    public function storeLangData($currantLang)
    {

        $dir      = base_path() . '/resources/lang';


        $langFolder = $dir . "/" . $currantLang;


        foreach($this->arrMessage as $fileName => $fileData)
        {
            $content = "<?php return [";
            $content .= $this->buildArray($fileData);
            $content .= "];";
            file_put_contents($langFolder . "/" . $fileName . '.php', $content);
        }
        $this->showTranslationModel('translation', 'hide');


    }

}

