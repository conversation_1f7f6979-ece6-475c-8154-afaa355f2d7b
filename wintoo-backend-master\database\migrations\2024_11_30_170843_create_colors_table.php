<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateColorsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('colors', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('name', 191)->nullable()->comment('name');
			$table->string('color_ar', 191)->nullable()->comment('color_ar');
			$table->string('code', 191)->nullable()->comment('code');
			$table->timestamps();
			$table->string('main_color_id', 191)->nullable()->comment('main_color_id');
			$table->string('human_name', 191)->nullable()->comment('human_name');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('colors');
	}

}
